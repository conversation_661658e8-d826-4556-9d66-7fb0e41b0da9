<?php

namespace Database\Seeders;

use App\Models\Role;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء الأدوار الأساسية
        $roles = [
            [
                'name' => 'admin',
                'description' => 'مدير النظام - لديه كافة الصلاحيات',
            ],
            [
                'name' => 'manager',
                'description' => 'مدير المطعم - لديه صلاحيات إدارة المطعم',
            ],
            [
                'name' => 'cashier',
                'description' => 'كاشير - لديه صلاحيات عرض وإنشاء الطلبات والفواتير وواجهة الكاشير (بدون تعديل أو حذف الفواتير)',
            ],
            [
                'name' => 'kitchen',
                'description' => 'مطبخ - لديه صلاحيات إدارة الطلبات في المطبخ',
            ],
        ];

        foreach ($roles as $role) {
            // إنشاء الدور فقط إذا لم يكن موجوداً
            Role::firstOrCreate(
                ['name' => $role['name']], // البحث بالاسم
                $role // البيانات للإنشاء
            );
        }
    }
}
