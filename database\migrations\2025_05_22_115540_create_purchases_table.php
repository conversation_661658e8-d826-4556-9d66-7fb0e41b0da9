<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchases', function (Blueprint $table) {
            $table->id();
            $table->string('purchase_number')->unique(); // رقم المشترى
            $table->string('supplier_name'); // اسم المورد
            $table->string('supplier_phone')->nullable(); // هاتف المورد
            $table->text('supplier_address')->nullable(); // عنوان المورد
            $table->date('purchase_date'); // تاريخ الشراء
            $table->decimal('total_amount', 10, 2); // إجمالي المبلغ
            $table->decimal('paid_amount', 10, 2)->default(0); // المبلغ المدفوع
            $table->decimal('remaining_amount', 10, 2)->default(0); // المبلغ المتبقي
            $table->string('payment_method')->default('cash'); // طريقة الدفع
            $table->string('payment_status')->default('pending'); // حالة الدفع (مدفوع، معلق، جزئي)
            $table->string('delivery_status')->default('pending'); // حالة التسليم (تم، معلق، جزئي)
            $table->date('delivery_date')->nullable(); // تاريخ التسليم
            $table->string('invoice_number')->nullable(); // رقم فاتورة المورد
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // المستخدم الذي أضاف المشترى
            $table->text('notes')->nullable(); // ملاحظات
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchases');
    }
};
