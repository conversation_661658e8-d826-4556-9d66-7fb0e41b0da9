<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Backup extends Model
{
    use HasFactory;

    protected $fillable = [
        'filename',
        'type',
        'status',
        'file_size',
        'description',
        'metadata',
        'created_by',
        'started_at',
        'completed_at',
        'error_message',
        'is_scheduled',
        'is_encrypted',
    ];

    protected $casts = [
        'metadata' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'is_scheduled' => 'boolean',
        'is_encrypted' => 'boolean',
        'sent_via_email' => 'boolean',
        'uploaded_to_cloud' => 'boolean',
    ];

    /**
     * العلاقة مع المستخدم الذي أنشأ النسخة الاحتياطية
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * التحقق من وجود الملف
     */
    public function fileExists()
    {
        $filepath = storage_path('app/backups/' . $this->filename);
        return file_exists($filepath);
    }

    /**
     * الحصول على مسار الملف الكامل
     */
    public function getFullPath()
    {
        return storage_path('app/backups/' . $this->filename);
    }

    /**
     * الحصول على حجم الملف المنسق
     */
    public function getFormattedSizeAttribute()
    {
        if (!$this->file_size) {
            return 'غير محدد';
        }

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * الحصول على مدة النسخ الاحتياطي
     */
    public function getDurationAttribute()
    {
        if (!$this->started_at || !$this->completed_at) {
            return null;
        }

        return $this->started_at->diffForHumans($this->completed_at, true);
    }

    /**
     * الحصول على نوع النسخة الاحتياطية مترجم
     */
    public function getTypeNameAttribute()
    {
        $types = [
            'full' => 'نسخة كاملة',
            'database' => 'قاعدة البيانات',
            'files' => 'الملفات',
        ];

        return $types[$this->type] ?? $this->type;
    }

    /**
     * الحصول على حالة النسخة الاحتياطية مترجمة
     */
    public function getStatusNameAttribute()
    {
        $statuses = [
            'pending' => 'في الانتظار',
            'running' => 'قيد التنفيذ',
            'completed' => 'مكتملة',
            'failed' => 'فشلت',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * الحصول على لون الحالة
     */
    public function getStatusColorAttribute()
    {
        $colors = [
            'pending' => 'warning',
            'running' => 'info',
            'completed' => 'success',
            'failed' => 'danger',
        ];

        return $colors[$this->status] ?? 'secondary';
    }

    /**
     * تحديث حجم الملف
     */
    public function updateFileSize()
    {
        if ($this->fileExists()) {
            $this->file_size = filesize($this->getFullPath());
            $this->save();
        }
    }

    /**
     * حذف الملف والسجل
     */
    public function deleteFile()
    {
        if ($this->fileExists()) {
            Storage::disk('local')->delete('backups/' . $this->filename);
        }

        $this->delete();
    }

    /**
     * النسخ الاحتياطية الحديثة
     */
    public static function recent($limit = 10)
    {
        return static::orderBy('created_at', 'desc')->limit($limit)->get();
    }

    /**
     * النسخ الاحتياطية الناجحة
     */
    public static function successful()
    {
        return static::where('status', 'completed');
    }

    /**
     * النسخ الاحتياطية الفاشلة
     */
    public static function failed()
    {
        return static::where('status', 'failed');
    }

    /**
     * إحصائيات النسخ الاحتياطي
     */
    public static function getStatistics()
    {
        return [
            'total' => static::count(),
            'successful' => static::successful()->count(),
            'failed' => static::failed()->count(),
            'total_size' => static::successful()->sum('file_size'),
            'last_backup' => static::successful()->latest()->first(),
        ];
    }
}
