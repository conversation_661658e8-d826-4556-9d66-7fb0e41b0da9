<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class LocaleMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // الحصول على اللغة من المصادر المختلفة
        $locale = $this->getLocale($request);

        // التحقق من صحة اللغة
        if ($this->isValidLocale($locale)) {
            // تطبيق اللغة
            App::setLocale($locale);

            // حفظ اللغة في الجلسة
            Session::put('locale', $locale);

            // إضافة معلومات اللغة للطلب
            $request->merge([
                'current_locale' => $locale,
                'locale_info' => config('app.available_locales')[$locale] ?? null
            ]);
        }

        return $next($request);
    }

    /**
     * الحصول على اللغة من المصادر المختلفة
     */
    private function getLocale(Request $request): string
    {
        // 1. من URL parameter
        if ($request->has('lang')) {
            return $request->get('lang');
        }

        // 2. من route parameter
        if ($request->route('locale')) {
            return $request->route('locale');
        }

        // 3. من الجلسة
        if (Session::has('locale')) {
            return Session::get('locale');
        }

        // 4. من المستخدم المسجل دخوله
        if (auth()->check() && auth()->user()->locale) {
            return auth()->user()->locale;
        }

        // 5. من header Accept-Language
        $acceptLanguage = $request->header('Accept-Language');
        if ($acceptLanguage) {
            $preferredLanguage = substr($acceptLanguage, 0, 2);
            if ($this->isValidLocale($preferredLanguage)) {
                return $preferredLanguage;
            }
        }

        // 6. اللغة الافتراضية
        return config('app.locale', 'ar');
    }

    /**
     * التحقق من صحة اللغة
     */
    private function isValidLocale(string $locale): bool
    {
        return array_key_exists($locale, config('app.available_locales', []));
    }
}
