<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use ZipArchive;

class BackupJob implements ShouldQueue
{
    use Queueable;

    public $timeout = 3600; // 1 hour timeout
    public $tries = 3;

    protected $backupType;
    protected $userId;
    protected $jobId;
    protected $backupId;

    /**
     * Create a new job instance.
     */
    public function __construct($backupType, $sendEmail = false, $uploadToDrive = false, $userId = null, $backupId = null)
    {
        $this->backupType = $backupType;
        $this->userId = $userId;
        $this->jobId = uniqid('backup_');
        $this->backupId = $backupId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $backup = null;

        try {
            // الحصول على سجل النسخة الاحتياطية إذا كان موجود
            if ($this->backupId) {
                $backup = \App\Models\Backup::find($this->backupId);
                if ($backup) {
                    $backup->update([
                        'status' => 'running',
                        'started_at' => now()
                    ]);
                }
            }

            Log::info("بدء عملية النسخ الاحتياطي في الخلفية", [
                'job_id' => $this->jobId,
                'backup_id' => $this->backupId,
                'type' => $this->backupType,
                'user_id' => $this->userId
            ]);

            // إنشاء النسخة الاحتياطية
            $backupPath = $this->performBackup();

            // تحديث معلومات الملف
            if ($backup) {
                $backup->update([
                    'status' => 'completed',
                    'completed_at' => now(),
                    'file_size' => file_exists($backupPath) ? filesize($backupPath) : null,
                    'filename' => basename($backupPath)
                ]);
            }

            Log::info("تم إنشاء النسخة الاحتياطية بنجاح", [
                'job_id' => $this->jobId,
                'backup_id' => $this->backupId,
                'path' => $backupPath
            ]);

            Log::info("تمت عملية النسخ الاحتياطي بنجاح", ['job_id' => $this->jobId]);

        } catch (\Exception $e) {
            // تحديث حالة النسخة الاحتياطية في حالة الفشل
            if ($backup) {
                $backup->update([
                    'status' => 'failed',
                    'completed_at' => now(),
                    'error_message' => $e->getMessage()
                ]);
            }

            Log::error("فشل في عملية النسخ الاحتياطي", [
                'job_id' => $this->jobId,
                'backup_id' => $this->backupId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * تنفيذ النسخ الاحتياطي
     */
    private function performBackup()
    {
        $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
        $backupDir = storage_path('app/backups');

        if (!File::exists($backupDir)) {
            File::makeDirectory($backupDir, 0755, true);
        }

        switch ($this->backupType) {
            case 'database':
                // استخدام الطريقة المبسطة مباشرة لتجنب مشاكل mysqldump
                return $this->backupDatabaseSimple($timestamp);
            case 'database_simple':
                return $this->backupDatabaseSimple($timestamp);
            case 'files':
                return $this->backupFiles($timestamp);
            case 'full':
            default:
                return $this->backupFull($timestamp);
        }
    }

    /**
     * نسخ احتياطي لقاعدة البيانات
     */
    private function backupDatabase($timestamp)
    {
        $filename = "database_backup_{$timestamp}.sql";
        $filepath = storage_path("app/backups/{$filename}");

        $dbHost = config('database.connections.mysql.host');
        $dbName = config('database.connections.mysql.database');
        $dbUser = config('database.connections.mysql.username');
        $dbPass = config('database.connections.mysql.password');
        $dbPort = config('database.connections.mysql.port', 3306);

        $options = [
            '--single-transaction',
            '--routines',
            '--triggers',
            '--quick',
            '--lock-tables=false',
            '--add-drop-table',
            '--disable-keys',
            '--extended-insert',
            '--compress',
        ];

        $optionsStr = implode(' ', $options);

        if (!empty($dbPass)) {
            $command = "mysqldump --host={$dbHost} --port={$dbPort} --user={$dbUser} --password=\"{$dbPass}\" {$optionsStr} {$dbName} > \"{$filepath}\"";
        } else {
            $command = "mysqldump --host={$dbHost} --port={$dbPort} --user={$dbUser} {$optionsStr} {$dbName} > \"{$filepath}\"";
        }

        exec($command, $output, $returnVar);

        if ($returnVar !== 0) {
            throw new \Exception('فشل في تصدير قاعدة البيانات');
        }

        if (!File::exists($filepath) || File::size($filepath) === 0) {
            throw new \Exception('فشل في إنشاء ملف النسخة الاحتياطية أو الملف فارغ');
        }

        return $filepath;
    }

    /**
     * نسخ احتياطي مبسط لقاعدة البيانات
     */
    private function backupDatabaseSimple($timestamp)
    {
        $filename = "database_backup_simple_{$timestamp}.sql";
        $filepath = storage_path("app/backups/{$filename}");

        $handle = fopen($filepath, 'w');

        if (!$handle) {
            throw new \Exception('فشل في إنشاء ملف النسخة الاحتياطية');
        }

        try {
            fwrite($handle, "-- نسخة احتياطية لقاعدة البيانات\n");
            fwrite($handle, "-- تاريخ الإنشاء: " . Carbon::now()->format('Y-m-d H:i:s') . "\n\n");

            // التحقق من نوع قاعدة البيانات
            $dbDriver = config('database.default');
            $connection = config("database.connections.{$dbDriver}");

            if ($dbDriver === 'sqlite') {
                // SQLite
                $tables = DB::select("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
                $tableKey = 'name';
            } else {
                // MySQL
                fwrite($handle, "SET FOREIGN_KEY_CHECKS=0;\n");
                fwrite($handle, "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n\n");

                $tables = DB::select('SHOW TABLES');
                $dbName = $connection['database'];
                $tableKey = "Tables_in_{$dbName}";
            }

            foreach ($tables as $table) {
                $tableName = $table->$tableKey;

                if (in_array($tableName, ['migrations', 'failed_jobs', 'password_resets'])) {
                    continue;
                }

                // إنشاء بنية الجدول
                if ($dbDriver === 'sqlite') {
                    // SQLite
                    $createTable = DB::select("SELECT sql FROM sqlite_master WHERE type='table' AND name='{$tableName}'")[0];
                    fwrite($handle, "DROP TABLE IF EXISTS `{$tableName}`;\n");
                    fwrite($handle, $createTable->sql . ";\n\n");
                } else {
                    // MySQL
                    $createTable = DB::select("SHOW CREATE TABLE `{$tableName}`")[0];
                    fwrite($handle, "DROP TABLE IF EXISTS `{$tableName}`;\n");
                    fwrite($handle, $createTable->{'Create Table'} . ";\n\n");
                }

                $rows = DB::table($tableName)->get();
                if ($rows->count() > 0) {
                    foreach ($rows as $row) {
                        $values = [];
                        foreach ((array)$row as $value) {
                            if (is_null($value)) {
                                $values[] = 'NULL';
                            } else {
                                $values[] = "'" . addslashes($value) . "'";
                            }
                        }

                        $columns = implode('`, `', array_keys((array)$row));
                        $valuesStr = implode(', ', $values);

                        fwrite($handle, "INSERT INTO `{$tableName}` (`{$columns}`) VALUES ({$valuesStr});\n");
                    }
                    fwrite($handle, "\n");
                }
            }

            // إنهاء الملف حسب نوع قاعدة البيانات
            if ($dbDriver === 'mysql') {
                fwrite($handle, "SET FOREIGN_KEY_CHECKS=1;\n");
            }

            fclose($handle);

            return $filepath;

        } catch (\Exception $e) {
            fclose($handle);
            if (File::exists($filepath)) {
                File::delete($filepath);
            }
            throw $e;
        }
    }

    /**
     * نسخ احتياطي للملفات
     */
    private function backupFiles($timestamp)
    {
        $filename = "files_backup_{$timestamp}.zip";
        $filepath = storage_path("app/backups/{$filename}");

        $zip = new ZipArchive();
        if ($zip->open($filepath, ZipArchive::CREATE) !== TRUE) {
            throw new \Exception('فشل في إنشاء ملف ZIP');
        }

        $this->addDirectoryToZip($zip, base_path(), '', [
            'node_modules', 'vendor', '.git', 'storage/logs',
            'storage/framework/cache', 'storage/framework/sessions',
            'storage/framework/views', 'storage/app/backups'
        ]);

        $zip->close();
        return $filepath;
    }

    /**
     * نسخ احتياطي كامل
     */
    private function backupFull($timestamp)
    {
        $filename = "full_backup_{$timestamp}.zip";
        $filepath = storage_path("app/backups/{$filename}");

        $dbBackup = $this->backupDatabaseSimple($timestamp);

        $zip = new ZipArchive();
        if ($zip->open($filepath, ZipArchive::CREATE) !== TRUE) {
            throw new \Exception('فشل في إنشاء ملف ZIP');
        }

        $zip->addFile($dbBackup, 'database_backup.sql');

        $this->addDirectoryToZip($zip, base_path(), '', [
            'node_modules', 'vendor', '.git', 'storage/logs',
            'storage/framework/cache', 'storage/framework/sessions',
            'storage/framework/views', 'storage/app/backups'
        ]);

        $zip->close();
        File::delete($dbBackup);

        return $filepath;
    }

    /**
     * إضافة مجلد إلى ZIP
     */
    private function addDirectoryToZip($zip, $dir, $zipDir = '', $excludeDirs = [])
    {
        if (is_dir($dir)) {
            $files = scandir($dir);
            foreach ($files as $file) {
                if ($file != '.' && $file != '..') {
                    $filePath = $dir . '/' . $file;
                    $zipPath = $zipDir . $file;

                    $shouldExclude = false;
                    foreach ($excludeDirs as $excludeDir) {
                        if (strpos($zipPath, $excludeDir) === 0) {
                            $shouldExclude = true;
                            break;
                        }
                    }

                    if ($shouldExclude) continue;

                    if (is_dir($filePath)) {
                        $zip->addEmptyDir($zipPath);
                        $this->addDirectoryToZip($zip, $filePath, $zipPath . '/', $excludeDirs);
                    } else {
                        $zip->addFile($filePath, $zipPath);
                    }
                }
            }
        }
    }



    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception)
    {
        // تحديث حالة النسخة الاحتياطية في حالة الفشل
        if ($this->backupId) {
            $backup = \App\Models\Backup::find($this->backupId);
            if ($backup) {
                $backup->update([
                    'status' => 'failed',
                    'completed_at' => now(),
                    'error_message' => $exception->getMessage()
                ]);
            }
        }

        Log::error("فشل في job النسخ الاحتياطي", [
            'job_id' => $this->jobId,
            'backup_id' => $this->backupId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
