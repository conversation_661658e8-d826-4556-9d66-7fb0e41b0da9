<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة #{{ $invoice->invoice_number }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            font-size: 14px;
        }
        .invoice-container {
            max-width: 800px;
            margin: 20px auto;
            background-color: #fff;
            padding: 30px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .invoice-header {
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        .invoice-title {
            font-size: 24px;
            font-weight: bold;
            color: #0d6efd;
        }
        .invoice-details {
            margin-bottom: 20px;
        }
        .table th {
            background-color: #f8f9fa;
        }
        .total-section {
            border-top: 2px solid #dee2e6;
            padding-top: 20px;
            margin-top: 20px;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #6c757d;
        }
        @media print {
            body {
                background-color: #fff;
            }
            .invoice-container {
                box-shadow: none;
                margin: 0;
                padding: 15px;
                max-width: 100%;
            }
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <div class="text-end mb-4 no-print">
            <button class="btn btn-primary" onclick="window.print()">
                <i class="fas fa-print me-1"></i> طباعة
            </button>
            <a href="{{ route('invoices.show', $invoice->id) }}" class="btn btn-secondary">
                العودة
            </a>
        </div>

        <div class="invoice-header">
            <div class="row">
                <div class="col-md-6">
                    <div class="invoice-title">فاتورة ضريبية مبسطة</div>
                    <div>رقم الفاتورة: {{ $invoice->invoice_number }}</div>
                    <div>التاريخ: {{ $invoice->created_at->format('Y-m-d H:i') }}</div>
                </div>
                <div class="col-md-6 text-end">
                    @php
                        $restaurantInfo = \App\Models\Setting::getRestaurantInfo();
                    @endphp

                    @if(\App\Models\Setting::get('print_logo', true) && $restaurantInfo['logo'])
                        <div class="mb-3 text-center">
                            <img src="{{ asset('storage/' . $restaurantInfo['logo']) }}" alt="شعار المطعم" style="max-height: 35px; max-width: 150px;">
                        </div>
                    @endif

                    <h3>{{ $restaurantInfo['name'] }}</h3>
                    <div>{{ $restaurantInfo['address'] }}</div>
                    <div>هاتف: {{ $restaurantInfo['phone'] }}</div>
                    <div>البريد الإلكتروني: {{ $restaurantInfo['email'] }}</div>
                    @if($restaurantInfo['tax_number'])
                    <div>الرقم الضريبي: {{ $restaurantInfo['tax_number'] }}</div>
                    @endif
                </div>
            </div>
        </div>

        <div class="invoice-details">
            <div class="row">
                <div class="col-md-6">
                    <h5>معلومات الطلب:</h5>
                    <div>رقم الطلب: {{ $invoice->order->id ?? 'غير محدد' }}</div>
                    <div>
                        @if($invoice->order && $invoice->order->table)
                            الطاولة: {{ $invoice->order->table->name }}
                        @elseif($invoice->order && $invoice->order->order_type == 'takeaway')
                            نوع الطلب: طلب خارجي
                        @else
                            الطاولة: غير محدد
                        @endif
                    </div>
                    @if($invoice->order && $invoice->order->order_type == 'takeaway' && $invoice->order->car_number)
                        <div>رقم السيارة: {{ $invoice->order->car_number }}</div>
                    @endif
                    @if($invoice->order && $invoice->order->order_type == 'takeaway' && $invoice->order->customer_phone)
                        <div>رقم الهاتف: {{ $invoice->order->customer_phone }}</div>
                    @endif
                    <div>النادل: {{ $invoice->order->user->name ?? 'غير محدد' }}</div>
                </div>
                <div class="col-md-6 text-end">
                    <h5>معلومات الدفع:</h5>
                    <div>
                        طريقة الدفع:
                        @if($invoice->payment_method == 'cash')
                            نقداً
                        @elseif($invoice->payment_method == 'credit_card')
                            بطاقة ائتمان
                        @elseif($invoice->payment_method == 'debit_card')
                            بطاقة خصم
                        @endif
                    </div>
                    <div>
                        حالة الدفع:
                        @if($invoice->payment_status == 'paid')
                            مدفوعة
                        @elseif($invoice->payment_status == 'partial')
                            مدفوعة جزئياً
                        @elseif($invoice->payment_status == 'unpaid')
                            غير مدفوعة
                        @endif
                    </div>
                    <div>الكاشير: {{ $invoice->user->name ?? 'غير محدد' }}</div>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>المنتج</th>
                        <th>السعر</th>
                        <th>الكمية</th>
                        <th>المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    @if($invoice->order && $invoice->order->orderItems->count() > 0)
                        @foreach($invoice->order->orderItems as $index => $item)
                        <tr>
                            <td>{{ $index + 1 }}</td>
                            <td>
                                @if($item->product)
                                    {{ $item->product->name }}
                                @else
                                    منتج محذوف
                                @endif
                                @if($item->notes)
                                    <br><small class="text-muted">{{ $item->notes }}</small>
                                @endif
                            </td>
                            <td>{{ number_format($item->unit_price, 2) }} {{ $restaurantInfo['currency'] }}</td>
                            <td>{{ $item->quantity }}</td>
                            <td>{{ number_format($item->subtotal, 2) }} {{ $restaurantInfo['currency'] }}</td>
                        </tr>
                        @endforeach
                    @else
                        <tr>
                            <td colspan="5" class="text-center">لا توجد عناصر في هذا الطلب</td>
                        </tr>
                    @endif
                </tbody>
            </table>
        </div>

        <div class="total-section">
            <div class="row">
                <div class="col-md-6">
                    @if($invoice->notes)
                    <div class="mb-3">
                        <h6>ملاحظات:</h6>
                        <p>{{ $invoice->notes }}</p>
                    </div>
                    @endif
                    <div>
                        <h6>شكراً لزيارتكم!</h6>
                        <p>نتطلع لرؤيتكم مرة أخرى</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th>المبلغ الإجمالي</th>
                            <td class="text-end">{{ number_format($invoice->total_amount ?? 0, 2) }} {{ $restaurantInfo['currency'] }}</td>
                        </tr>
                        @if($invoice->tax_amount && $invoice->tax_amount > 0)
                        <tr>
                            <th>ضريبة القيمة المضافة ({{ $restaurantInfo['tax_rate'] }}%)</th>
                            <td class="text-end">{{ number_format($invoice->tax_amount, 2) }} {{ $restaurantInfo['currency'] }}</td>
                        </tr>
                        @endif
                        @if($invoice->discount_amount && $invoice->discount_amount > 0)
                        <tr>
                            <th>الخصم</th>
                            <td class="text-end">{{ number_format($invoice->discount_amount, 2) }} {{ $restaurantInfo['currency'] }}</td>
                        </tr>
                        @endif
                        @if($invoice->final_amount && $invoice->final_amount != $invoice->total_amount)
                        <tr>
                            <th>المبلغ النهائي</th>
                            <td class="text-end">{{ number_format($invoice->final_amount, 2) }} {{ $restaurantInfo['currency'] }}</td>
                        </tr>
                        @endif
                        <tr>
                            <th>المبلغ المدفوع</th>
                            <td class="text-end">{{ number_format($invoice->paid_amount ?? 0, 2) }} {{ $restaurantInfo['currency'] }}</td>
                        </tr>
                        @if($invoice->remaining_amount && $invoice->remaining_amount > 0)
                        <tr>
                            <th>المبلغ المتبقي</th>
                            <td class="text-end fw-bold">{{ number_format($invoice->remaining_amount, 2) }} {{ $restaurantInfo['currency'] }}</td>
                        </tr>
                        @endif
                    </table>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>تم إنشاء هذه الفاتورة بواسطة نظام إدارة المطعم</p>
            <p>{{ now()->format('Y-m-d H:i:s') }}</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // طباعة تلقائية عند تحميل الصفحة
        window.onload = function() {
            // تأخير الطباعة لضمان تحميل الصفحة بالكامل
            setTimeout(function() {
                // window.print();
            }, 500);
        };
    </script>
</body>
</html>
