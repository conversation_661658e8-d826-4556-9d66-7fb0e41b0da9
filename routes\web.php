<?php

use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\OrderItemController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\TableController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\WaiterController;
use App\Http\Controllers\ChefController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\ExpenseController;
use App\Http\Controllers\PurchaseController;
use Illuminate\Support\Facades\Route;

// الصفحة الرئيسية
Route::get('/', function () {
    return view('welcome');
});

// مسارات المصادقة
Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');





// مسارات محمية بالمصادقة
Route::middleware(['auth'])->group(function () {
    // لوحة التحكم
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // مسارات المستخدمين (للمدير فقط)
    Route::group(['middleware' => 'admin'], function () {
        Route::resource('users', UserController::class);
        Route::resource('roles', RoleController::class);
    });

    // مسارات الطاولات (متاحة للمدير والكاشير)
    Route::group(['middleware' => 'admin'], function () {
        Route::resource('tables', TableController::class);
    });

    // مسارات التصنيفات (متاحة للمدير والكاشير)
    Route::group(['middleware' => 'admin'], function () {
        Route::resource('categories', CategoryController::class);
    });

    // مسارات المنتجات (متاحة للمدير والكاشير)
    Route::group(['middleware' => 'admin'], function () {
        Route::resource('products', ProductController::class);
        Route::patch('products/{product}/toggle-availability', [ProductController::class, 'toggleAvailability'])->name('products.toggle-availability');
    });

    // مسارات الطلبات (متاحة للمدير والكاشير)
    Route::group(['middleware' => 'admin'], function () {
        Route::resource('orders', OrderController::class);
        Route::get('/orders/{order}/items', [OrderController::class, 'items'])->name('orders.items');
        Route::post('/orders/{order}/status', [OrderController::class, 'updateStatus'])->name('orders.status');
    });

    // مسارات عناصر الطلب (متاحة للمدير والكاشير)
    Route::group(['middleware' => 'admin'], function () {
        Route::resource('order-items', OrderItemController::class);
    });

    // مسارات الفواتير (متاحة للمدير والكاشير)
    Route::group(['middleware' => 'admin'], function () {
        Route::resource('invoices', InvoiceController::class);
        Route::get('invoices/{invoice}/print', [InvoiceController::class, 'print'])->name('invoices.print');
        Route::get('invoices/{invoice}/print/85mm', [InvoiceController::class, 'print85mm'])->name('invoices.print.85mm');
        Route::get('invoices/{invoice}/print/56mm', [InvoiceController::class, 'print56mm'])->name('invoices.print.56mm');
    });

    // مسارات التقارير (للمدير والمدير العام فقط)
    Route::group(['middleware' => 'admin'], function () {
        Route::get('/reports/sales', [ReportController::class, 'sales'])->name('reports.sales');
        Route::get('/reports/orders', [ReportController::class, 'orders'])->name('reports.orders');
        Route::get('/reports/products', [ReportController::class, 'products'])->name('reports.products');
    });

    // مسارات الإعدادات (للمدير فقط)
    Route::group(['middleware' => 'admin'], function () {
        Route::get('/settings', [SettingController::class, 'index'])->name('settings.index');
        Route::get('/settings/edit', [SettingController::class, 'edit'])->name('settings.edit');
        Route::put('/settings', [SettingController::class, 'update'])->name('settings.update');
        Route::delete('/settings/logo', [SettingController::class, 'deleteLogo'])->name('settings.delete-logo');
        Route::delete('/settings/reset', [SettingController::class, 'reset'])->name('settings.reset');
        Route::get('/settings/export', [SettingController::class, 'export'])->name('settings.export');
    });

    // مسارات المصروفات (للمدير والمدير العام فقط)
    Route::group(['middleware' => 'admin'], function () {
        Route::resource('expenses', ExpenseController::class);
        Route::get('/expenses-export', [ExpenseController::class, 'export'])->name('expenses.export');
    });

    // مسارات المشتريات (للمدير والمدير العام فقط)
    Route::group(['middleware' => 'admin'], function () {
        Route::resource('purchases', PurchaseController::class);
        Route::get('/purchases-export', [PurchaseController::class, 'export'])->name('purchases.export');
    });

    // صفحة اختبار النسخ الاحتياطي (للمدير فقط)
    Route::get('/test-backup', function() {
        return view('test-backup');
    })->middleware('admin')->name('test.backup');

    // صفحة اختبار الصلاحيات
    Route::get('/test-permissions', function() {
        $user = auth()->user();
        return response()->json([
            'user' => $user,
            'hasRole_admin' => $user ? $user->hasRole('admin') : false,
            'hasRole_manager' => $user ? $user->hasRole('manager') : false,
            'role_id' => $user ? $user->role_id : null,
            'role_name' => $user ? $user->role : null,
            'getUserRole' => $user ? $user->getUserRole() : null,
        ]);
    })->middleware('auth')->name('test.permissions');

    // اختبار النسخ الاحتياطي بدون middleware
    Route::post('/test-backup-direct', function(Illuminate\Http\Request $request) {
        $user = auth()->user();

        // التحقق اليدوي من الصلاحيات
        if (!$user) {
            return response()->json(['error' => 'غير مسجل دخول'], 401);
        }

        if (!$user->hasRole('admin') && !$user->hasRole('manager')) {
            return response()->json([
                'error' => 'ليس لديك صلاحية',
                'user_role' => $user->role,
                'role_id' => $user->role_id,
                'hasRole_admin' => $user->hasRole('admin'),
                'hasRole_manager' => $user->hasRole('manager')
            ], 403);
        }

        // استدعاء BackupController مباشرة
        $controller = new App\Http\Controllers\BackupController();
        return $controller->createBackupSync($request);
    })->middleware('auth')->name('test.backup.direct');

    // مسارات النسخ الاحتياطي (للمدير فقط)
    Route::group(['middleware' => 'admin'], function () {
        Route::get('/backup', function() {
            $backups = \App\Models\Backup::with('creator')->orderBy('created_at', 'desc')->paginate(20);
            $statistics = \App\Models\Backup::getStatistics();
            $recentBackups = \App\Models\Backup::recent(5);
            return view('admin.backup.enhanced', compact('backups', 'statistics', 'recentBackups'));
        })->name('backup.index');

        Route::post('/backup/create', [App\Http\Controllers\BackupController::class, 'createBackup'])->name('backup.create');
        Route::post('/backup/create-sync', [App\Http\Controllers\BackupController::class, 'createBackupSync'])->name('backup.create-sync');
        Route::get('/backup/download/{filename}', [App\Http\Controllers\BackupController::class, 'downloadBackup'])->name('backup.download');
        Route::delete('/backup/delete/{filename}', [App\Http\Controllers\BackupController::class, 'deleteBackup'])->name('backup.delete');
        Route::post('/backup/delete/{filename}', [App\Http\Controllers\BackupController::class, 'deleteBackup'])->name('backup.delete.post');
        Route::post('/backup/cleanup-pending', [App\Http\Controllers\BackupController::class, 'cleanupPending'])->name('backup.cleanup.pending');
        Route::post('/backup/restore', [App\Http\Controllers\BackupController::class, 'restoreDatabase'])->name('backup.restore');
        Route::post('/backup/restore-direct', [App\Http\Controllers\BackupController::class, 'restoreBackupDirect'])->name('backup.restore-direct');
    });



    // مسارات واجهة الكاشير - متاحة للكاشير والمدير
    Route::group(['prefix' => 'waiter'], function () {
        Route::get('/', [WaiterController::class, 'index'])->name('waiter.index');

        // المسارات التي لا تحتوي على معرف
        Route::post('/orders', [WaiterController::class, 'createOrder'])->name('waiter.orders.create');
        Route::get('/orders/active', [WaiterController::class, 'getActiveOrders'])->name('waiter.orders.active');
        Route::post('/orders/suspend', [WaiterController::class, 'suspendOrder'])->name('waiter.orders.suspend');

        // المسارات التي تحتوي على معرف
        Route::get('/orders/{id}', [WaiterController::class, 'getOrder'])->name('waiter.orders.get');
        Route::post('/orders/{id}/pay', [WaiterController::class, 'payOrder'])->name('waiter.orders.pay');
        Route::post('/orders/{id}/status', [WaiterController::class, 'updateOrderStatus'])->name('waiter.orders.status');
        Route::put('/orders/{id}/status', [WaiterController::class, 'updateOrderStatus'])->name('waiter.orders.status.put');
        Route::post('/orders/{id}/add-product', [WaiterController::class, 'addProductToOrder'])->name('waiter.orders.add-product');
        Route::post('/kitchen-order', [WaiterController::class, 'createKitchenOrder'])->name('waiter.kitchen-order.create');
        Route::get('/kitchen-order/{id}', [WaiterController::class, 'printKitchenOrder'])->name('waiter.kitchen-order.print');
        Route::post('/orders/{id}/remove-product', [WaiterController::class, 'removeProductFromOrder'])->name('waiter.orders.remove-product');

        // مسارات الإشعارات للكاشير
        Route::get('/notifications', [NotificationController::class, 'getNotifications'])->name('waiter.notifications.get');
        Route::post('/notifications/{id}/read', [NotificationController::class, 'markAsRead'])->name('waiter.notifications.mark-as-read');
        Route::post('/notifications/read-all', [NotificationController::class, 'markAllAsRead'])->name('waiter.notifications.mark-all-as-read');
    });

    // مسارات واجهة الشيف - متاحة لدور المطبخ والمدير
    Route::group(['prefix' => 'chef'], function () {
        Route::get('/', [ChefController::class, 'index'])->name('chef.index');

        // المسارات التي لا تحتوي على معرف
        Route::get('/orders/active', [ChefController::class, 'getActiveOrders'])->name('chef.orders.active');

        // المسارات التي تحتوي على معرف
        Route::get('/orders/{id}', [ChefController::class, 'getOrder'])->name('chef.orders.get');
        Route::post('/orders/{id}/status', [ChefController::class, 'updateOrderStatus'])->name('chef.orders.status');
    });

    // مسارات الإشعارات (متاحة للمدير والكاشير)
    Route::group(['prefix' => 'notifications', 'middleware' => 'admin'], function () {
        Route::get('/', [NotificationController::class, 'getNotifications'])->name('notifications.get');
        Route::post('/{id}/read', [NotificationController::class, 'markAsRead'])->name('notifications.mark-as-read');
        Route::post('/read-all', [NotificationController::class, 'markAllAsRead'])->name('notifications.mark-all-as-read');
    });
});






