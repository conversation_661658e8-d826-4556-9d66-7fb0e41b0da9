<?php $__env->startSection('title', 'تقرير المبيعات'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 20px;
    }
    .report-card {
        transition: all 0.3s;
    }
    .report-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>تقرير المبيعات</h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?php echo e(route('reports.orders')); ?>" class="btn btn-secondary">
                <i class="fas fa-chart-line me-1"></i> تقرير الطلبات
            </a>
        </div>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="<?php echo e(route('reports.sales')); ?>" method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo e($startDate); ?>">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo e($endDate); ?>">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter me-1"></i> تصفية
                    </button>
                    <a href="<?php echo e(route('reports.sales')); ?>" class="btn btn-secondary">
                        <i class="fas fa-redo me-1"></i> إعادة تعيين
                    </a>
                </div>
            </form>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white report-card">
                <div class="card-body">
                    <h5 class="card-title">إجمالي المبيعات</h5>
                    <p class="card-text display-6"><?php echo e(number_format($totalSales, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></p>
                    <p class="card-text">عدد الفواتير: <?php echo e($invoices->count()); ?></p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white report-card">
                <div class="card-body">
                    <h5 class="card-title">إجمالي المدفوعات</h5>
                    <p class="card-text display-6"><?php echo e(number_format($totalPaid, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></p>
                    <p class="card-text">نسبة التحصيل: <?php echo e($totalSales > 0 ? number_format(($totalPaid / $totalSales) * 100, 1) : 0); ?>%</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-danger text-white report-card">
                <div class="card-body">
                    <h5 class="card-title">إجمالي المتبقي</h5>
                    <p class="card-text display-6"><?php echo e(number_format($totalRemaining, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></p>
                    <p class="card-text">نسبة المتبقي: <?php echo e($totalSales > 0 ? number_format(($totalRemaining / $totalSales) * 100, 1) : 0); ?>%</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">حالة الفواتير</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="invoiceStatusChart"></canvas>
                    </div>
                    <div class="row text-center mt-3">
                        <div class="col-md-4">
                            <div class="card bg-success text-white">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-0">مدفوعة</h6>
                                    <p class="card-text mb-0"><?php echo e($paidInvoices); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-warning text-dark">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-0">مدفوعة جزئياً</h6>
                                    <p class="card-text mb-0"><?php echo e($partialInvoices); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-danger text-white">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-0">غير مدفوعة</h6>
                                    <p class="card-text mb-0"><?php echo e($unpaidInvoices); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">طرق الدفع</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="paymentMethodChart"></canvas>
                    </div>
                    <div class="row text-center mt-3">
                        <div class="col-md-4">
                            <div class="card bg-success text-white">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-0">نقداً</h6>
                                    <p class="card-text mb-0"><?php echo e(number_format($salesByPaymentMethod['cash'], 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-info text-white">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-0">بطاقة ائتمان</h6>
                                    <p class="card-text mb-0"><?php echo e(number_format($salesByPaymentMethod['credit_card'], 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-0">بطاقة خصم</h6>
                                    <p class="card-text mb-0"><?php echo e(number_format($salesByPaymentMethod['debit_card'], 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">المبيعات اليومية</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="dailySalesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">المنتجات الأكثر مبيعًا</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>المنتج</th>
                                    <th>الكمية المباعة</th>
                                    <th>إجمالي المبيعات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $topProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($index + 1); ?></td>
                                    <td>
                                        <?php if($product->product): ?>
                                            <a href="<?php echo e(route('products.show', $product->product->id)); ?>"><?php echo e($product->product->name); ?></a>
                                        <?php else: ?>
                                            منتج محذوف
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($product->total_quantity); ?></td>
                                    <td><?php echo e(number_format($product->total_sales, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="4" class="text-center">لا توجد بيانات</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-invoice-dollar me-2"></i>
                        قائمة الفواتير
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>التاريخ</th>
                                    <th>الطاولة</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>المبلغ المدفوع</th>
                                    <th>المبلغ المتبقي</th>
                                    <th>حالة الدفع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $invoices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $invoice): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($invoice->invoice_number); ?></td>
                                    <td><?php echo e($invoice->created_at->format('Y-m-d')); ?></td>
                                    <td>
                                        <?php if($invoice->order && $invoice->order->table): ?>
                                            <?php echo e($invoice->order->table->name); ?>

                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e(number_format($invoice->total_amount, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></td>
                                    <td><?php echo e(number_format($invoice->paid_amount, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></td>
                                    <td><?php echo e(number_format($invoice->remaining_amount, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></td>
                                    <td>
                                        <?php if($invoice->payment_status == 'paid'): ?>
                                            <span class="badge bg-success">مدفوعة</span>
                                        <?php elseif($invoice->payment_status == 'partial'): ?>
                                            <span class="badge bg-warning">مدفوعة جزئياً</span>
                                        <?php elseif($invoice->payment_status == 'unpaid'): ?>
                                            <span class="badge bg-danger">غير مدفوعة</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="<?php echo e(route('invoices.show', $invoice->id)); ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('invoices.print', $invoice->id)); ?>" class="btn btn-sm btn-primary" target="_blank">
                                            <i class="fas fa-print"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="8" class="text-center">لا توجد بيانات</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- معلومات النتائج -->
                    <div class="d-flex justify-content-between align-items-center mt-3 mb-3">
                        <div class="text-muted">
                            <small>
                                <i class="fas fa-info-circle me-1"></i>
                                عرض <?php echo e($invoices->firstItem() ?? 0); ?> - <?php echo e($invoices->lastItem() ?? 0); ?> من <?php echo e($invoices->total()); ?> فاتورة
                            </small>
                        </div>
                        <div class="text-muted">
                            <small>
                                <i class="fas fa-file-invoice me-1"></i>
                                <?php echo e($invoices->perPage()); ?> فاتورة في الصفحة
                            </small>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <?php if($invoices->hasPages()): ?>
                        <div class="d-flex justify-content-center">
                            <?php echo e($invoices->appends(request()->query())->links('custom-pagination')); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // حالة الفواتير
        const invoiceStatusCtx = document.getElementById('invoiceStatusChart').getContext('2d');
        const invoiceStatusChart = new Chart(invoiceStatusCtx, {
            type: 'pie',
            data: {
                labels: ['مدفوعة', 'مدفوعة جزئياً', 'غير مدفوعة'],
                datasets: [{
                    data: [<?php echo e($paidInvoices); ?>, <?php echo e($partialInvoices); ?>, <?php echo e($unpaidInvoices); ?>],
                    backgroundColor: ['#198754', '#ffc107', '#dc3545'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // طرق الدفع
        const paymentMethodCtx = document.getElementById('paymentMethodChart').getContext('2d');
        const paymentMethodChart = new Chart(paymentMethodCtx, {
            type: 'pie',
            data: {
                labels: ['نقداً', 'بطاقة ائتمان', 'بطاقة خصم'],
                datasets: [{
                    data: [
                        <?php echo e($salesByPaymentMethod['cash']); ?>,
                        <?php echo e($salesByPaymentMethod['credit_card']); ?>,
                        <?php echo e($salesByPaymentMethod['debit_card']); ?>

                    ],
                    backgroundColor: ['#198754', '#0dcaf0', '#0d6efd'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // المبيعات اليومية
        const dailySalesCtx = document.getElementById('dailySalesChart').getContext('2d');
        const dailySalesChart = new Chart(dailySalesCtx, {
            type: 'bar',
            data: {
                labels: [
                    <?php $__currentLoopData = $dailySales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $date => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        '<?php echo e($date); ?>',
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                ],
                datasets: [{
                    label: 'إجمالي المبيعات',
                    data: [
                        <?php $__currentLoopData = $dailySales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php echo e($data['total']); ?>,
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    ],
                    backgroundColor: '#0d6efd',
                    borderWidth: 1
                }, {
                    label: 'المبلغ المدفوع',
                    data: [
                        <?php $__currentLoopData = $dailySales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php echo e($data['paid']); ?>,
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    ],
                    backgroundColor: '#198754',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\restaurant\resources\views/reports/sales.blade.php ENDPATH**/ ?>