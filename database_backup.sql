-- نسخة احتياطية لقاعدة البيانات
-- تاريخ الإنشاء: 2025-05-24 11:56:38


-- --------------------------------------------------------
-- بنية الجدول `users`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `users`;
CREATE TABLE "users" ("id" integer primary key autoincrement not null, "name" varchar not null, "email" varchar not null, "email_verified_at" datetime, "password" varchar not null, "role_id" integer, "remember_token" varchar, "created_at" datetime, "updated_at" datetime, "role" varchar not null default 'admin', foreign key("role_id") references "roles"("id") on delete set null);

-- بيانات الجدول `users`
INSERT INTO `users` (`id`, `name`, `email`, `email_verified_at`, `password`, `role_id`, `remember_token`, `created_at`, `updated_at`, `role`) VALUES ('1', 'مدير النظام', '<EMAIL>', '2025-05-23 09:50:36', '$2y$12$BflXD1oanQlig0rNgEtNpeuM./dB18EmzagVEXPKGxpxZODjepSzC', '1', 'sNrTO7qVEdoWkBOMiBRFfkxn4cjU7xTRZHX2mMGRy1uqYG1Z3xe2hAdrCxrY', '2025-05-23 09:50:37', '2025-05-23 10:58:58', 'admin');
INSERT INTO `users` (`id`, `name`, `email`, `email_verified_at`, `password`, `role_id`, `remember_token`, `created_at`, `updated_at`, `role`) VALUES ('2', 'مدير المطعم', '<EMAIL>', '2025-05-23 09:50:37', '$2y$12$43oA/D6fS0WosxXQuPv8VeRQrsJ96Z4K0PcgYu38Runo/FcAeGnny', '2', 'aqYmz2oYCozAQhx6bJqhv4bPoSrxLyqUCmzWVsXjbY5CunchJT3KTUVEGQ1H', '2025-05-23 09:50:37', '2025-05-23 10:55:46', 'admin');
INSERT INTO `users` (`id`, `name`, `email`, `email_verified_at`, `password`, `role_id`, `remember_token`, `created_at`, `updated_at`, `role`) VALUES ('6', 'Demetrius Monahan', '<EMAIL>', '2025-05-23 09:50:37', '$2y$12$RVFOBAywXAvlFHe/yNdwi.ct7kg98yKvinaZZigS2F0sEyTuPSC9a', '4', '2N6hCAZzbS5lagZxifkOijupxTBR4dXqC3jgnKftz0ewAEpTe0b6wAHqrjh3', '2025-05-23 09:50:37', '2025-05-23 11:04:43', 'admin');
INSERT INTO `users` (`id`, `name`, `email`, `email_verified_at`, `password`, `role_id`, `remember_token`, `created_at`, `updated_at`, `role`) VALUES ('8', 'Diamond Nienow', '<EMAIL>', '2025-05-23 09:50:37', '$2y$12$zoVrgJucxRN.vxYk07cEMOEn7s38olrA3eMbnN52ushvj7kukXeUW', '5', 'pfSa3toMcxQJq8p01lSYUns5Yob2Ulhd1tQz679jUNV0Lv7ZWV6poxKnSJTr', '2025-05-23 09:50:37', '2025-05-23 10:56:53', 'admin');
INSERT INTO `users` (`id`, `name`, `email`, `email_verified_at`, `password`, `role_id`, `remember_token`, `created_at`, `updated_at`, `role`) VALUES ('10', 'شيف تجريبي', '<EMAIL>', NULL, '$2y$12$BbPa7vjPK1/7NegVM1fZFOZd23Ps32Z3Rt3xLMPbIsFT7fg2KBoFC', '5', NULL, '2025-05-23 11:06:26', '2025-05-23 11:06:26', 'admin');


-- --------------------------------------------------------
-- بنية الجدول `password_reset_tokens`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `password_reset_tokens`;
CREATE TABLE "password_reset_tokens" ("email" varchar not null, "token" varchar not null, "created_at" datetime, primary key ("email"));


-- --------------------------------------------------------
-- بنية الجدول `sessions`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `sessions`;
CREATE TABLE "sessions" ("id" varchar not null, "user_id" integer, "ip_address" varchar, "user_agent" text, "payload" text not null, "last_activity" integer not null, primary key ("id"));

-- بيانات الجدول `sessions`
INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES ('Nw8bqxfmftEhlhIvkcXhsl74CjASNPUsjeXVVNlz', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiOFRMUVk0R1VqaTlmMGtMdElvSGl4YzZqSzVMTTZwVFZQeTRYaTRNTCI7czozOiJ1cmwiO2E6MTp7czo4OiJpbnRlbmRlZCI7czo0MzoiaHR0cDovL2xvY2FsaG9zdC9yZXN0YXVyYW50L3B1YmxpYy9pbnZvaWNlcyI7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjQzOiJodHRwOi8vbG9jYWxob3N0L3Jlc3RhdXJhbnQvcHVibGljL2ludm9pY2VzIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', '1748083077');
INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES ('GuJpJf6S7YDeGcNwTRxXSPZEC08pMiNESSkuRBHp', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiMEZCdGJCVW9NQ0dubnlKcG13NWhBRnBzV2IzSHFJZ3FYRDJ6c3MzQSI7czozOiJ1cmwiO2E6MTp7czo4OiJpbnRlbmRlZCI7czo1NzoiaHR0cDovL2xvY2FsaG9zdC9yZXN0YXVyYW50L3B1YmxpYy9pbnZvaWNlcy8xMS9wcmludC81Nm1tIjt9czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NTc6Imh0dHA6Ly9sb2NhbGhvc3QvcmVzdGF1cmFudC9wdWJsaWMvaW52b2ljZXMvMTEvcHJpbnQvNTZtbSI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', '1748083077');
INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES ('wGS3oBSeTBjFFCt1L46TDiPam9FAPJLjve9lLzHc', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoickx3RjFKcmFxeW5GQVhiS3dQWk91MVlHRWpmRElEVXN6U2V4QnRRMiI7czozOiJ1cmwiO2E6MTp7czo4OiJpbnRlbmRlZCI7czo1MjoiaHR0cDovL2xvY2FsaG9zdC9yZXN0YXVyYW50L3B1YmxpYy9pbnZvaWNlcy8xMS9wcmludCI7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjUyOiJodHRwOi8vbG9jYWxob3N0L3Jlc3RhdXJhbnQvcHVibGljL2ludm9pY2VzLzExL3ByaW50Ijt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', '1748083077');
INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES ('rIMifkXtxtfGyP4VHOblTm0lZqZc8JwJz5UJUF8y', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoibXpGNDdMQTFrdFhrT3Bnc3lGc2p3SFhWcWxTSGo1aWJVNWRGMWpLeiI7czozOiJ1cmwiO2E6MTp7czo4OiJpbnRlbmRlZCI7czo1NzoiaHR0cDovL2xvY2FsaG9zdC9yZXN0YXVyYW50L3B1YmxpYy9pbnZvaWNlcy8xMS9wcmludC84NW1tIjt9czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NTc6Imh0dHA6Ly9sb2NhbGhvc3QvcmVzdGF1cmFudC9wdWJsaWMvaW52b2ljZXMvMTEvcHJpbnQvODVtbSI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', '1748083077');
INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES ('xM035zwg6OPXEuyviA2EUyzkiLbSV02bbSxBYKRI', '6', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiM1NwTzh1NlA2Y1o0dlFlQWJiWnlsMHAzSHdZOFJmaE5kblgxcXFYTSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NTU6Imh0dHA6Ly9sb2NhbGhvc3QvcmVzdGF1cmFudC9wdWJsaWMvd2FpdGVyL29yZGVycy9hY3RpdmUiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aTo2O30=', '1748083384');
INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES ('HjmXYZi7KjnsN8RwRC3OT8L5zmuZ23zpZYrQA0hU', '1', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiTzlGNUhQWEtaRmlYYVRLTFVKY1ZJTEsxdFZYSU5QYko3QTBlb2hZdiI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NTM6Imh0dHA6Ly9sb2NhbGhvc3QvcmVzdGF1cmFudC9wdWJsaWMvY2hlZi9vcmRlcnMvYWN0aXZlIjt9czozOiJ1cmwiO2E6MDp7fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7fQ==', '1748085728');
INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES ('yRuhrFzTO5RDpFpbLRsU7FX7AZQsgi8zQma4K0dl', '6', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiUXdqSGRsaUFjaVAzRmxPR1hsa0xvSGg4UnQ3eTU5OFYxaVFZTHF0NCI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDQ6Imh0dHA6Ly9sb2NhbGhvc3QvcmVzdGF1cmFudC9wdWJsaWMvZGFzaGJvYXJkIjt9czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6Njt9', '1748085876');
INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES ('1030jtHeZ5r3FArhQrnBC4vckdL1BitIjfKjg4DC', '1', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiVW40TERRQmljNmdmZW44MXRBNko5N0RCQzhKbHlaZWs1enRHV3JUUSI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjQwOiJodHRwOi8vMTI3LjAuMC4xOjgwMDAvY2hlZi9vcmRlcnMvYWN0aXZlIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTt9', '1748087796');
INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES ('wQ2vACNOeWoteLa0B3m782qbIsGySFaRrbtqSJlV', '1', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiSHJMTDBZWm1VeTF3NkZvekdsWDdYVkNJQ1gydnZVUjR3cHRydWF6RSI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NTM6Imh0dHA6Ly9sb2NhbGhvc3QvcmVzdGF1cmFudC9wdWJsaWMvY2hlZi9vcmRlcnMvYWN0aXZlIjt9czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTt9', '1748087769');


-- --------------------------------------------------------
-- بنية الجدول `cache`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `cache`;
CREATE TABLE "cache" ("key" varchar not null, "value" text not null, "expiration" integer not null, primary key ("key"));

-- بيانات الجدول `cache`
INSERT INTO `cache` (`key`, `value`, `expiration`) VALUES ('laravel_cache_setting_restaurant_name', 's:38:\"مطعم الوجبات اللذيذة\";', '1748091272');
INSERT INTO `cache` (`key`, `value`, `expiration`) VALUES ('laravel_cache_setting_restaurant_logo', 's:50:\"logos/27IlPuVnM0uQFB54rhCgGAIsJz2uxumDZeRKsN8C.png\";', '1748091272');
INSERT INTO `cache` (`key`, `value`, `expiration`) VALUES ('laravel_cache_setting_currency', 's:5:\"ر.ع\";', '1748091375');
INSERT INTO `cache` (`key`, `value`, `expiration`) VALUES ('laravel_cache_setting_tax_rate', 'd:5;', '1748091375');


-- --------------------------------------------------------
-- بنية الجدول `cache_locks`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `cache_locks`;
CREATE TABLE "cache_locks" ("key" varchar not null, "owner" varchar not null, "expiration" integer not null, primary key ("key"));


-- --------------------------------------------------------
-- بنية الجدول `jobs`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `jobs`;
CREATE TABLE "jobs" ("id" integer primary key autoincrement not null, "queue" varchar not null, "payload" text not null, "attempts" integer not null, "reserved_at" integer, "available_at" integer not null, "created_at" integer not null);

-- بيانات الجدول `jobs`
INSERT INTO `jobs` (`id`, `queue`, `payload`, `attempts`, `reserved_at`, `available_at`, `created_at`) VALUES ('4', 'default', '{\"uuid\":\"e13486f6-0fac-4f62-8046-c12176a60a1f\",\"displayName\":\"App\\\\Jobs\\\\BackupJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":3600,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\BackupJob\",\"command\":\"O:18:\\\"App\\\\Jobs\\\\BackupJob\\\":6:{s:13:\\\"\\u0000*\\u0000backupType\\\";s:8:\\\"database\\\";s:12:\\\"\\u0000*\\u0000sendEmail\\\";b:0;s:16:\\\"\\u0000*\\u0000uploadToDrive\\\";b:0;s:9:\\\"\\u0000*\\u0000userId\\\";i:1;s:8:\\\"\\u0000*\\u0000jobId\\\";s:20:\\\"backup_683063ccb20c3\\\";s:11:\\\"\\u0000*\\u0000backupId\\\";i:4;}\"},\"createdAt\":1748001740,\"delay\":null}', '0', NULL, '1748001740', '1748001740');
INSERT INTO `jobs` (`id`, `queue`, `payload`, `attempts`, `reserved_at`, `available_at`, `created_at`) VALUES ('5', 'default', '{\"uuid\":\"8988db0f-f012-41c0-9c9d-2def8d7ca2c8\",\"displayName\":\"App\\\\Jobs\\\\BackupJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":3600,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\BackupJob\",\"command\":\"O:18:\\\"App\\\\Jobs\\\\BackupJob\\\":6:{s:13:\\\"\\u0000*\\u0000backupType\\\";s:8:\\\"database\\\";s:12:\\\"\\u0000*\\u0000sendEmail\\\";b:0;s:16:\\\"\\u0000*\\u0000uploadToDrive\\\";b:0;s:9:\\\"\\u0000*\\u0000userId\\\";i:1;s:8:\\\"\\u0000*\\u0000jobId\\\";s:20:\\\"backup_6830648b6a5e9\\\";s:11:\\\"\\u0000*\\u0000backupId\\\";i:5;}\"},\"createdAt\":1748001931,\"delay\":null}', '0', NULL, '1748001931', '1748001931');
INSERT INTO `jobs` (`id`, `queue`, `payload`, `attempts`, `reserved_at`, `available_at`, `created_at`) VALUES ('6', 'default', '{\"uuid\":\"bc45c776-be1a-47a0-8400-b1d7a16b6ef9\",\"displayName\":\"App\\\\Jobs\\\\BackupJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":3600,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\BackupJob\",\"command\":\"O:18:\\\"App\\\\Jobs\\\\BackupJob\\\":6:{s:13:\\\"\\u0000*\\u0000backupType\\\";s:8:\\\"database\\\";s:12:\\\"\\u0000*\\u0000sendEmail\\\";b:0;s:16:\\\"\\u0000*\\u0000uploadToDrive\\\";b:0;s:9:\\\"\\u0000*\\u0000userId\\\";i:1;s:8:\\\"\\u0000*\\u0000jobId\\\";s:20:\\\"backup_6830649fe1692\\\";s:11:\\\"\\u0000*\\u0000backupId\\\";i:6;}\"},\"createdAt\":1748001951,\"delay\":null}', '0', NULL, '1748001951', '1748001951');
INSERT INTO `jobs` (`id`, `queue`, `payload`, `attempts`, `reserved_at`, `available_at`, `created_at`) VALUES ('7', 'default', '{\"uuid\":\"81d34d99-a512-4831-9045-7c29b99d1f9a\",\"displayName\":\"App\\\\Jobs\\\\BackupJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":3600,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\BackupJob\",\"command\":\"O:18:\\\"App\\\\Jobs\\\\BackupJob\\\":6:{s:13:\\\"\\u0000*\\u0000backupType\\\";s:8:\\\"database\\\";s:12:\\\"\\u0000*\\u0000sendEmail\\\";b:0;s:16:\\\"\\u0000*\\u0000uploadToDrive\\\";b:0;s:9:\\\"\\u0000*\\u0000userId\\\";i:1;s:8:\\\"\\u0000*\\u0000jobId\\\";s:20:\\\"backup_6830681ec1cd5\\\";s:11:\\\"\\u0000*\\u0000backupId\\\";i:8;}\"},\"createdAt\":1748002846,\"delay\":null}', '0', NULL, '1748002846', '1748002846');
INSERT INTO `jobs` (`id`, `queue`, `payload`, `attempts`, `reserved_at`, `available_at`, `created_at`) VALUES ('8', 'default', '{\"uuid\":\"459564ae-fa63-443d-9791-8a132a1be4f2\",\"displayName\":\"App\\\\Jobs\\\\BackupJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":3600,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\BackupJob\",\"command\":\"O:18:\\\"App\\\\Jobs\\\\BackupJob\\\":4:{s:13:\\\"\\u0000*\\u0000backupType\\\";s:8:\\\"database\\\";s:9:\\\"\\u0000*\\u0000userId\\\";i:1;s:8:\\\"\\u0000*\\u0000jobId\\\";s:20:\\\"backup_6830698cb4c5f\\\";s:11:\\\"\\u0000*\\u0000backupId\\\";i:9;}\"},\"createdAt\":1748003212,\"delay\":null}', '0', NULL, '1748003212', '1748003212');
INSERT INTO `jobs` (`id`, `queue`, `payload`, `attempts`, `reserved_at`, `available_at`, `created_at`) VALUES ('9', 'default', '{\"uuid\":\"a47bacfe-fd5b-473a-8185-13ba69db9ac1\",\"displayName\":\"App\\\\Jobs\\\\BackupJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":3600,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\BackupJob\",\"command\":\"O:18:\\\"App\\\\Jobs\\\\BackupJob\\\":4:{s:13:\\\"\\u0000*\\u0000backupType\\\";s:8:\\\"database\\\";s:9:\\\"\\u0000*\\u0000userId\\\";i:1;s:8:\\\"\\u0000*\\u0000jobId\\\";s:20:\\\"backup_68306ade11008\\\";s:11:\\\"\\u0000*\\u0000backupId\\\";i:10;}\"},\"createdAt\":1748003550,\"delay\":null}', '0', NULL, '1748003550', '1748003550');
INSERT INTO `jobs` (`id`, `queue`, `payload`, `attempts`, `reserved_at`, `available_at`, `created_at`) VALUES ('10', 'default', '{\"uuid\":\"176f1856-0ae1-4af2-b74d-836822e41ca4\",\"displayName\":\"App\\\\Jobs\\\\BackupJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":3600,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\BackupJob\",\"command\":\"O:18:\\\"App\\\\Jobs\\\\BackupJob\\\":4:{s:13:\\\"\\u0000*\\u0000backupType\\\";s:8:\\\"database\\\";s:9:\\\"\\u0000*\\u0000userId\\\";i:1;s:8:\\\"\\u0000*\\u0000jobId\\\";s:20:\\\"backup_6831b093479aa\\\";s:11:\\\"\\u0000*\\u0000backupId\\\";i:11;}\"},\"createdAt\":1748086933,\"delay\":null}', '0', NULL, '1748086933', '1748086933');
INSERT INTO `jobs` (`id`, `queue`, `payload`, `attempts`, `reserved_at`, `available_at`, `created_at`) VALUES ('11', 'default', '{\"uuid\":\"60ca641f-e7f4-46ac-8244-66b8db171b4f\",\"displayName\":\"App\\\\Jobs\\\\BackupJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":3600,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\BackupJob\",\"command\":\"O:18:\\\"App\\\\Jobs\\\\BackupJob\\\":4:{s:13:\\\"\\u0000*\\u0000backupType\\\";s:8:\\\"database\\\";s:9:\\\"\\u0000*\\u0000userId\\\";i:1;s:8:\\\"\\u0000*\\u0000jobId\\\";s:20:\\\"backup_6831b0cc4bec8\\\";s:11:\\\"\\u0000*\\u0000backupId\\\";i:12;}\"},\"createdAt\":1748086988,\"delay\":null}', '0', NULL, '1748086988', '1748086988');
INSERT INTO `jobs` (`id`, `queue`, `payload`, `attempts`, `reserved_at`, `available_at`, `created_at`) VALUES ('12', 'default', '{\"uuid\":\"4d58a23f-5e97-47d2-801b-7fd3267bdf63\",\"displayName\":\"App\\\\Jobs\\\\BackupJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":3600,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\BackupJob\",\"command\":\"O:18:\\\"App\\\\Jobs\\\\BackupJob\\\":5:{s:13:\\\"\\u0000*\\u0000backupType\\\";s:8:\\\"database\\\";s:12:\\\"\\u0000*\\u0000sendEmail\\\";b:1;s:16:\\\"\\u0000*\\u0000uploadToDrive\\\";b:0;s:8:\\\"\\u0000*\\u0000jobId\\\";s:20:\\\"backup_68305ef621a4f\\\";s:11:\\\"\\u0000*\\u0000backupId\\\";i:1;}\"},\"createdAt\":1748000502,\"delay\":null}', '1', NULL, '1748087037', '1748087037');
INSERT INTO `jobs` (`id`, `queue`, `payload`, `attempts`, `reserved_at`, `available_at`, `created_at`) VALUES ('13', 'default', '{\"uuid\":\"8331b99d-4773-4315-9efe-07cd635bf47d\",\"displayName\":\"App\\\\Jobs\\\\BackupJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":3600,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\BackupJob\",\"command\":\"O:18:\\\"App\\\\Jobs\\\\BackupJob\\\":6:{s:13:\\\"\\u0000*\\u0000backupType\\\";s:8:\\\"database\\\";s:12:\\\"\\u0000*\\u0000sendEmail\\\";b:0;s:16:\\\"\\u0000*\\u0000uploadToDrive\\\";b:0;s:9:\\\"\\u0000*\\u0000userId\\\";i:1;s:8:\\\"\\u0000*\\u0000jobId\\\";s:20:\\\"backup_68306364ad1bb\\\";s:11:\\\"\\u0000*\\u0000backupId\\\";i:2;}\"},\"createdAt\":1748001636,\"delay\":null}', '1', NULL, '1748087101', '1748087101');
INSERT INTO `jobs` (`id`, `queue`, `payload`, `attempts`, `reserved_at`, `available_at`, `created_at`) VALUES ('14', 'default', '{\"uuid\":\"fa953fc9-f85f-423d-a267-8ba20b027b68\",\"displayName\":\"App\\\\Jobs\\\\BackupJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":3600,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\BackupJob\",\"command\":\"O:18:\\\"App\\\\Jobs\\\\BackupJob\\\":4:{s:13:\\\"\\u0000*\\u0000backupType\\\";s:8:\\\"database\\\";s:9:\\\"\\u0000*\\u0000userId\\\";i:1;s:8:\\\"\\u0000*\\u0000jobId\\\";s:20:\\\"backup_6831b14c2bab9\\\";s:11:\\\"\\u0000*\\u0000backupId\\\";i:13;}\"},\"createdAt\":1748087116,\"delay\":null}', '0', NULL, '1748087116', '1748087116');
INSERT INTO `jobs` (`id`, `queue`, `payload`, `attempts`, `reserved_at`, `available_at`, `created_at`) VALUES ('15', 'default', '{\"uuid\":\"d1069a12-9b36-4850-a35d-0cf9b4cc0a98\",\"displayName\":\"App\\\\Jobs\\\\BackupJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":3600,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\BackupJob\",\"command\":\"O:18:\\\"App\\\\Jobs\\\\BackupJob\\\":4:{s:13:\\\"\\u0000*\\u0000backupType\\\";s:8:\\\"database\\\";s:9:\\\"\\u0000*\\u0000userId\\\";i:1;s:8:\\\"\\u0000*\\u0000jobId\\\";s:20:\\\"backup_6831b19d41f31\\\";s:11:\\\"\\u0000*\\u0000backupId\\\";i:14;}\"},\"createdAt\":1748087197,\"delay\":null}', '0', NULL, '1748087197', '1748087197');
INSERT INTO `jobs` (`id`, `queue`, `payload`, `attempts`, `reserved_at`, `available_at`, `created_at`) VALUES ('16', 'default', '{\"uuid\":\"7c8b98cc-f3bd-49b3-afa1-45272a01e2ee\",\"displayName\":\"App\\\\Jobs\\\\BackupJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":3600,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\BackupJob\",\"command\":\"O:18:\\\"App\\\\Jobs\\\\BackupJob\\\":4:{s:13:\\\"\\u0000*\\u0000backupType\\\";s:8:\\\"database\\\";s:9:\\\"\\u0000*\\u0000userId\\\";i:1;s:8:\\\"\\u0000*\\u0000jobId\\\";s:20:\\\"backup_6831b3a06d61f\\\";s:11:\\\"\\u0000*\\u0000backupId\\\";i:20;}\"},\"createdAt\":1748087712,\"delay\":null}', '0', NULL, '1748087712', '1748087712');


-- --------------------------------------------------------
-- بنية الجدول `job_batches`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `job_batches`;
CREATE TABLE "job_batches" ("id" varchar not null, "name" varchar not null, "total_jobs" integer not null, "pending_jobs" integer not null, "failed_jobs" integer not null, "failed_job_ids" text not null, "options" text, "cancelled_at" integer, "created_at" integer not null, "finished_at" integer, primary key ("id"));


-- --------------------------------------------------------
-- بنية الجدول `roles`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `roles`;
CREATE TABLE "roles" ("id" integer primary key autoincrement not null, "name" varchar not null, "description" varchar, "created_at" datetime, "updated_at" datetime);

-- بيانات الجدول `roles`
INSERT INTO `roles` (`id`, `name`, `description`, `created_at`, `updated_at`) VALUES ('1', 'admin', 'مدير النظام - لديه كافة الصلاحيات', '2025-05-23 09:50:35', '2025-05-23 09:50:35');
INSERT INTO `roles` (`id`, `name`, `description`, `created_at`, `updated_at`) VALUES ('2', 'manager', 'مدير المطعم - لديه صلاحيات إدارة المطعم', '2025-05-23 09:50:35', '2025-05-23 09:50:35');
INSERT INTO `roles` (`id`, `name`, `description`, `created_at`, `updated_at`) VALUES ('4', 'cashier', 'كاشير - لديه صلاحيات إدارة الطلبات والفواتير وواجهة النادل فقط', '2025-05-23 09:50:35', '2025-05-23 09:50:35');
INSERT INTO `roles` (`id`, `name`, `description`, `created_at`, `updated_at`) VALUES ('5', 'kitchen', 'مطبخ - لديه صلاحيات إدارة الطلبات في المطبخ', '2025-05-23 09:50:35', '2025-05-23 09:50:35');


-- --------------------------------------------------------
-- بنية الجدول `tables`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `tables`;
CREATE TABLE "tables" ("id" integer primary key autoincrement not null, "name" varchar not null, "capacity" integer not null, "status" varchar check ("status" in ('available', 'occupied')) not null default 'available', "description" text, "created_at" datetime, "updated_at" datetime);

-- بيانات الجدول `tables`
INSERT INTO `tables` (`id`, `name`, `capacity`, `status`, `description`, `created_at`, `updated_at`) VALUES ('1', 'طاولة 1', '2', 'available', NULL, '2025-05-23 09:50:37', '2025-05-24 11:16:23');
INSERT INTO `tables` (`id`, `name`, `capacity`, `status`, `description`, `created_at`, `updated_at`) VALUES ('2', 'طاولة 2', '4', 'available', NULL, '2025-05-23 09:50:37', '2025-05-23 09:50:37');
INSERT INTO `tables` (`id`, `name`, `capacity`, `status`, `description`, `created_at`, `updated_at`) VALUES ('3', 'طاولة 3', '4', 'available', NULL, '2025-05-23 09:50:37', '2025-05-23 09:50:37');
INSERT INTO `tables` (`id`, `name`, `capacity`, `status`, `description`, `created_at`, `updated_at`) VALUES ('4', 'طاولة 4', '6', 'available', NULL, '2025-05-23 09:50:37', '2025-05-23 09:50:37');
INSERT INTO `tables` (`id`, `name`, `capacity`, `status`, `description`, `created_at`, `updated_at`) VALUES ('5', 'طاولة 5', '8', 'available', NULL, '2025-05-23 09:50:37', '2025-05-23 09:50:37');
INSERT INTO `tables` (`id`, `name`, `capacity`, `status`, `description`, `created_at`, `updated_at`) VALUES ('6', 'طاولة 6', '2', 'available', NULL, '2025-05-23 09:50:37', '2025-05-23 09:50:37');
INSERT INTO `tables` (`id`, `name`, `capacity`, `status`, `description`, `created_at`, `updated_at`) VALUES ('7', 'طاولة 7', '4', 'available', NULL, '2025-05-23 09:50:37', '2025-05-23 09:50:37');
INSERT INTO `tables` (`id`, `name`, `capacity`, `status`, `description`, `created_at`, `updated_at`) VALUES ('8', 'طاولة 8', '6', 'available', NULL, '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `tables` (`id`, `name`, `capacity`, `status`, `description`, `created_at`, `updated_at`) VALUES ('9', 'طاولة VIP 1', '10', 'available', 'طاولة VIP مع خدمة خاصة', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `tables` (`id`, `name`, `capacity`, `status`, `description`, `created_at`, `updated_at`) VALUES ('10', 'طاولة VIP 2', '12', 'available', 'طاولة VIP مع خدمة خاصة', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `tables` (`id`, `name`, `capacity`, `status`, `description`, `created_at`, `updated_at`) VALUES ('11', 'طاولة 9', '4', 'available', NULL, '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `tables` (`id`, `name`, `capacity`, `status`, `description`, `created_at`, `updated_at`) VALUES ('12', 'طاولة 10', '6', 'available', NULL, '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `tables` (`id`, `name`, `capacity`, `status`, `description`, `created_at`, `updated_at`) VALUES ('13', 'طاولة 11', '2', 'available', NULL, '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `tables` (`id`, `name`, `capacity`, `status`, `description`, `created_at`, `updated_at`) VALUES ('14', 'طاولة 12', '8', 'available', NULL, '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `tables` (`id`, `name`, `capacity`, `status`, `description`, `created_at`, `updated_at`) VALUES ('15', 'طاولة 13', '4', 'available', NULL, '2025-05-23 09:50:38', '2025-05-23 09:50:38');


-- --------------------------------------------------------
-- بنية الجدول `categories`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `categories`;
CREATE TABLE "categories" ("id" integer primary key autoincrement not null, "name" varchar not null, "description" text, "image" varchar, "created_at" datetime, "updated_at" datetime);

-- بيانات الجدول `categories`
INSERT INTO `categories` (`id`, `name`, `description`, `image`, `created_at`, `updated_at`) VALUES ('1', 'مقبلات', 'أطباق صغيرة تقدم قبل الوجبة الرئيسية', 'categories/rctBlxL8nD0uZtDCUS77Y8rkwY1oObomcDSxPAfo.png', '2025-05-23 09:50:38', '2025-05-23 12:26:15');
INSERT INTO `categories` (`id`, `name`, `description`, `image`, `created_at`, `updated_at`) VALUES ('2', 'سلطات', 'سلطات طازجة ومتنوعة', NULL, '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `categories` (`id`, `name`, `description`, `image`, `created_at`, `updated_at`) VALUES ('3', 'أطباق رئيسية', 'أطباق رئيسية متنوعة', NULL, '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `categories` (`id`, `name`, `description`, `image`, `created_at`, `updated_at`) VALUES ('4', 'مشاوي', 'تشكيلة من المشاوي الشهية', NULL, '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `categories` (`id`, `name`, `description`, `image`, `created_at`, `updated_at`) VALUES ('5', 'حلويات', 'حلويات شرقية وغربية', NULL, '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `categories` (`id`, `name`, `description`, `image`, `created_at`, `updated_at`) VALUES ('6', 'مشروبات ساخنة', 'مشروبات ساخنة متنوعة', NULL, '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `categories` (`id`, `name`, `description`, `image`, `created_at`, `updated_at`) VALUES ('7', 'مشروبات باردة', 'مشروبات باردة منعشة', NULL, '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `categories` (`id`, `name`, `description`, `image`, `created_at`, `updated_at`) VALUES ('8', 'عصائر طازجة', 'عصائر طبيعية 100%', NULL, '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `categories` (`id`, `name`, `description`, `image`, `created_at`, `updated_at`) VALUES ('9', 'وجبات سريعة', 'وجبات سريعة التحضير', NULL, '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `categories` (`id`, `name`, `description`, `image`, `created_at`, `updated_at`) VALUES ('10', 'أطباق جانبية', 'أطباق تقدم مع الوجبات الرئيسية', NULL, '2025-05-23 09:50:38', '2025-05-23 09:50:38');


-- --------------------------------------------------------
-- بنية الجدول `products`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `products`;
CREATE TABLE "products" ("id" integer primary key autoincrement not null, "name" varchar not null, "description" text, "price" numeric not null, "image" varchar, "category_id" integer not null, "is_available" tinyint(1) not null default '1', "created_at" datetime, "updated_at" datetime, foreign key("category_id") references "categories"("id") on delete cascade);

-- بيانات الجدول `products`
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('1', 'حمص', 'حمص مع زيت زيتون وصنوبر', '25', NULL, '1', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('2', 'متبل', 'متبل باذنجان مع طحينة', '25', NULL, '1', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('3', 'تبولة', 'سلطة البقدونس مع البرغل', '30', NULL, '1', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('4', 'فتوش', 'سلطة الخضار مع الخبز المحمص', '30', NULL, '1', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('5', 'بابا غنوج', 'سلطة الباذنجان المشوي', '25', NULL, '1', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('6', 'سمبوسك', 'عجينة مقلية محشوة باللحم', '35', NULL, '1', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('7', 'كبة', 'كبة مقلية محشوة باللحم والصنوبر', '40', NULL, '1', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('8', 'سلطة خضراء', 'خضروات طازجة مع صلصة الليمون', '35', NULL, '2', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('9', 'سلطة سيزر', 'خس روماني مع صلصة السيزر والدجاج', '45', NULL, '2', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('10', 'سلطة يونانية', 'خضروات مع جبنة الفيتا والزيتون', '40', NULL, '2', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('11', 'سلطة الشمندر', 'شمندر مع جبنة الماعز', '35', NULL, '2', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('12', 'كبسة لحم', 'أرز بخاري مع قطع اللحم', '85', NULL, '3', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('13', 'مندي دجاج', 'أرز مندي مع دجاج مشوي', '75', NULL, '3', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('14', 'برياني', 'أرز برياني مع اللحم والبهارات', '90', NULL, '3', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('15', 'مقلوبة', 'أرز مع الباذنجان والدجاج', '80', NULL, '3', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('16', 'ملوخية', 'ملوخية مع الدجاج والأرز', '70', NULL, '3', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('17', 'شيش طاووق', 'قطع دجاج مشوية متبلة', '65', NULL, '4', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('18', 'كباب', 'لحم مفروم مشوي مع البهارات', '70', NULL, '4', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('19', 'كفتة', 'لحم مفروم مشوي مع البصل والبقدونس', '70', NULL, '4', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('20', 'ريش', 'ريش غنم مشوية', '120', NULL, '4', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('21', 'مشاوي مشكلة', 'تشكيلة من المشاوي المختلفة', '150', NULL, '4', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('22', 'كنافة', 'كنافة بالجبن والقطر', '40', NULL, '5', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('23', 'بقلاوة', 'بقلاوة بالفستق الحلبي', '35', NULL, '5', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('24', 'قطايف', 'قطايف محشوة بالقشطة أو الجوز', '30', NULL, '5', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('25', 'أم علي', 'حلوى مصرية من العجين والحليب والمكسرات', '35', 'products/wMqXrOzYKmvxoo3fbErEposTJWZPVFs4VIWKFvET.jpg', '5', '1', '2025-05-23 09:50:38', '2025-05-23 12:26:31');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('26', 'قهوة عربية', 'قهوة عربية مع الهيل', '15', NULL, '6', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('27', 'شاي', 'شاي مع النعناع', '10', NULL, '6', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('28', 'قهوة تركية', 'قهوة تركية مركزة', '15', NULL, '6', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('29', 'كولا', 'مشروب غازي', '10', NULL, '7', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('30', 'سفن أب', 'مشروب غازي', '10', NULL, '7', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('31', 'مياه معدنية', 'مياه معدنية نقية', '5', NULL, '7', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('32', 'عصير برتقال', 'عصير برتقال طازج', '20', NULL, '8', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('33', 'عصير مانجو', 'عصير مانجو طازج', '25', NULL, '8', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('34', 'عصير فراولة', 'عصير فراولة طازج', '25', NULL, '8', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('35', 'ليموناضة', 'عصير ليمون منعش', '15', NULL, '8', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('36', 'حمص', 'Cupiditate dolor eum asperiores animi quibusdam eum voluptas. Quisquam aliquam quaerat ut commodi odio nobis.', '139.72', NULL, '10', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('37', 'متبل', 'Facere a corrupti corrupti rerum cum. Illo et natus earum maiores odit rerum et.', '148.61', NULL, '7', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('38', 'تبولة', 'Dolores recusandae adipisci velit facere id minus cumque.', '189.27', NULL, '9', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('39', 'فتوش', 'Omnis enim doloribus non velit.', '137.19', NULL, '10', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('40', 'بابا غنوج', 'Sunt itaque aut et. Qui vero beatae et voluptas sunt.', '61.49', NULL, '8', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('41', 'سمبوسك', 'Nam repellat quia consequatur sed qui sed aliquid. Ipsam dolores eum quidem aut.', '117.44', NULL, '9', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('42', 'كبة', 'Ipsa non fugit qui officia commodi. Quia magnam cum at vero.', '21.34', NULL, '3', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('43', 'فلافل', 'Totam error quia aliquam.', '184.35', NULL, '9', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('44', 'سلطة خضراء', 'Repellat ducimus rem eum nulla vero. Aspernatur autem numquam mollitia rerum.', '122.19', NULL, '9', '1', '2025-05-23 09:50:38', '2025-05-23 09:50:38');
INSERT INTO `products` (`id`, `name`, `description`, `price`, `image`, `category_id`, `is_available`, `created_at`, `updated_at`) VALUES ('45', 'سلطة فواكه', 'Hic quis excepturi minima ut dolore porro iusto dolorem. Unde non velit vel nihil optio consequuntur officiis qui.', '62.92', NULL, '5', '0', '2025-05-23 09:50:38', '2025-05-23 09:50:38');


-- --------------------------------------------------------
-- بنية الجدول `orders`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `orders`;
CREATE TABLE "orders" ("id" integer primary key autoincrement not null, "user_id" integer not null, "table_id" integer, "total_amount" numeric not null default '0', "notes" text, "created_at" datetime, "updated_at" datetime, "payment_method" varchar check ("payment_method" in ('cash', 'card')) not null default 'cash', "preparation_status" varchar check ("preparation_status" in ('pending', 'preparing', 'ready', 'delivered', 'completed')), "order_type" varchar check ("order_type" in ('dine_in', 'takeaway')) not null default 'dine_in', "customer_name" varchar, "customer_phone" varchar, "car_number" varchar, "status" varchar check ("status" in ('pending', 'preparing', 'ready', 'delivered', 'completed', 'cancelled', 'suspended', 'in_progress', 'draft')) not null default 'pending', foreign key("user_id") references "users"("id") on delete cascade, foreign key("table_id") references "tables"("id") on delete set null);

-- بيانات الجدول `orders`
INSERT INTO `orders` (`id`, `user_id`, `table_id`, `total_amount`, `notes`, `created_at`, `updated_at`, `payment_method`, `preparation_status`, `order_type`, `customer_name`, `customer_phone`, `car_number`, `status`) VALUES ('21', '1', NULL, '30', NULL, '2025-05-23 10:51:47', '2025-05-24 11:10:36', 'cash', 'delivered', 'takeaway', 'عميل تجريبي', NULL, NULL, 'delivered');
INSERT INTO `orders` (`id`, `user_id`, `table_id`, `total_amount`, `notes`, `created_at`, `updated_at`, `payment_method`, `preparation_status`, `order_type`, `customer_name`, `customer_phone`, `car_number`, `status`) VALUES ('22', '6', NULL, '25', NULL, '2025-05-23 10:52:49', '2025-05-24 11:10:39', 'cash', 'delivered', 'takeaway', '3', NULL, NULL, 'delivered');
INSERT INTO `orders` (`id`, `user_id`, `table_id`, `total_amount`, `notes`, `created_at`, `updated_at`, `payment_method`, `preparation_status`, `order_type`, `customer_name`, `customer_phone`, `car_number`, `status`) VALUES ('23', '6', '1', '57.75', NULL, '2025-05-23 11:00:50', '2025-05-24 11:10:41', 'cash', 'delivered', 'dine_in', NULL, NULL, NULL, 'delivered');
INSERT INTO `orders` (`id`, `user_id`, `table_id`, `total_amount`, `notes`, `created_at`, `updated_at`, `payment_method`, `preparation_status`, `order_type`, `customer_name`, `customer_phone`, `car_number`, `status`) VALUES ('24', '6', NULL, '25', NULL, '2025-05-23 11:05:40', '2025-05-24 11:10:42', 'cash', 'delivered', 'takeaway', '1', NULL, NULL, 'delivered');
INSERT INTO `orders` (`id`, `user_id`, `table_id`, `total_amount`, `notes`, `created_at`, `updated_at`, `payment_method`, `preparation_status`, `order_type`, `customer_name`, `customer_phone`, `car_number`, `status`) VALUES ('25', '6', '1', '25', NULL, '2025-05-23 11:20:13', '2025-05-24 11:10:44', 'cash', 'delivered', 'dine_in', '1', NULL, NULL, 'delivered');
INSERT INTO `orders` (`id`, `user_id`, `table_id`, `total_amount`, `notes`, `created_at`, `updated_at`, `payment_method`, `preparation_status`, `order_type`, `customer_name`, `customer_phone`, `car_number`, `status`) VALUES ('26', '6', NULL, '25', NULL, '2025-05-23 12:36:16', '2025-05-24 11:10:46', 'card', 'delivered', 'takeaway', NULL, NULL, NULL, 'delivered');
INSERT INTO `orders` (`id`, `user_id`, `table_id`, `total_amount`, `notes`, `created_at`, `updated_at`, `payment_method`, `preparation_status`, `order_type`, `customer_name`, `customer_phone`, `car_number`, `status`) VALUES ('27', '1', NULL, '50', 'مسودة طلب', '2025-05-24 11:09:15', '2025-05-24 11:11:14', 'cash', 'delivered', 'takeaway', 'مسودة - ٢٦‏/١١‏/١٤٤٦ هـ ٣:٠٩:١٥ م', NULL, NULL, 'delivered');
INSERT INTO `orders` (`id`, `user_id`, `table_id`, `total_amount`, `notes`, `created_at`, `updated_at`, `payment_method`, `preparation_status`, `order_type`, `customer_name`, `customer_phone`, `car_number`, `status`) VALUES ('28', '6', NULL, '25', 'مسودة طلب', '2025-05-24 11:11:44', '2025-05-24 11:13:13', 'cash', 'delivered', 'takeaway', 'مسودة - ٢٦‏/١١‏/١٤٤٦ هـ ٣:١١:٤٤ م', NULL, NULL, 'delivered');
INSERT INTO `orders` (`id`, `user_id`, `table_id`, `total_amount`, `notes`, `created_at`, `updated_at`, `payment_method`, `preparation_status`, `order_type`, `customer_name`, `customer_phone`, `car_number`, `status`) VALUES ('29', '1', '1', '25', 'مسودة طلب | رقم الطاولة: 1', '2025-05-24 11:13:27', '2025-05-24 11:16:23', 'cash', NULL, 'dine_in', 'مسودة - ٢٦‏/١١‏/١٤٤٦ هـ ٣:١٣:٢٧ م', NULL, NULL, 'completed');
INSERT INTO `orders` (`id`, `user_id`, `table_id`, `total_amount`, `notes`, `created_at`, `updated_at`, `payment_method`, `preparation_status`, `order_type`, `customer_name`, `customer_phone`, `car_number`, `status`) VALUES ('30', '1', NULL, '25', 'مسودة طلب', '2025-05-24 11:16:30', '2025-05-24 11:20:53', 'cash', 'delivered', 'takeaway', 'مسودة - ٢٦‏/١١‏/١٤٤٦ هـ ٣:١٦:٣٠ م', NULL, NULL, 'delivered');
INSERT INTO `orders` (`id`, `user_id`, `table_id`, `total_amount`, `notes`, `created_at`, `updated_at`, `payment_method`, `preparation_status`, `order_type`, `customer_name`, `customer_phone`, `car_number`, `status`) VALUES ('31', '1', NULL, '25', 'مسودة طلب', '2025-05-24 11:19:01', '2025-05-24 11:20:55', 'cash', 'delivered', 'takeaway', 'مسودة - ٢٦‏/١١‏/١٤٤٦ هـ ٣:١٩:٠٠ م', NULL, NULL, 'delivered');


-- --------------------------------------------------------
-- بنية الجدول `order_items`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `order_items`;
CREATE TABLE "order_items" ("id" integer primary key autoincrement not null, "order_id" integer not null, "product_id" integer not null, "quantity" integer not null, "unit_price" numeric not null, "subtotal" numeric not null, "notes" text, "created_at" datetime, "updated_at" datetime, foreign key("order_id") references "orders"("id") on delete cascade, foreign key("product_id") references "products"("id") on delete cascade);

-- بيانات الجدول `order_items`
INSERT INTO `order_items` (`id`, `order_id`, `product_id`, `quantity`, `unit_price`, `subtotal`, `notes`, `created_at`, `updated_at`) VALUES ('75', '22', '1', '1', '25', '25', NULL, '2025-05-23 10:52:49', '2025-05-23 10:52:49');
INSERT INTO `order_items` (`id`, `order_id`, `product_id`, `quantity`, `unit_price`, `subtotal`, `notes`, `created_at`, `updated_at`) VALUES ('76', '21', '3', '1', '30', '30', NULL, '2025-05-23 10:54:23', '2025-05-23 10:54:23');
INSERT INTO `order_items` (`id`, `order_id`, `product_id`, `quantity`, `unit_price`, `subtotal`, `notes`, `created_at`, `updated_at`) VALUES ('77', '23', '2', '1', '25', '25', NULL, '2025-05-23 11:00:50', '2025-05-23 11:00:50');
INSERT INTO `order_items` (`id`, `order_id`, `product_id`, `quantity`, `unit_price`, `subtotal`, `notes`, `created_at`, `updated_at`) VALUES ('78', '23', '3', '1', '30', '30', NULL, '2025-05-23 11:00:50', '2025-05-23 11:00:50');
INSERT INTO `order_items` (`id`, `order_id`, `product_id`, `quantity`, `unit_price`, `subtotal`, `notes`, `created_at`, `updated_at`) VALUES ('79', '24', '1', '1', '25', '25', NULL, '2025-05-23 11:05:40', '2025-05-23 11:05:40');
INSERT INTO `order_items` (`id`, `order_id`, `product_id`, `quantity`, `unit_price`, `subtotal`, `notes`, `created_at`, `updated_at`) VALUES ('80', '25', '1', '1', '25', '25', NULL, '2025-05-23 11:20:13', '2025-05-23 11:20:13');
INSERT INTO `order_items` (`id`, `order_id`, `product_id`, `quantity`, `unit_price`, `subtotal`, `notes`, `created_at`, `updated_at`) VALUES ('81', '26', '1', '1', '25', '25', NULL, '2025-05-23 12:36:16', '2025-05-23 12:36:16');
INSERT INTO `order_items` (`id`, `order_id`, `product_id`, `quantity`, `unit_price`, `subtotal`, `notes`, `created_at`, `updated_at`) VALUES ('82', '27', '1', '2', '25', '50', NULL, '2025-05-24 11:09:15', '2025-05-24 11:09:34');
INSERT INTO `order_items` (`id`, `order_id`, `product_id`, `quantity`, `unit_price`, `subtotal`, `notes`, `created_at`, `updated_at`) VALUES ('83', '28', '1', '1', '25', '25', NULL, '2025-05-24 11:11:44', '2025-05-24 11:11:44');
INSERT INTO `order_items` (`id`, `order_id`, `product_id`, `quantity`, `unit_price`, `subtotal`, `notes`, `created_at`, `updated_at`) VALUES ('84', '29', '1', '1', '25', '25', NULL, '2025-05-24 11:13:27', '2025-05-24 11:13:27');
INSERT INTO `order_items` (`id`, `order_id`, `product_id`, `quantity`, `unit_price`, `subtotal`, `notes`, `created_at`, `updated_at`) VALUES ('85', '30', '1', '1', '25', '25', NULL, '2025-05-24 11:16:30', '2025-05-24 11:16:30');
INSERT INTO `order_items` (`id`, `order_id`, `product_id`, `quantity`, `unit_price`, `subtotal`, `notes`, `created_at`, `updated_at`) VALUES ('86', '31', '1', '1', '25', '25', NULL, '2025-05-24 11:19:01', '2025-05-24 11:19:01');


-- --------------------------------------------------------
-- بنية الجدول `invoices`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `invoices`;
CREATE TABLE "invoices" ("id" integer primary key autoincrement not null, "order_id" integer not null, "user_id" integer not null, "invoice_number" varchar not null, "total_amount" numeric not null, "tax_amount" numeric not null default '0', "discount_amount" numeric not null default '0', "final_amount" numeric not null, "payment_method" varchar check ("payment_method" in ('cash', 'card', 'other')) not null default 'cash', "payment_status" varchar check ("payment_status" in ('pending', 'paid', 'cancelled')) not null default 'pending', "notes" text, "created_at" datetime, "updated_at" datetime, "paid_amount" numeric not null default '0', "remaining_amount" numeric not null default '0', foreign key("order_id") references "orders"("id") on delete cascade, foreign key("user_id") references "users"("id") on delete cascade);

-- بيانات الجدول `invoices`
INSERT INTO `invoices` (`id`, `order_id`, `user_id`, `invoice_number`, `total_amount`, `tax_amount`, `discount_amount`, `final_amount`, `payment_method`, `payment_status`, `notes`, `created_at`, `updated_at`, `paid_amount`, `remaining_amount`) VALUES ('7', '22', '6', 'INV-20250523-3006', '25', '1.25', '0', '26.25', 'cash', 'paid', NULL, '2025-05-23 10:54:04', '2025-05-23 10:54:04', '26.25', '0');
INSERT INTO `invoices` (`id`, `order_id`, `user_id`, `invoice_number`, `total_amount`, `tax_amount`, `discount_amount`, `final_amount`, `payment_method`, `payment_status`, `notes`, `created_at`, `updated_at`, `paid_amount`, `remaining_amount`) VALUES ('8', '21', '6', 'INV-20250523-3450', '30', '1.5', '0', '31.5', 'cash', 'paid', NULL, '2025-05-23 10:54:28', '2025-05-23 10:54:28', '31.5', '0');
INSERT INTO `invoices` (`id`, `order_id`, `user_id`, `invoice_number`, `total_amount`, `tax_amount`, `discount_amount`, `final_amount`, `payment_method`, `payment_status`, `notes`, `created_at`, `updated_at`, `paid_amount`, `remaining_amount`) VALUES ('9', '24', '1', 'INV-20250523-9187', '25', '1.25', '0', '26.25', 'cash', 'paid', NULL, '2025-05-23 11:11:12', '2025-05-23 11:11:12', '26.25', '0');
INSERT INTO `invoices` (`id`, `order_id`, `user_id`, `invoice_number`, `total_amount`, `tax_amount`, `discount_amount`, `final_amount`, `payment_method`, `payment_status`, `notes`, `created_at`, `updated_at`, `paid_amount`, `remaining_amount`) VALUES ('11', '26', '6', 'INV-20250523-3389', '25', '1.25', '0', '26.25', 'card', 'paid', NULL, '2025-05-23 12:36:16', '2025-05-23 12:36:16', '26.25', '0');
INSERT INTO `invoices` (`id`, `order_id`, `user_id`, `invoice_number`, `total_amount`, `tax_amount`, `discount_amount`, `final_amount`, `payment_method`, `payment_status`, `notes`, `created_at`, `updated_at`, `paid_amount`, `remaining_amount`) VALUES ('12', '29', '1', 'INV-20250524-1806', '25', '1.25', '0', '26.25', 'cash', 'paid', 'مسودة طلب | رقم الطاولة: 1', '2025-05-24 11:16:08', '2025-05-24 11:16:08', '26.25', '0');
INSERT INTO `invoices` (`id`, `order_id`, `user_id`, `invoice_number`, `total_amount`, `tax_amount`, `discount_amount`, `final_amount`, `payment_method`, `payment_status`, `notes`, `created_at`, `updated_at`, `paid_amount`, `remaining_amount`) VALUES ('13', '29', '1', 'INV-20250524-9410', '25', '1.25', '0', '26.25', 'cash', 'paid', 'مسودة طلب | رقم الطاولة: 1', '2025-05-24 11:16:23', '2025-05-24 11:16:23', '26.25', '0');
INSERT INTO `invoices` (`id`, `order_id`, `user_id`, `invoice_number`, `total_amount`, `tax_amount`, `discount_amount`, `final_amount`, `payment_method`, `payment_status`, `notes`, `created_at`, `updated_at`, `paid_amount`, `remaining_amount`) VALUES ('14', '30', '1', 'INV-20250524-4497', '25', '1.25', '0', '26.25', 'cash', 'paid', 'مسودة طلب', '2025-05-24 11:16:46', '2025-05-24 11:16:46', '26.25', '0');
INSERT INTO `invoices` (`id`, `order_id`, `user_id`, `invoice_number`, `total_amount`, `tax_amount`, `discount_amount`, `final_amount`, `payment_method`, `payment_status`, `notes`, `created_at`, `updated_at`, `paid_amount`, `remaining_amount`) VALUES ('15', '31', '1', 'INV-20250524-3009', '25', '1.25', '0', '26.25', 'cash', 'paid', 'مسودة طلب', '2025-05-24 11:19:09', '2025-05-24 11:19:09', '26.25', '0');


-- --------------------------------------------------------
-- بنية الجدول `notifications`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `notifications`;
CREATE TABLE "notifications" ("id" integer primary key autoincrement not null, "user_id" integer not null, "title" varchar not null, "message" text not null, "type" varchar, "data" text, "read" tinyint(1) not null default '0', "created_at" datetime, "updated_at" datetime, foreign key("user_id") references "users"("id") on delete cascade);

-- بيانات الجدول `notifications`
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('30', '1', 'طلب جاهز للتسليم', 'الطلب رقم 21 جاهز للتسليم', 'order_ready', '\"{\\\"order_id\\\":21}\"', '1', '2025-05-23 10:53:18', '2025-05-23 11:06:08');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('32', '6', 'طلب جاهز للتسليم', 'الطلب رقم 22 جاهز للتسليم', 'order_ready', '\"{\\\"order_id\\\":22}\"', '1', '2025-05-23 10:53:20', '2025-05-23 11:05:17');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('54', '1', 'تم تسليم الطلب', 'تم تسليم الطلب رقم 21', 'order_delivered', '\"{\\\"order_id\\\":21}\"', '1', '2025-05-23 10:53:45', '2025-05-23 11:06:07');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('55', '6', 'تم تسليم الطلب', 'تم تسليم الطلب رقم 22', 'order_delivered', '\"{\\\"order_id\\\":22}\"', '1', '2025-05-23 10:53:46', '2025-05-23 11:05:17');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('56', '6', 'طلب جاهز للتسليم', 'الطلب رقم 23 جاهز للتسليم', 'order_ready', '\"{\\\"order_id\\\":23}\"', '1', '2025-05-23 11:01:09', '2025-05-23 11:05:16');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('57', '6', 'تم تسليم الطلب', 'تم تسليم الطلب رقم 23', 'order_delivered', '\"{\\\"order_id\\\":23}\"', '1', '2025-05-23 11:01:11', '2025-05-23 11:05:15');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('58', '6', 'طلب جاهز للتسليم', 'الطلب رقم 24 جاهز للتسليم', 'order_ready', '\"{\\\"order_id\\\":24}\"', '1', '2025-05-23 11:06:20', '2025-05-23 11:06:39');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('59', '6', 'تم تسليم الطلب', 'تم تسليم الطلب رقم 24', 'order_delivered', '\"{\\\"order_id\\\":24}\"', '1', '2025-05-23 11:06:22', '2025-05-23 11:06:38');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('60', '6', 'طلب جاهز للتسليم', 'الطلب رقم 25 جاهز للتسليم', 'order_ready', '\"{\\\"order_id\\\":25}\"', '1', '2025-05-23 11:20:21', '2025-05-23 12:36:35');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('61', '6', 'تم تسليم الطلب', 'تم تسليم الطلب رقم 25', 'order_delivered', '\"{\\\"order_id\\\":25}\"', '1', '2025-05-23 11:20:22', '2025-05-23 12:36:35');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('62', '6', 'طلب جاهز للتسليم', 'الطلب رقم 26 جاهز للتسليم', 'order_ready', '\"{\\\"order_id\\\":26}\"', '1', '2025-05-23 12:36:27', '2025-05-23 12:36:35');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('63', '6', 'تم تسليم الطلب', 'تم تسليم الطلب رقم 26', 'order_delivered', '\"{\\\"order_id\\\":26}\"', '1', '2025-05-23 12:36:28', '2025-05-23 12:36:35');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('64', '1', 'طلب جاهز للتسليم', 'الطلب رقم 21 جاهز للتسليم', 'order_ready', '\"{\\\"order_id\\\":21}\"', '1', '2025-05-24 11:10:28', '2025-05-24 11:11:20');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('65', '6', 'طلب جاهز للتسليم', 'الطلب رقم 23 جاهز للتسليم', 'order_ready', '\"{\\\"order_id\\\":23}\"', '1', '2025-05-24 11:10:30', '2025-05-24 11:11:27');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('66', '6', 'طلب جاهز للتسليم', 'الطلب رقم 22 جاهز للتسليم', 'order_ready', '\"{\\\"order_id\\\":22}\"', '1', '2025-05-24 11:10:30', '2025-05-24 11:11:27');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('67', '6', 'طلب جاهز للتسليم', 'الطلب رقم 25 جاهز للتسليم', 'order_ready', '\"{\\\"order_id\\\":25}\"', '1', '2025-05-24 11:10:31', '2025-05-24 11:11:27');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('68', '6', 'طلب جاهز للتسليم', 'الطلب رقم 24 جاهز للتسليم', 'order_ready', '\"{\\\"order_id\\\":24}\"', '1', '2025-05-24 11:10:32', '2025-05-24 11:11:27');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('69', '6', 'طلب جاهز للتسليم', 'الطلب رقم 26 جاهز للتسليم', 'order_ready', '\"{\\\"order_id\\\":26}\"', '1', '2025-05-24 11:10:34', '2025-05-24 11:11:27');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('70', '1', 'تم تسليم الطلب', 'تم تسليم الطلب رقم 21', 'order_delivered', '\"{\\\"order_id\\\":21}\"', '1', '2025-05-24 11:10:36', '2025-05-24 11:11:20');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('71', '6', 'تم تسليم الطلب', 'تم تسليم الطلب رقم 22', 'order_delivered', '\"{\\\"order_id\\\":22}\"', '1', '2025-05-24 11:10:39', '2025-05-24 11:11:27');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('72', '6', 'تم تسليم الطلب', 'تم تسليم الطلب رقم 23', 'order_delivered', '\"{\\\"order_id\\\":23}\"', '1', '2025-05-24 11:10:41', '2025-05-24 11:11:27');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('73', '6', 'تم تسليم الطلب', 'تم تسليم الطلب رقم 24', 'order_delivered', '\"{\\\"order_id\\\":24}\"', '1', '2025-05-24 11:10:42', '2025-05-24 11:11:27');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('74', '6', 'تم تسليم الطلب', 'تم تسليم الطلب رقم 25', 'order_delivered', '\"{\\\"order_id\\\":25}\"', '1', '2025-05-24 11:10:44', '2025-05-24 11:11:27');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('75', '6', 'تم تسليم الطلب', 'تم تسليم الطلب رقم 26', 'order_delivered', '\"{\\\"order_id\\\":26}\"', '1', '2025-05-24 11:10:46', '2025-05-24 11:11:27');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('76', '1', 'طلب جاهز للتسليم', 'الطلب رقم 27 جاهز للتسليم', 'order_ready', '\"{\\\"order_id\\\":27}\"', '1', '2025-05-24 11:11:12', '2025-05-24 11:11:20');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('77', '1', 'تم تسليم الطلب', 'تم تسليم الطلب رقم 27', 'order_delivered', '\"{\\\"order_id\\\":27}\"', '1', '2025-05-24 11:11:14', '2025-05-24 11:11:20');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('78', '6', 'طلب جاهز للتسليم', 'الطلب رقم 28 جاهز للتسليم', 'order_ready', '\"{\\\"order_id\\\":28}\"', '1', '2025-05-24 11:13:12', '2025-05-24 11:21:20');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('79', '6', 'تم تسليم الطلب', 'تم تسليم الطلب رقم 28', 'order_delivered', '\"{\\\"order_id\\\":28}\"', '1', '2025-05-24 11:13:13', '2025-05-24 11:21:20');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('80', '1', 'طلب جاهز للتسليم', 'الطلب رقم 30 جاهز للتسليم', 'order_ready', '\"{\\\"order_id\\\":30}\"', '1', '2025-05-24 11:16:41', '2025-05-24 11:33:05');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('81', '1', 'تم تسليم الطلب', 'تم تسليم الطلب رقم 30', 'order_delivered', '\"{\\\"order_id\\\":30}\"', '1', '2025-05-24 11:16:43', '2025-05-24 11:33:05');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('82', '1', 'طلب جاهز للتسليم', 'الطلب رقم 30 جاهز للتسليم', 'order_ready', '\"{\\\"order_id\\\":30}\"', '1', '2025-05-24 11:20:51', '2025-05-24 11:33:05');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('83', '1', 'طلب جاهز للتسليم', 'الطلب رقم 31 جاهز للتسليم', 'order_ready', '\"{\\\"order_id\\\":31}\"', '1', '2025-05-24 11:20:53', '2025-05-24 11:33:05');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('84', '1', 'تم تسليم الطلب', 'تم تسليم الطلب رقم 30', 'order_delivered', '\"{\\\"order_id\\\":30}\"', '1', '2025-05-24 11:20:53', '2025-05-24 11:33:05');
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `data`, `read`, `created_at`, `updated_at`) VALUES ('85', '1', 'تم تسليم الطلب', 'تم تسليم الطلب رقم 31', 'order_delivered', '\"{\\\"order_id\\\":31}\"', '1', '2025-05-24 11:20:55', '2025-05-24 11:33:05');


-- --------------------------------------------------------
-- بنية الجدول `settings`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `settings`;
CREATE TABLE "settings" ("id" integer primary key autoincrement not null, "key" varchar not null, "value" text, "type" varchar not null default 'text', "group" varchar not null default 'general', "label" varchar not null, "description" text, "is_public" tinyint(1) not null default '0', "created_at" datetime, "updated_at" datetime);

-- بيانات الجدول `settings`
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `group`, `label`, `description`, `is_public`, `created_at`, `updated_at`) VALUES ('1', 'restaurant_name', 'مطعم الوجبات اللذيذة', 'text', 'restaurant', 'اسم المطعم', 'اسم المطعم الذي سيظهر في الفواتير والتقارير', '1', '2025-05-23 10:34:18', '2025-05-23 10:40:31');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `group`, `label`, `description`, `is_public`, `created_at`, `updated_at`) VALUES ('2', 'restaurant_address', 'مسقط، سلطنة عمان', 'text', 'restaurant', 'عنوان المطعم', 'العنوان الكامل للمطعم', '1', '2025-05-23 10:34:18', '2025-05-23 10:40:58');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `group`, `label`, `description`, `is_public`, `created_at`, `updated_at`) VALUES ('3', 'restaurant_phone', '+96899553103', 'text', 'restaurant', 'رقم الهاتف', 'رقم هاتف المطعم', '1', '2025-05-23 10:34:18', '2025-05-23 10:41:09');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `group`, `label`, `description`, `is_public`, `created_at`, `updated_at`) VALUES ('4', 'restaurant_email', '<EMAIL>', 'text', 'restaurant', 'البريد الإلكتروني', 'البريد الإلكتروني للمطعم', '1', '2025-05-23 10:34:18', '2025-05-23 10:39:38');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `group`, `label`, `description`, `is_public`, `created_at`, `updated_at`) VALUES ('5', 'restaurant_website', 'http://localhost:8000/settings/edit', 'text', 'restaurant', 'الموقع الإلكتروني', 'موقع المطعم الإلكتروني', '1', '2025-05-23 10:34:18', '2025-05-23 10:39:38');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `group`, `label`, `description`, `is_public`, `created_at`, `updated_at`) VALUES ('6', 'currency', 'ر.ع', 'text', 'financial', 'رمز العملة', 'رمز العملة المستخدم في النظام', '1', '2025-05-23 10:34:18', '2025-05-23 10:39:38');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `group`, `label`, `description`, `is_public`, `created_at`, `updated_at`) VALUES ('7', 'currency_code', 'OMR', 'text', 'financial', 'كود العملة', 'الكود الدولي للعملة', '0', '2025-05-23 10:34:18', '2025-05-23 10:39:38');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `group`, `label`, `description`, `is_public`, `created_at`, `updated_at`) VALUES ('8', 'tax_rate', '5', 'number', 'financial', 'معدل الضريبة (%)', 'معدل ضريبة القيمة المضافة', '0', '2025-05-23 10:34:18', '2025-05-23 10:39:38');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `group`, `label`, `description`, `is_public`, `created_at`, `updated_at`) VALUES ('9', 'tax_number', '*********', 'text', 'financial', 'الرقم الضريبي', 'الرقم الضريبي للمطعم', '1', '2025-05-23 10:34:18', '2025-05-23 10:39:38');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `group`, `label`, `description`, `is_public`, `created_at`, `updated_at`) VALUES ('10', 'default_language', 'ar', 'text', 'system', 'اللغة الافتراضية', 'اللغة الافتراضية للنظام', '0', '2025-05-23 10:34:18', '2025-05-23 10:34:18');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `group`, `label`, `description`, `is_public`, `created_at`, `updated_at`) VALUES ('11', 'timezone', 'Asia/Riyadh', 'text', 'system', 'المنطقة الزمنية', 'المنطقة الزمنية للمطعم', '0', '2025-05-23 10:34:18', '2025-05-23 10:34:18');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `group`, `label`, `description`, `is_public`, `created_at`, `updated_at`) VALUES ('12', 'date_format', 'Y-m-d', 'text', 'system', 'تنسيق التاريخ', 'تنسيق عرض التاريخ في النظام', '0', '2025-05-23 10:34:18', '2025-05-23 10:34:18');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `group`, `label`, `description`, `is_public`, `created_at`, `updated_at`) VALUES ('13', 'time_format', 'H:i', 'text', 'system', 'تنسيق الوقت', 'تنسيق عرض الوقت في النظام', '0', '2025-05-23 10:34:18', '2025-05-23 10:34:18');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `group`, `label`, `description`, `is_public`, `created_at`, `updated_at`) VALUES ('14', 'default_print_size', '85mm', 'text', 'printing', 'حجم الطباعة الافتراضي', 'حجم الطباعة الافتراضي للفواتير', '0', '2025-05-23 10:34:18', '2025-05-23 10:39:38');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `group`, `label`, `description`, `is_public`, `created_at`, `updated_at`) VALUES ('15', 'print_logo', '1', 'boolean', 'printing', 'طباعة الشعار', 'هل تريد طباعة شعار المطعم في الفواتير؟', '0', '2025-05-23 10:34:19', '2025-05-23 10:34:19');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `group`, `label`, `description`, `is_public`, `created_at`, `updated_at`) VALUES ('16', 'auto_print_kitchen_order', '1', 'boolean', 'printing', 'طباعة طلب المطبخ تلقائياً', 'طباعة طلب المطبخ تلقائياً عند إنشاء الطلب', '0', '2025-05-23 10:34:19', '2025-05-23 12:35:44');
INSERT INTO `settings` (`id`, `key`, `value`, `type`, `group`, `label`, `description`, `is_public`, `created_at`, `updated_at`) VALUES ('17', 'restaurant_logo', 'logos/27IlPuVnM0uQFB54rhCgGAIsJz2uxumDZeRKsN8C.png', 'image', 'restaurant', 'شعار المطعم', 'شعار المطعم الذي سيظهر في الفواتير والتقارير', '0', '2025-05-23 11:11:57', '2025-05-23 11:21:45');


-- --------------------------------------------------------
-- بنية الجدول `expenses`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `expenses`;
CREATE TABLE "expenses" ("id" integer primary key autoincrement not null, "title" varchar not null, "description" text, "amount" numeric not null, "category" varchar not null, "expense_date" date not null, "payment_method" varchar not null default 'cash', "receipt_number" varchar, "vendor" varchar, "status" varchar not null default 'paid', "user_id" integer not null, "notes" text, "created_at" datetime, "updated_at" datetime, foreign key("user_id") references "users"("id") on delete cascade);


-- --------------------------------------------------------
-- بنية الجدول `purchases`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `purchases`;
CREATE TABLE "purchases" ("id" integer primary key autoincrement not null, "purchase_number" varchar not null, "supplier_name" varchar not null, "supplier_phone" varchar, "supplier_address" text, "purchase_date" date not null, "total_amount" numeric not null, "paid_amount" numeric not null default '0', "remaining_amount" numeric not null default '0', "payment_method" varchar not null default 'cash', "payment_status" varchar not null default 'pending', "delivery_status" varchar not null default 'pending', "delivery_date" date, "invoice_number" varchar, "user_id" integer not null, "notes" text, "created_at" datetime, "updated_at" datetime, foreign key("user_id") references "users"("id") on delete cascade);

-- بيانات الجدول `purchases`
INSERT INTO `purchases` (`id`, `purchase_number`, `supplier_name`, `supplier_phone`, `supplier_address`, `purchase_date`, `total_amount`, `paid_amount`, `remaining_amount`, `payment_method`, `payment_status`, `delivery_status`, `delivery_date`, `invoice_number`, `user_id`, `notes`, `created_at`, `updated_at`) VALUES ('1', 'PU000001', 'تجربة', '12345678', NULL, '2025-05-23 00:00:00', '15', '15', '0', 'cash', 'paid', 'delivered', NULL, '76846', '1', NULL, '2025-05-23 10:47:40', '2025-05-23 10:47:40');


-- --------------------------------------------------------
-- بنية الجدول `purchase_items`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `purchase_items`;
CREATE TABLE "purchase_items" ("id" integer primary key autoincrement not null, "purchase_id" integer not null, "item_name" varchar not null, "item_description" text, "unit" varchar not null, "quantity" numeric not null, "unit_price" numeric not null, "total_price" numeric not null, "expiry_date" date, "category" varchar, "created_at" datetime, "updated_at" datetime, foreign key("purchase_id") references "purchases"("id") on delete cascade);

-- بيانات الجدول `purchase_items`
INSERT INTO `purchase_items` (`id`, `purchase_id`, `item_name`, `item_description`, `unit`, `quantity`, `unit_price`, `total_price`, `expiry_date`, `category`, `created_at`, `updated_at`) VALUES ('1', '1', 'اغراض', NULL, 'bag', '2', '7.5', '15', '2025-05-23 00:00:00', 'grains', '2025-05-23 10:47:40', '2025-05-23 10:47:40');


-- --------------------------------------------------------
-- بنية الجدول `licenses`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `licenses`;
CREATE TABLE "licenses" ("id" integer primary key autoincrement not null, "created_at" datetime, "updated_at" datetime);


-- --------------------------------------------------------
-- بنية الجدول `installations`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `installations`;
CREATE TABLE "installations" ("id" integer primary key autoincrement not null, "created_at" datetime, "updated_at" datetime);


-- --------------------------------------------------------
-- بنية الجدول `backups`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `backups`;
CREATE TABLE "backups" ("id" integer primary key autoincrement not null, "filename" varchar not null, "type" varchar not null, "status" varchar not null default 'pending', "file_size" integer, "description" text, "metadata" text, "created_by" integer, "started_at" datetime, "completed_at" datetime, "error_message" text, "is_scheduled" tinyint(1) not null default '0', "is_encrypted" tinyint(1) not null default '0', "created_at" datetime, "updated_at" datetime, foreign key("created_by") references "users"("id") on delete set null);

-- بيانات الجدول `backups`
INSERT INTO `backups` (`id`, `filename`, `type`, `status`, `file_size`, `description`, `metadata`, `created_by`, `started_at`, `completed_at`, `error_message`, `is_scheduled`, `is_encrypted`, `created_at`, `updated_at`) VALUES ('7', 'test_database_2025-05-23_12-19-07.sql', 'database', 'completed', '201', 'نسخة احتياطية تجريبية للاختبار', NULL, '1', '2025-05-23 12:19:07', '2025-05-23 12:19:07', NULL, '0', '0', '2025-05-23 12:19:07', '2025-05-23 12:19:07');
INSERT INTO `backups` (`id`, `filename`, `type`, `status`, `file_size`, `description`, `metadata`, `created_by`, `started_at`, `completed_at`, `error_message`, `is_scheduled`, `is_encrypted`, `created_at`, `updated_at`) VALUES ('19', 'database_backup_simple_2025-05-24_11-50-24.sql', 'database', 'completed', '80154', NULL, NULL, '1', '2025-05-24 11:50:24', '2025-05-24 11:50:24', NULL, '0', '0', '2025-05-24 11:50:24', '2025-05-24 11:50:24');
INSERT INTO `backups` (`id`, `filename`, `type`, `status`, `file_size`, `description`, `metadata`, `created_by`, `started_at`, `completed_at`, `error_message`, `is_scheduled`, `is_encrypted`, `created_at`, `updated_at`) VALUES ('23', 'manual_full_2025-05-24_11-56-38.zip', 'full', 'running', NULL, NULL, NULL, '1', '2025-05-24 11:56:38', NULL, NULL, '0', '0', '2025-05-24 11:56:38', '2025-05-24 11:56:38');

