@extends('layouts.app')

@section('title', 'عرض المنتج')

@section('content')
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>عرض المنتج: {{ $product->name }}</h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('products.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
            </a>
            <a href="{{ route('products.edit', $product->id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card shadow-sm mb-4">
                <div class="card-body text-center">
                    @if($product->image)
                        <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}" class="img-fluid rounded mb-3" style="max-height: 250px;">
                    @else
                        <div class="bg-light p-5 rounded mb-3">
                            <i class="fas fa-image fa-4x text-muted"></i>
                            <p class="mt-2 text-muted">لا توجد صورة</p>
                        </div>
                    @endif

                    <h3 class="card-title">{{ $product->name }}</h3>
                    <p class="card-text fs-4 fw-bold text-primary">{{ number_format($product->price, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</p>

                    <div class="d-grid gap-2">
                        <form action="{{ route('products.toggle-availability', $product->id) }}" method="POST">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="btn {{ $product->is_available ? 'btn-success' : 'btn-danger' }} w-100">
                                <i class="fas {{ $product->is_available ? 'fa-check-circle' : 'fa-times-circle' }} me-1"></i>
                                {{ $product->is_available ? 'متاح للطلب' : 'غير متاح للطلب' }}
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">معلومات المنتج</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 30%">الاسم</th>
                            <td>{{ $product->name }}</td>
                        </tr>
                        <tr>
                            <th>التصنيف</th>
                            <td>
                                @if($product->category)
                                    <a href="{{ route('categories.show', $product->category->id) }}">{{ $product->category->name }}</a>
                                @else
                                    غير مصنف
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>السعر</th>
                            <td>{{ number_format($product->price, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                        </tr>
                        <tr>
                            <th>الحالة</th>
                            <td>
                                <span class="badge {{ $product->is_available ? 'bg-success' : 'bg-danger' }}">
                                    {{ $product->is_available ? 'متاح للطلب' : 'غير متاح للطلب' }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <th>الوصف</th>
                            <td>{{ $product->description ?? 'لا يوجد وصف' }}</td>
                        </tr>
                        <tr>
                            <th>تاريخ الإنشاء</th>
                            <td>{{ $product->created_at->format('Y-m-d H:i') }}</td>
                        </tr>
                        <tr>
                            <th>آخر تحديث</th>
                            <td>{{ $product->updated_at->format('Y-m-d H:i') }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">إحصائيات الطلبات</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <div class="card border-primary mb-3">
                                <div class="card-body">
                                    <h5 class="card-title">عدد مرات الطلب</h5>
                                    <p class="card-text display-6">{{ $product->orderItems->count() }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="card border-success mb-3">
                                <div class="card-body">
                                    <h5 class="card-title">إجمالي الكمية</h5>
                                    <p class="card-text display-6">{{ $product->orderItems->sum('quantity') }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="card border-info mb-3">
                                <div class="card-body">
                                    <h5 class="card-title">إجمالي المبيعات</h5>
                                    <p class="card-text display-6">{{ number_format($product->orderItems->sum('subtotal'), 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
