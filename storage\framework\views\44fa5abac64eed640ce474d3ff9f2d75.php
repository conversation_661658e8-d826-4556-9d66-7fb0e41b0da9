<?php $__env->startSection('title', 'عرض الطلب'); ?>

<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>عرض الطلب رقم: <?php echo e($order->id); ?></h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?php echo e(route('orders.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
            </a>
            <?php if(!in_array($order->status, ['completed', 'cancelled'])): ?>
            <a href="<?php echo e(route('orders.edit', $order->id)); ?>" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
            <?php endif; ?>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">معلومات الطلب</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 40%">رقم الطلب</th>
                            <td><?php echo e($order->id); ?></td>
                        </tr>
                        <tr>
                            <th>الطاولة</th>
                            <td>
                                <?php if($order->table): ?>
                                    <a href="<?php echo e(route('tables.show', $order->table->id)); ?>"><?php echo e($order->table->name); ?></a>
                                <?php else: ?>
                                    غير محدد
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>النادل</th>
                            <td>
                                <?php if($order->user): ?>
                                    <a href="<?php echo e(route('users.show', $order->user->id)); ?>"><?php echo e($order->user->name); ?></a>
                                <?php else: ?>
                                    غير محدد
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>الحالة</th>
                            <td>
                                <?php if($order->status == 'pending'): ?>
                                    <span class="badge bg-secondary">قيد الانتظار</span>
                                <?php elseif($order->status == 'preparing'): ?>
                                    <span class="badge bg-primary">قيد التحضير</span>
                                <?php elseif($order->status == 'ready'): ?>
                                    <span class="badge bg-info">جاهز</span>
                                <?php elseif($order->status == 'delivered'): ?>
                                    <span class="badge bg-success">تم التوصيل</span>
                                <?php elseif($order->status == 'completed'): ?>
                                    <span class="badge bg-success">مكتمل</span>
                                <?php elseif($order->status == 'cancelled'): ?>
                                    <span class="badge bg-danger">ملغي</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>المبلغ الإجمالي</th>
                            <td class="fw-bold"><?php echo e(number_format($order->total_amount, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></td>
                        </tr>
                        <tr>
                            <th>تاريخ الإنشاء</th>
                            <td><?php echo e($order->created_at->format('Y-m-d H:i')); ?></td>
                        </tr>
                        <tr>
                            <th>آخر تحديث</th>
                            <td><?php echo e($order->updated_at->format('Y-m-d H:i')); ?></td>
                        </tr>
                    </table>

                    <?php if($order->notes): ?>
                    <div class="alert alert-info mt-3">
                        <h6 class="alert-heading">ملاحظات:</h6>
                        <p class="mb-0"><?php echo e($order->notes); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <?php if(!in_array($order->status, ['completed', 'cancelled'])): ?>
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">تحديث الحالة</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('orders.update', $order->id)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <input type="hidden" name="notes" value="<?php echo e($order->notes); ?>">

                        <div class="mb-3">
                            <label for="status" class="form-label">الحالة الجديدة</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="pending" <?php echo e($order->status == 'pending' ? 'selected' : ''); ?>>قيد الانتظار</option>
                                <option value="preparing" <?php echo e($order->status == 'preparing' ? 'selected' : ''); ?>>قيد التحضير</option>
                                <option value="ready" <?php echo e($order->status == 'ready' ? 'selected' : ''); ?>>جاهز</option>
                                <option value="delivered" <?php echo e($order->status == 'delivered' ? 'selected' : ''); ?>>تم التوصيل</option>
                                <option value="completed" <?php echo e($order->status == 'completed' ? 'selected' : ''); ?>>مكتمل</option>
                                <option value="cancelled" <?php echo e($order->status == 'cancelled' ? 'selected' : ''); ?>>ملغي</option>
                            </select>
                        </div>

                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-save me-1"></i> تحديث الحالة
                        </button>
                    </form>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">عناصر الطلب</h5>
                </div>
                <div class="card-body">
                    <?php if($order->orderItems->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>#</th>
                                        <th>المنتج</th>
                                        <th>السعر</th>
                                        <th>الكمية</th>
                                        <th>المجموع</th>
                                        <th>ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $order->orderItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($index + 1); ?></td>
                                        <td>
                                            <?php if($item->product): ?>
                                                <a href="<?php echo e(route('products.show', $item->product->id)); ?>"><?php echo e($item->product->name); ?></a>
                                            <?php else: ?>
                                                منتج محذوف
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e(number_format($item->unit_price, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></td>
                                        <td><?php echo e($item->quantity); ?></td>
                                        <td><?php echo e(number_format($item->subtotal, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></td>
                                        <td><?php echo e($item->notes ?? '-'); ?></td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th colspan="4" class="text-end">المجموع الكلي:</th>
                                        <th><?php echo e(number_format($order->total_amount, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            لا توجد عناصر في هذا الطلب
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\restaurant\resources\views/orders/show.blade.php ENDPATH**/ ?>