<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 border-r-4" :class="borderColor">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ title }}</p>
        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ value }}</p>
        <div v-if="trend" class="flex items-center mt-2">
          <component
            :is="trend.direction === 'up' ? ArrowUpIcon : ArrowDownIcon"
            :class="[
              'w-4 h-4 ml-1',
              trend.direction === 'up' ? 'text-green-500' : 'text-red-500'
            ]"
          />
          <span
            :class="[
              'text-sm font-medium',
              trend.direction === 'up' ? 'text-green-600' : 'text-red-600'
            ]"
          >
            {{ trend.percentage }}%
          </span>
          <span class="text-sm text-gray-500 dark:text-gray-400 mr-2">
            من الأمس
          </span>
        </div>
      </div>
      <div :class="iconBgColor" class="p-3 rounded-full">
        <component :is="iconComponent" :class="iconColor" class="w-6 h-6" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import {
  CurrencyDollarIcon,
  ShoppingCartIcon,
  TableCellsIcon,
  ClockIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/vue/24/outline'

const props = defineProps({
  title: String,
  value: [String, Number],
  icon: String,
  color: {
    type: String,
    default: 'blue'
  },
  trend: Object
})

const iconComponents = {
  CurrencyDollarIcon,
  ShoppingCartIcon,
  TableCellsIcon,
  ClockIcon
}

const iconComponent = computed(() => iconComponents[props.icon] || ShoppingCartIcon)

const colorClasses = {
  green: {
    border: 'border-green-500',
    iconBg: 'bg-green-100 dark:bg-green-900',
    iconColor: 'text-green-600 dark:text-green-400'
  },
  blue: {
    border: 'border-blue-500',
    iconBg: 'bg-blue-100 dark:bg-blue-900',
    iconColor: 'text-blue-600 dark:text-blue-400'
  },
  yellow: {
    border: 'border-yellow-500',
    iconBg: 'bg-yellow-100 dark:bg-yellow-900',
    iconColor: 'text-yellow-600 dark:text-yellow-400'
  },
  red: {
    border: 'border-red-500',
    iconBg: 'bg-red-100 dark:bg-red-900',
    iconColor: 'text-red-600 dark:text-red-400'
  }
}

const borderColor = computed(() => colorClasses[props.color]?.border || colorClasses.blue.border)
const iconBgColor = computed(() => colorClasses[props.color]?.iconBg || colorClasses.blue.iconBg)
const iconColor = computed(() => colorClasses[props.color]?.iconColor || colorClasses.blue.iconColor)
</script>
