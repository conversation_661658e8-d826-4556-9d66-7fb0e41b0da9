<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class BackupScheduleCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:schedule {type=database : نوع النسخة الاحتياطية (full|database|files)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'تشغيل النسخ الاحتياطي المجدول';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->argument('type');

        $this->info("بدء النسخ الاحتياطي المجدول - النوع: {$type}");

        try {
            // إنشاء سجل في قاعدة البيانات
            $backup = \App\Models\Backup::create([
                'filename' => 'scheduled_' . $type . '_' . now()->format('Y-m-d_H-i-s') . '.zip',
                'type' => $type,
                'status' => 'pending',
                'description' => 'نسخة احتياطية مجدولة',
                'is_scheduled' => true,
                'started_at' => now(),
            ]);

            // تشغيل النسخ الاحتياطي
            \App\Jobs\BackupJob::dispatch($type, true, false, null, $backup->id);

            $this->info("تم بدء النسخ الاحتياطي بنجاح - ID: {$backup->id}");

        } catch (\Exception $e) {
            $this->error("فشل في بدء النسخ الاحتياطي: " . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
