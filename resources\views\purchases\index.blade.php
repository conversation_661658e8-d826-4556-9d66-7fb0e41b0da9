@extends('layouts.app')

@section('title', 'إدارة المشتريات')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ number_format($totalPurchases, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</h4>
                                    <p class="mb-0">إجمالي المشتريات</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-shopping-cart fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ number_format($monthlyPurchases, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</h4>
                                    <p class="mb-0">مشتريات الشهر</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar-month fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ number_format($pendingPayments, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</h4>
                                    <p class="mb-0">مدفوعات معلقة</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4>{{ number_format($todayPurchases, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</h4>
                                    <p class="mb-0">مشتريات اليوم</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar-day fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-shopping-cart me-2"></i>
                        إدارة المشتريات
                    </h3>
                    <div>
                        <a href="{{ route('purchases.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            إضافة مشترى جديد
                        </a>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-download me-1"></i>
                                تصدير
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#">تصدير الكل</a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportFiltered()">تصدير المفلتر</a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-1"></i>
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-1"></i>
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <!-- فلاتر البحث -->
                    <form method="GET" action="{{ route('purchases.index') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="search">البحث</label>
                                    <input type="text" class="form-control" id="search" name="search" 
                                           value="{{ request('search') }}" placeholder="البحث في المشتريات...">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="payment_status">حالة الدفع</label>
                                    <select class="form-select" id="payment_status" name="payment_status">
                                        <option value="">جميع الحالات</option>
                                        @foreach($paymentStatuses as $key => $name)
                                            <option value="{{ $key }}" {{ request('payment_status') == $key ? 'selected' : '' }}>
                                                {{ $name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="start_date">من تاريخ</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" 
                                           value="{{ request('start_date') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="end_date">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" 
                                           value="{{ request('end_date') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div class="d-flex">
                                        <button type="submit" class="btn btn-primary me-2">
                                            <i class="fas fa-search me-1"></i>
                                            بحث
                                        </button>
                                        <a href="{{ route('purchases.index') }}" class="btn btn-secondary">
                                            <i class="fas fa-times me-1"></i>
                                            مسح
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- جدول المشتريات -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>رقم المشترى</th>
                                    <th>التاريخ</th>
                                    <th>المورد</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>المبلغ المدفوع</th>
                                    <th>المبلغ المتبقي</th>
                                    <th>حالة الدفع</th>
                                    <th>حالة التسليم</th>
                                    <th>المستخدم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($purchases as $purchase)
                                    <tr>
                                        <td>
                                            <strong>{{ $purchase->purchase_number }}</strong>
                                        </td>
                                        <td>{{ $purchase->purchase_date->format('Y-m-d') }}</td>
                                        <td>
                                            <div>
                                                <strong>{{ $purchase->supplier_name }}</strong>
                                                @if($purchase->supplier_phone)
                                                    <br><small class="text-muted">{{ $purchase->supplier_phone }}</small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>{{ number_format($purchase->total_amount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                        <td>{{ number_format($purchase->paid_amount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                        <td>{{ number_format($purchase->remaining_amount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                        <td>
                                            @if($purchase->payment_status == 'paid')
                                                <span class="badge bg-success">{{ $purchase->payment_status_name }}</span>
                                            @elseif($purchase->payment_status == 'partial')
                                                <span class="badge bg-warning">{{ $purchase->payment_status_name }}</span>
                                            @elseif($purchase->payment_status == 'overdue')
                                                <span class="badge bg-danger">{{ $purchase->payment_status_name }}</span>
                                            @else
                                                <span class="badge bg-secondary">{{ $purchase->payment_status_name }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($purchase->delivery_status == 'delivered')
                                                <span class="badge bg-success">{{ $purchase->delivery_status_name }}</span>
                                            @elseif($purchase->delivery_status == 'partial')
                                                <span class="badge bg-warning">{{ $purchase->delivery_status_name }}</span>
                                            @elseif($purchase->delivery_status == 'cancelled')
                                                <span class="badge bg-danger">{{ $purchase->delivery_status_name }}</span>
                                            @else
                                                <span class="badge bg-secondary">{{ $purchase->delivery_status_name }}</span>
                                            @endif
                                        </td>
                                        <td>{{ $purchase->user->name }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('purchases.show', $purchase) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('purchases.edit', $purchase) }}" class="btn btn-sm btn-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        onclick="deletePurchase({{ $purchase->id }})">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="10" class="text-center">لا توجد مشتريات</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $purchases->appends(request()->query())->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذا المشترى؟</p>
                <p class="text-danger"><strong>تحذير:</strong> سيتم حذف جميع العناصر المرتبطة به!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function deletePurchase(id) {
    document.getElementById('deleteForm').action = '/purchases/' + id;
    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

function exportFiltered() {
    const form = document.querySelector('form');
    const url = new URL('/purchases/export', window.location.origin);
    
    // إضافة المعاملات الحالية للرابط
    const formData = new FormData(form);
    for (let [key, value] of formData.entries()) {
        if (value) {
            url.searchParams.append(key, value);
        }
    }
    
    window.location.href = url.toString();
}
</script>
@endsection
