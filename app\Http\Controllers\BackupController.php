<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\File;
use ZipArchive;
use Carbon\Carbon;
use App\Models\Backup;
use App\Jobs\BackupJob;

class BackupController extends Controller
{
    /**
     * إنشاء instance جديد من controller
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            $user = auth()->user();

            // السماح للمدير فقط
            if (!$user || !$user->hasRole('admin')) {
                abort(403, 'ليس لديك صلاحية للوصول إلى النسخ الاحتياطي');
            }

            return $next($request);
        });
    }

    /**
     * عرض صفحة النسخ الاحتياطي
     */
    public function index()
    {
        try {
            // التأكد من وجود مجلد النسخ الاحتياطية
            $backupDir = storage_path('app/backups');
            if (!File::exists($backupDir)) {
                File::makeDirectory($backupDir, 0755, true);
            }

            $backups = Backup::with('creator')->orderBy('created_at', 'desc')->paginate(20);
            $statistics = Backup::getStatistics();
            $recentBackups = Backup::recent(5);

            return view('admin.backup.enhanced', compact('backups', 'statistics', 'recentBackups'));
        } catch (\Exception $e) {
            \Log::error('خطأ في صفحة النسخ الاحتياطي: ' . $e->getMessage());
            return back()->with('error', 'حدث خطأ في تحميل صفحة النسخ الاحتياطي: ' . $e->getMessage());
        }
    }

    /**
     * إنشاء نسخة احتياطية يدوية
     */
    public function createBackup(Request $request)
    {


        try {
            // التحقق من صحة البيانات
            $request->validate([
                'backup_type' => 'required|in:database,files,full',
                'description' => 'nullable|string|max:255',
            ]);

            $backupType = $request->input('backup_type', 'database');
            $description = $request->input('description', 'نسخة احتياطية يدوية');
            $userId = auth()->id();

            // التحقق من وجود مجلد النسخ الاحتياطية
            $backupDir = storage_path('app/backups');
            if (!File::exists($backupDir)) {
                File::makeDirectory($backupDir, 0755, true);
            }

            // إنشاء سجل في قاعدة البيانات
            $backup = \App\Models\Backup::create([
                'filename' => 'manual_' . $backupType . '_' . now()->format('Y-m-d_H-i-s') . '.zip',
                'type' => $backupType,
                'status' => 'pending',
                'description' => $description,
                'created_by' => $userId,
                'is_scheduled' => false,
            ]);

            // تحديث حالة النسخة الاحتياطية إلى قيد التنفيذ
            $backup->update([
                'status' => 'running',
                'started_at' => now()
            ]);

            try {
                // تنفيذ النسخ الاحتياطي مباشرة
                $backupPath = $this->performBackup($backupType);

                // تحديث معلومات النسخة الاحتياطية
                $backup->update([
                    'status' => 'completed',
                    'completed_at' => now(),
                    'file_size' => file_exists($backupPath) ? filesize($backupPath) : null,
                    'filename' => basename($backupPath)
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'تم إنشاء النسخة الاحتياطية بنجاح',
                    'backup_path' => basename($backupPath),
                    'backup_id' => $backup->id
                ])->header('Content-Type', 'application/json');

            } catch (\Exception $e) {
                // تحديث حالة النسخة الاحتياطية في حالة الفشل
                $backup->update([
                    'status' => 'failed',
                    'completed_at' => now(),
                    'error_message' => $e->getMessage()
                ]);

                throw $e; // إعادة رمي الخطأ ليتم التعامل معه في catch الخارجي
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة: ' . implode(', ', $e->validator->errors()->all())
            ], 422);
        } catch (\Exception $e) {
            \Log::error('خطأ في إنشاء النسخة الاحتياطية: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في بدء عملية النسخ الاحتياطي: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * إنشاء نسخة احتياطية فورية (للاختبار)
     */
    public function createBackupSync(Request $request)
    {
        try {
            // التحقق من صحة البيانات
            $request->validate([
                'backup_type' => 'required|in:database,files,full',
                'description' => 'nullable|string|max:255',
            ]);

            $backupType = $request->input('backup_type', 'database');
            $description = $request->input('description', 'نسخة احتياطية فورية');

            // إنشاء سجل في قاعدة البيانات
            $backup = \App\Models\Backup::create([
                'filename' => 'sync_' . $backupType . '_' . now()->format('Y-m-d_H-i-s') . '.zip',
                'type' => $backupType,
                'status' => 'running',
                'description' => $description,
                'created_by' => auth()->id(),
                'is_scheduled' => false,
                'started_at' => now(),
            ]);

            $backupPath = $this->performBackup($backupType);

            // تحديث معلومات النسخة الاحتياطية
            $backup->update([
                'status' => 'completed',
                'completed_at' => now(),
                'file_size' => file_exists($backupPath) ? filesize($backupPath) : null,
                'filename' => basename($backupPath)
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء النسخة الاحتياطية بنجاح',
                'backup_path' => basename($backupPath),
                'backup_id' => $backup->id
            ])->header('Content-Type', 'application/json');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة: ' . implode(', ', $e->validator->errors()->all())
            ], 422);
        } catch (\Exception $e) {
            // تحديث حالة النسخة الاحتياطية في حالة الفشل
            if (isset($backup)) {
                $backup->update([
                    'status' => 'failed',
                    'completed_at' => now(),
                    'error_message' => $e->getMessage()
                ]);
            }

            \Log::error('خطأ في إنشاء النسخة الاحتياطية الفورية: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في إنشاء النسخة الاحتياطية: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * تنفيذ النسخ الاحتياطي
     */
    private function performBackup($type)
    {
        $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
        $backupDir = storage_path('app/backups');

        if (!File::exists($backupDir)) {
            File::makeDirectory($backupDir, 0755, true);
        }

        switch ($type) {
            case 'database':
                // استخدام الطريقة المبسطة مباشرة لدعم SQLite
                return $this->backupDatabaseSimple($timestamp);
            case 'database_simple':
                return $this->backupDatabaseSimple($timestamp);
            case 'files':
                return $this->backupFiles($timestamp);
            case 'full':
            default:
                return $this->backupFull($timestamp);
        }
    }

    /**
     * نسخ احتياطي لقاعدة البيانات فقط
     */
    private function backupDatabase($timestamp)
    {
        $filename = "database_backup_{$timestamp}.sql";
        $filepath = storage_path("app/backups/{$filename}");

        $dbHost = config('database.connections.mysql.host');
        $dbName = config('database.connections.mysql.database');
        $dbUser = config('database.connections.mysql.username');
        $dbPass = config('database.connections.mysql.password');
        $dbPort = config('database.connections.mysql.port', 3306);

        // تحسين أداء mysqldump
        $options = [
            '--single-transaction',     // للحفاظ على تناسق البيانات
            '--routines',              // تضمين الإجراءات المخزنة
            '--triggers',              // تضمين المحفزات
            '--quick',                 // استرداد الصفوف واحدة تلو الأخرى
            '--lock-tables=false',     // عدم قفل الجداول
            '--add-drop-table',        // إضافة DROP TABLE قبل CREATE
            '--disable-keys',          // تعطيل المفاتيح أثناء الإدراج
            '--extended-insert',       // استخدام INSERT متعددة القيم
            '--compress',              // ضغط البيانات المنقولة
        ];

        $optionsStr = implode(' ', $options);

        // بناء الأمر مع معالجة كلمة المرور
        if (!empty($dbPass)) {
            $command = "mysqldump --host={$dbHost} --port={$dbPort} --user={$dbUser} --password=\"{$dbPass}\" {$optionsStr} {$dbName} > \"{$filepath}\"";
        } else {
            $command = "mysqldump --host={$dbHost} --port={$dbPort} --user={$dbUser} {$optionsStr} {$dbName} > \"{$filepath}\"";
        }

        // تنفيذ الأمر مع timeout
        $process = proc_open($command, [
            0 => ['pipe', 'r'],
            1 => ['pipe', 'w'],
            2 => ['pipe', 'w']
        ], $pipes);

        if (!is_resource($process)) {
            throw new \Exception('فشل في بدء عملية تصدير قاعدة البيانات');
        }

        // إغلاق stdin
        fclose($pipes[0]);

        // قراءة الأخطاء
        $errors = stream_get_contents($pipes[2]);
        fclose($pipes[1]);
        fclose($pipes[2]);

        $returnVar = proc_close($process);

        if ($returnVar !== 0) {
            throw new \Exception('فشل في تصدير قاعدة البيانات: ' . $errors);
        }

        // التحقق من وجود الملف وحجمه
        if (!File::exists($filepath) || File::size($filepath) === 0) {
            throw new \Exception('فشل في إنشاء ملف النسخة الاحتياطية أو الملف فارغ');
        }

        return $filepath;
    }

    /**
     * نسخ احتياطي مبسط لقاعدة البيانات (للقواعد الصغيرة)
     */
    private function backupDatabaseSimple($timestamp)
    {
        $filename = "database_backup_simple_{$timestamp}.sql";
        $filepath = storage_path("app/backups/{$filename}");

        try {
            $handle = fopen($filepath, 'w');

            if (!$handle) {
                throw new \Exception('فشل في إنشاء ملف النسخة الاحتياطية');
            }

            // كتابة header
            fwrite($handle, "-- نسخة احتياطية لقاعدة البيانات\n");
            fwrite($handle, "-- تاريخ الإنشاء: " . Carbon::now()->format('Y-m-d H:i:s') . "\n\n");

            // التحقق من نوع قاعدة البيانات
            $dbDriver = config('database.default');
            $connection = config("database.connections.{$dbDriver}");

            if ($dbDriver === 'sqlite') {
                // SQLite
                $tables = DB::select("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
                $tableKey = 'name';
            } else {
                // MySQL
                fwrite($handle, "SET FOREIGN_KEY_CHECKS=0;\n");
                fwrite($handle, "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n");
                fwrite($handle, "SET time_zone = \"+00:00\";\n\n");

                $tables = DB::select('SHOW TABLES');
                $dbName = $connection['database'];
                $tableKey = "Tables_in_{$dbName}";
            }

            foreach ($tables as $table) {
                $tableName = $table->$tableKey;

                // تخطي جداول النظام
                if (in_array($tableName, ['migrations', 'failed_jobs', 'password_resets'])) {
                    continue;
                }

                fwrite($handle, "\n-- --------------------------------------------------------\n");
                fwrite($handle, "-- بنية الجدول `{$tableName}`\n");
                fwrite($handle, "-- --------------------------------------------------------\n\n");

                // إنشاء بنية الجدول
                if ($dbDriver === 'sqlite') {
                    // SQLite
                    $createTable = DB::select("SELECT sql FROM sqlite_master WHERE type='table' AND name='{$tableName}'")[0];
                    fwrite($handle, "DROP TABLE IF EXISTS `{$tableName}`;\n");
                    fwrite($handle, $createTable->sql . ";\n\n");
                } else {
                    // MySQL
                    $createTable = DB::select("SHOW CREATE TABLE `{$tableName}`")[0];
                    fwrite($handle, "DROP TABLE IF EXISTS `{$tableName}`;\n");
                    fwrite($handle, $createTable->{'Create Table'} . ";\n\n");
                }

                // إدراج البيانات
                $rows = DB::table($tableName)->get();
                if ($rows->count() > 0) {
                    fwrite($handle, "-- بيانات الجدول `{$tableName}`\n");

                    foreach ($rows as $row) {
                        $values = [];
                        foreach ((array)$row as $value) {
                            if (is_null($value)) {
                                $values[] = 'NULL';
                            } else {
                                $values[] = "'" . addslashes($value) . "'";
                            }
                        }

                        $columns = implode('`, `', array_keys((array)$row));
                        $valuesStr = implode(', ', $values);

                        fwrite($handle, "INSERT INTO `{$tableName}` (`{$columns}`) VALUES ({$valuesStr});\n");
                    }
                    fwrite($handle, "\n");
                }
            }

            // إنهاء الملف حسب نوع قاعدة البيانات
            if ($dbDriver === 'mysql') {
                fwrite($handle, "SET FOREIGN_KEY_CHECKS=1;\n");
            }

            fclose($handle);

            return $filepath;

        } catch (\Exception $e) {
            if (isset($handle) && $handle) {
                fclose($handle);
            }
            if (File::exists($filepath)) {
                File::delete($filepath);
            }
            throw $e;
        }
    }

    /**
     * نسخ احتياطي للملفات فقط
     */
    private function backupFiles($timestamp)
    {
        $filename = "files_backup_{$timestamp}.zip";
        $filepath = storage_path("app/backups/{$filename}");

        $zip = new ZipArchive();
        if ($zip->open($filepath, ZipArchive::CREATE) !== TRUE) {
            throw new \Exception('فشل في إنشاء ملف ZIP');
        }

        // إضافة ملفات المشروع (باستثناء بعض المجلدات)
        $this->addDirectoryToZip($zip, base_path(), '', [
            'node_modules',
            'vendor',
            '.git',
            'storage/logs',
            'storage/framework/cache',
            'storage/framework/sessions',
            'storage/framework/views',
            'storage/app/backups'
        ]);

        $zip->close();

        return $filepath;
    }

    /**
     * نسخ احتياطي كامل (قاعدة البيانات + الملفات)
     */
    private function backupFull($timestamp)
    {
        $filename = "full_backup_{$timestamp}.zip";
        $filepath = storage_path("app/backups/{$filename}");

        // إنشاء نسخة احتياطية لقاعدة البيانات أولاً
        $dbBackup = $this->backupDatabaseSimple($timestamp);

        $zip = new ZipArchive();
        if ($zip->open($filepath, ZipArchive::CREATE) !== TRUE) {
            throw new \Exception('فشل في إنشاء ملف ZIP');
        }

        // إضافة نسخة قاعدة البيانات
        $zip->addFile($dbBackup, 'database_backup.sql');

        // إضافة ملفات المشروع
        $this->addDirectoryToZip($zip, base_path(), '', [
            'node_modules',
            'vendor',
            '.git',
            'storage/logs',
            'storage/framework/cache',
            'storage/framework/sessions',
            'storage/framework/views',
            'storage/app/backups'
        ]);

        $zip->close();

        // حذف ملف قاعدة البيانات المؤقت
        File::delete($dbBackup);

        return $filepath;
    }

    /**
     * إضافة مجلد إلى ملف ZIP
     */
    private function addDirectoryToZip($zip, $dir, $zipDir = '', $excludeDirs = [])
    {
        if (is_dir($dir)) {
            $files = scandir($dir);
            foreach ($files as $file) {
                if ($file != '.' && $file != '..') {
                    $filePath = $dir . '/' . $file;
                    $zipPath = $zipDir . $file;

                    // تحقق من المجلدات المستبعدة
                    $shouldExclude = false;
                    foreach ($excludeDirs as $excludeDir) {
                        if (strpos($zipPath, $excludeDir) === 0) {
                            $shouldExclude = true;
                            break;
                        }
                    }

                    if ($shouldExclude) {
                        continue;
                    }

                    if (is_dir($filePath)) {
                        $zip->addEmptyDir($zipPath);
                        $this->addDirectoryToZip($zip, $filePath, $zipPath . '/', $excludeDirs);
                    } else {
                        $zip->addFile($filePath, $zipPath);
                    }
                }
            }
        }
    }



    /**
     * الحصول على قائمة النسخ الاحتياطية
     */
    private function getBackupsList()
    {
        $backupDir = storage_path('app/backups');
        $backups = [];

        if (File::exists($backupDir)) {
            $files = File::files($backupDir);
            foreach ($files as $file) {
                $backups[] = [
                    'name' => $file->getFilename(),
                    'size' => $this->formatBytes($file->getSize()),
                    'date' => Carbon::createFromTimestamp($file->getMTime())->format('Y-m-d H:i:s'),
                    'path' => $file->getPathname()
                ];
            }

            // ترتيب حسب التاريخ (الأحدث أولاً)
            usort($backups, function($a, $b) {
                return strtotime($b['date']) - strtotime($a['date']);
            });
        }

        return $backups;
    }

    /**
     * تنسيق حجم الملف
     */
    private function formatBytes($size, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }

        return round($size, $precision) . ' ' . $units[$i];
    }

    /**
     * تحميل نسخة احتياطية
     */
    public function downloadBackup($filename)
    {
        $filepath = storage_path("app/backups/{$filename}");

        if (!File::exists($filepath)) {
            abort(404, 'الملف غير موجود');
        }

        return response()->download($filepath);
    }

    /**
     * حذف نسخة احتياطية
     */
    public function deleteBackup(Request $request, $filename)
    {


        try {
            // البحث عن النسخة الاحتياطية في قاعدة البيانات
            $backup = \App\Models\Backup::where('filename', $filename)->first();

            if ($backup) {
                // التحقق من حالة النسخة الاحتياطية
                if ($backup->status === 'pending' || $backup->status === 'running') {
                    // للنسخ المعلقة، احذف السجل مباشرة لأن الملف غير موجود
                    $backup->delete();

                    \Log::info('تم حذف النسخة الاحتياطية المعلقة', [
                        'filename' => $filename,
                        'status' => $backup->status
                    ]);

                    return response()->json([
                        'success' => true,
                        'message' => 'تم حذف النسخة الاحتياطية المعلقة بنجاح'
                    ])->header('Content-Type', 'application/json');
                }

                // للنسخ المكتملة، احذف الملف والسجل
                $backup->deleteFile();

                return response()->json([
                    'success' => true,
                    'message' => 'تم حذف النسخة الاحتياطية بنجاح'
                ])->header('Content-Type', 'application/json');
            }

            // إذا لم توجد في قاعدة البيانات، جرب حذف الملف مباشرة
            $filepath = storage_path("app/backups/{$filename}");

            if (File::exists($filepath)) {
                File::delete($filepath);

                return response()->json([
                    'success' => true,
                    'message' => 'تم حذف الملف بنجاح (ملف قديم)'
                ])->header('Content-Type', 'application/json');
            }

            return response()->json([
                'success' => false,
                'message' => 'الملف غير موجود'
            ], 404)->header('Content-Type', 'application/json');

        } catch (\Exception $e) {
            \Log::error('خطأ في حذف النسخة الاحتياطية: ' . $e->getMessage(), [
                'filename' => $filename,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في حذف النسخة الاحتياطية: ' . $e->getMessage()
            ], 500)->header('Content-Type', 'application/json');
        }
    }

    /**
     * استعادة نسخة احتياطية مباشرة
     */
    public function restoreBackupDirect(Request $request)
    {
        try {
            $request->validate([
                'filename' => 'required|string',
            ]);

            $filename = $request->input('filename');
            $filepath = storage_path("app/backups/{$filename}");

            if (!File::exists($filepath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'ملف النسخة الاحتياطية غير موجود'
                ], 404)->header('Content-Type', 'application/json');
            }

            // إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
            $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
            $currentBackup = $this->backupDatabase("before_restore_{$timestamp}");

            // إنشاء سجل للنسخة الاحتياطية الحالية
            \App\Models\Backup::create([
                'filename' => basename($currentBackup),
                'type' => 'database',
                'status' => 'completed',
                'description' => 'نسخة احتياطية تلقائية قبل الاستعادة',
                'created_by' => auth()->id(),
                'is_scheduled' => false,
                'file_size' => file_exists($currentBackup) ? filesize($currentBackup) : null,
                'started_at' => now(),
                'completed_at' => now(),
            ]);

            // تحديد نوع الاستعادة بناءً على امتداد الملف
            $extension = pathinfo($filename, PATHINFO_EXTENSION);

            if ($extension === 'sql') {
                // استعادة قاعدة البيانات فقط
                $this->performDatabaseRestore($filepath);
                $message = 'تم استعادة قاعدة البيانات بنجاح';
            } elseif ($extension === 'zip') {
                // استعادة كاملة (قاعدة البيانات + الملفات)
                $this->performFullRestore($filepath);
                $message = 'تم استعادة النسخة الكاملة بنجاح';
            } else {
                throw new \Exception('نوع الملف غير مدعوم');
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'current_backup' => basename($currentBackup),
                'restored_file' => $filename
            ])->header('Content-Type', 'application/json');

        } catch (\Exception $e) {
            \Log::error('خطأ في الاستعادة المباشرة: ' . $e->getMessage(), [
                'filename' => $request->input('filename'),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في استعادة النسخة الاحتياطية: ' . $e->getMessage()
            ], 500)->header('Content-Type', 'application/json');
        }
    }

    /**
     * رفع واستعادة قاعدة بيانات
     */
    public function restoreDatabase(Request $request)
    {
        try {
            $request->validate([
                'restore_file' => 'required|file|mimes:sql,zip|max:102400', // 100MB max
                'restore_type' => 'required|in:database,full',
            ], [
                'restore_file.required' => 'يرجى اختيار ملف النسخة الاحتياطية',
                'restore_file.file' => 'يجب أن يكون الملف صالحاً',
                'restore_file.mimes' => 'يجب أن يكون الملف من نوع SQL أو ZIP',
                'restore_file.max' => 'حجم الملف يجب أن يكون أقل من 100 ميجابايت',
                'restore_type.required' => 'يرجى تحديد نوع الاستعادة',
                'restore_type.in' => 'نوع الاستعادة غير صحيح',
            ]);

            $file = $request->file('restore_file');
            $restoreType = $request->input('restore_type');
            $timestamp = Carbon::now()->format('Y-m-d_H-i-s');

            $extension = $file->getClientOriginalExtension();
            $filename = "uploaded_{$restoreType}_{$timestamp}.{$extension}";

            // حفظ الملف المرفوع
            $filepath = storage_path("app/backups/{$filename}");
            $file->move(storage_path('app/backups'), $filename);

            // إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
            $currentBackup = $this->backupDatabase("before_restore_{$timestamp}");

            // إنشاء سجل للنسخة الاحتياطية الحالية
            \App\Models\Backup::create([
                'filename' => basename($currentBackup),
                'type' => 'database',
                'status' => 'completed',
                'description' => 'نسخة احتياطية تلقائية قبل الاستعادة',
                'created_by' => auth()->id(),
                'is_scheduled' => false,
                'file_size' => file_exists($currentBackup) ? filesize($currentBackup) : null,
                'started_at' => now(),
                'completed_at' => now(),
            ]);

            // تحديد نوع الاستعادة
            if ($extension === 'sql' || $restoreType === 'database') {
                $this->performDatabaseRestore($filepath);
                $message = 'تم استعادة قاعدة البيانات بنجاح';
            } elseif ($extension === 'zip' && $restoreType === 'full') {
                $this->performFullRestore($filepath);
                $message = 'تم استعادة النسخة الكاملة بنجاح';
            } else {
                throw new \Exception('نوع الاستعادة غير متطابق مع نوع الملف');
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'current_backup' => basename($currentBackup),
                'restored_file' => $filename
            ])->header('Content-Type', 'application/json');

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في استعادة قاعدة البيانات: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * تنفيذ استعادة قاعدة البيانات
     */
    private function performDatabaseRestore($filepath)
    {
        // قراءة محتوى ملف SQL
        $sqlContent = File::get($filepath);

        // تنظيف وتقسيم الاستعلامات
        $sqlContent = preg_replace('/\/\*.*?\*\//s', '', $sqlContent); // إزالة التعليقات متعددة الأسطر
        $queries = preg_split('/;\s*$/m', $sqlContent);
        $queries = array_filter(array_map('trim', $queries));

        // تنفيذ الاستعلامات في مجموعات صغيرة
        DB::beginTransaction();

        try {
            // إعدادات الأداء
            DB::statement('SET FOREIGN_KEY_CHECKS=0');
            DB::statement('SET UNIQUE_CHECKS=0');
            DB::statement('SET AUTOCOMMIT=0');

            $batchSize = 50; // تنفيذ 50 استعلام في كل مرة
            $batches = array_chunk($queries, $batchSize);

            foreach ($batches as $batch) {
                foreach ($batch as $query) {
                    $query = trim($query);

                    // تخطي الاستعلامات الفارغة والتعليقات
                    if (empty($query) ||
                        str_starts_with($query, '--') ||
                        str_starts_with($query, '/*') ||
                        str_starts_with($query, '#')) {
                        continue;
                    }

                    try {
                        DB::statement($query);
                    } catch (\Exception $e) {
                        // تسجيل الاستعلام الذي فشل للمراجعة
                        \Log::warning('فشل في تنفيذ استعلام: ' . substr($query, 0, 100) . '... - ' . $e->getMessage());

                        // إذا كان الخطأ خطيراً، توقف
                        if (str_contains($e->getMessage(), 'syntax error') ||
                            str_contains($e->getMessage(), 'doesn\'t exist')) {
                            throw $e;
                        }
                    }
                }

                // commit كل مجموعة
                DB::commit();
                DB::beginTransaction();
            }

            // إعادة تفعيل الإعدادات
            DB::statement('SET FOREIGN_KEY_CHECKS=1');
            DB::statement('SET UNIQUE_CHECKS=1');
            DB::statement('SET AUTOCOMMIT=1');

            DB::commit();

        } catch (\Exception $e) {
            DB::rollback();
            DB::statement('SET FOREIGN_KEY_CHECKS=1');
            DB::statement('SET UNIQUE_CHECKS=1');
            DB::statement('SET AUTOCOMMIT=1');
            throw new \Exception('فشل في تنفيذ استعلامات قاعدة البيانات: ' . $e->getMessage());
        }
    }

    /**
     * استعادة كاملة من ملف ZIP
     */
    private function performFullRestore($filepath)
    {
        $extractPath = storage_path('app/temp_restore');

        // إنشاء مجلد مؤقت للاستخراج
        if (File::exists($extractPath)) {
            File::deleteDirectory($extractPath);
        }
        File::makeDirectory($extractPath, 0755, true);

        try {
            $zip = new \ZipArchive();
            if ($zip->open($filepath) !== TRUE) {
                throw new \Exception('فشل في فتح ملف ZIP');
            }

            // استخراج الملفات
            $zip->extractTo($extractPath);
            $zip->close();

            // البحث عن ملف قاعدة البيانات
            $dbFile = null;
            $files = File::allFiles($extractPath);

            foreach ($files as $file) {
                if (pathinfo($file->getFilename(), PATHINFO_EXTENSION) === 'sql') {
                    $dbFile = $file->getPathname();
                    break;
                }
            }

            if ($dbFile) {
                // استعادة قاعدة البيانات
                $this->performDatabaseRestore($dbFile);
            } else {
                throw new \Exception('لم يتم العثور على ملف قاعدة البيانات في النسخة الاحتياطية');
            }

        } finally {
            // تنظيف المجلد المؤقت
            if (File::exists($extractPath)) {
                File::deleteDirectory($extractPath);
            }
        }
    }

    /**
     * تنظيف النسخ الاحتياطية المعلقة
     */
    public function cleanupPending(Request $request)
    {
        try {
            // البحث عن النسخ المعلقة لأكثر من 30 دقيقة
            $pendingBackups = \App\Models\Backup::where('status', 'pending')
                ->orWhere('status', 'running')
                ->where('created_at', '<', \Carbon\Carbon::now()->subMinutes(30))
                ->get();

            if ($pendingBackups->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا توجد نسخ احتياطية معلقة للتنظيف'
                ])->header('Content-Type', 'application/json');
            }

            $updated = 0;
            foreach ($pendingBackups as $backup) {
                $backup->status = 'failed';
                $backup->completed_at = now();
                $backup->save();
                $updated++;
            }

            \Log::info('تم تنظيف النسخ المعلقة', [
                'count' => $updated,
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => "تم تحديث {$updated} نسخة احتياطية معلقة بنجاح"
            ])->header('Content-Type', 'application/json');

        } catch (\Exception $e) {
            \Log::error('❌ خطأ في تنظيف النسخ المعلقة', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تنظيف النسخ المعلقة: ' . $e->getMessage()
            ], 500)->header('Content-Type', 'application/json');
        }
    }

    /**
     * تنفيذ النسخ الاحتياطي لقاعدة البيانات
     */
    private function performDatabaseBackup($backup)
    {
        try {
            $backupDir = storage_path('app/backups');
            if (!File::exists($backupDir)) {
                File::makeDirectory($backupDir, 0755, true);
            }

            $filepath = storage_path("app/backups/{$backup->filename}");

            // إنشاء نسخة احتياطية بسيطة
            $content = "-- نسخة احتياطية لقاعدة البيانات\n";
            $content .= "-- تاريخ الإنشاء: " . now()->format('Y-m-d H:i:s') . "\n";
            $content .= "-- المستخدم: " . auth()->user()->name . "\n";
            $content .= "-- النوع: " . $backup->type . "\n\n";

            // إضافة بعض البيانات الأساسية
            $content .= "-- إحصائيات النظام:\n";
            $content .= "-- عدد المستخدمين: " . \App\Models\User::count() . "\n";

            if (class_exists('\App\Models\Order')) {
                $content .= "-- عدد الطلبات: " . \App\Models\Order::count() . "\n";
            }

            if (class_exists('\App\Models\Product')) {
                $content .= "-- عدد المنتجات: " . \App\Models\Product::count() . "\n";
            }

            $content .= "\n-- تم إنشاء هذه النسخة بواسطة نظام النسخ الاحتياطي\n";

            File::put($filepath, $content);

            // تحديث معلومات النسخة الاحتياطية
            $backup->update([
                'status' => 'completed',
                'completed_at' => now(),
                'file_size' => File::size($filepath),
            ]);

            return [
                'success' => true,
                'message' => 'تم إنشاء النسخة الاحتياطية بنجاح',
                'filepath' => $filepath
            ];

        } catch (\Exception $e) {
            $backup->update([
                'status' => 'failed',
                'completed_at' => now(),
                'error_message' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'فشل في إنشاء النسخة الاحتياطية: ' . $e->getMessage()
            ];
        }
    }
}
