<template>
  <AppLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          <HomeIcon class="w-8 h-8 inline-block ml-2" />
          لوحة التحكم
        </h1>
        <div class="text-sm text-gray-500 dark:text-gray-400">
          {{ currentDate }}
        </div>
      </div>
    </template>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Today's Sales -->
      <StatCard
        title="مبيعات اليوم"
        :value="formatCurrency(stats.todaySales)"
        icon="CurrencyDollarIcon"
        color="green"
        :trend="stats.salesTrend"
      />
      
      <!-- Today's Orders -->
      <StatCard
        title="طلبات اليوم"
        :value="stats.todayOrders"
        icon="ShoppingCartIcon"
        color="blue"
        :trend="stats.ordersTrend"
      />
      
      <!-- Active Tables -->
      <StatCard
        title="الطاولات النشطة"
        :value="stats.activeTables"
        icon="TableCellsIcon"
        color="yellow"
        :trend="stats.tablesTrend"
      />
      
      <!-- Pending Orders -->
      <StatCard
        title="طلبات معلقة"
        :value="stats.pendingOrders"
        icon="ClockIcon"
        color="red"
        :trend="stats.pendingTrend"
      />
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Sales Chart -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          <ChartBarIcon class="w-5 h-5 inline-block ml-2" />
          مبيعات الأسبوع
        </h3>
        <div class="h-64">
          <Line
            :data="salesChartData"
            :options="chartOptions"
          />
        </div>
      </div>

      <!-- Orders Chart -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          <PresentationChartLineIcon class="w-5 h-5 inline-block ml-2" />
          توزيع الطلبات
        </h3>
        <div class="h-64">
          <Doughnut
            :data="ordersChartData"
            :options="doughnutOptions"
          />
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Recent Orders -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          <ListBulletIcon class="w-5 h-5 inline-block ml-2" />
          آخر الطلبات
        </h3>
        <div class="space-y-3">
          <div
            v-for="order in recentOrders"
            :key="order.id"
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
          >
            <div class="flex items-center">
              <div class="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center ml-3">
                <span class="text-primary-600 dark:text-primary-400 font-semibold">
                  {{ order.table_number || 'خ' }}
                </span>
              </div>
              <div>
                <p class="font-medium text-gray-900 dark:text-white">
                  طلب #{{ order.id }}
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ order.customer_name || 'عميل' }} - {{ formatTime(order.created_at) }}
                </p>
              </div>
            </div>
            <div class="text-left">
              <p class="font-semibold text-gray-900 dark:text-white">
                {{ formatCurrency(order.total_amount) }}
              </p>
              <span
                :class="getStatusClass(order.status)"
                class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
              >
                {{ getStatusText(order.status) }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          <BoltIcon class="w-5 h-5 inline-block ml-2" />
          إجراءات سريعة
        </h3>
        <div class="grid grid-cols-2 gap-4">
          <Link
            :href="route('orders.create')"
            class="flex flex-col items-center p-4 bg-primary-50 dark:bg-primary-900/20 rounded-lg hover:bg-primary-100 dark:hover:bg-primary-900/30 transition-colors"
          >
            <PlusIcon class="w-8 h-8 text-primary-600 dark:text-primary-400 mb-2" />
            <span class="text-sm font-medium text-primary-600 dark:text-primary-400">طلب جديد</span>
          </Link>
          
          <Link
            :href="route('products.index')"
            class="flex flex-col items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
          >
            <CubeIcon class="w-8 h-8 text-green-600 dark:text-green-400 mb-2" />
            <span class="text-sm font-medium text-green-600 dark:text-green-400">المنتجات</span>
          </Link>
          
          <Link
            :href="route('reports.sales')"
            class="flex flex-col items-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg hover:bg-yellow-100 dark:hover:bg-yellow-900/30 transition-colors"
          >
            <ChartBarIcon class="w-8 h-8 text-yellow-600 dark:text-yellow-400 mb-2" />
            <span class="text-sm font-medium text-yellow-600 dark:text-yellow-400">التقارير</span>
          </Link>
          
          <Link
            :href="route('settings.index')"
            class="flex flex-col items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
          >
            <CogIcon class="w-8 h-8 text-gray-600 dark:text-gray-400 mb-2" />
            <span class="text-sm font-medium text-gray-600 dark:text-gray-400">الإعدادات</span>
          </Link>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue'
import { Link } from '@inertiajs/vue3'
import AppLayout from '@/Layouts/AppLayout.vue'
import StatCard from '@/Components/StatCard.vue'
import {
  HomeIcon,
  CurrencyDollarIcon,
  ShoppingCartIcon,
  TableCellsIcon,
  ClockIcon,
  ChartBarIcon,
  PresentationChartLineIcon,
  ListBulletIcon,
  BoltIcon,
  PlusIcon,
  CubeIcon,
  CogIcon
} from '@heroicons/vue/24/outline'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
} from 'chart.js'
import { Line, Doughnut } from 'vue-chartjs'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
)

const props = defineProps({
  stats: Object,
  recentOrders: Array,
  salesData: Array,
  ordersData: Array
})

const currentDate = computed(() => {
  return new Date().toLocaleDateString('ar-SA', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
})

const salesChartData = computed(() => ({
  labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
  datasets: [
    {
      label: 'المبيعات',
      data: props.salesData || [0, 0, 0, 0, 0, 0, 0],
      borderColor: 'rgb(59, 130, 246)',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.4
    }
  ]
}))

const ordersChartData = computed(() => ({
  labels: ['مكتمل', 'قيد التحضير', 'معلق', 'ملغي'],
  datasets: [
    {
      data: props.ordersData || [0, 0, 0, 0],
      backgroundColor: [
        'rgba(34, 197, 94, 0.8)',
        'rgba(59, 130, 246, 0.8)',
        'rgba(245, 158, 11, 0.8)',
        'rgba(239, 68, 68, 0.8)'
      ],
      borderWidth: 0
    }
  ]
}))

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    }
  },
  scales: {
    y: {
      beginAtZero: true
    }
  }
}

const doughnutOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom'
    }
  }
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('ar-SA', {
    style: 'currency',
    currency: 'SAR'
  }).format(amount || 0)
}

const formatTime = (datetime) => {
  return new Date(datetime).toLocaleTimeString('ar-SA', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getStatusClass = (status) => {
  const classes = {
    completed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    preparing: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    cancelled: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
  }
  return classes[status] || classes.pending
}

const getStatusText = (status) => {
  const texts = {
    completed: 'مكتمل',
    preparing: 'قيد التحضير',
    pending: 'معلق',
    cancelled: 'ملغي'
  }
  return texts[status] || 'غير محدد'
}
</script>
