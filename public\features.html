<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المطعم - Restaurant Management System</title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --dark-color: #34495e;
            --light-color: #ecf0f1;
        }

        body {
            font-family: 'Cairo', 'Roboto', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 100px 0;
            text-align: center;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .feature-description {
            color: #666;
            line-height: 1.8;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 3rem;
            color: var(--primary-color);
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: var(--secondary-color);
            border-radius: 2px;
        }

        .language-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .language-toggle button {
            background: var(--secondary-color);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .language-toggle button:hover {
            background: var(--primary-color);
            transform: scale(1.05);
        }

        .stats-section {
            background: var(--light-color);
            padding: 80px 0;
        }

        .stat-item {
            text-align: center;
            padding: 2rem;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: var(--secondary-color);
            display: block;
        }

        .stat-label {
            font-size: 1.1rem;
            color: var(--dark-color);
            margin-top: 0.5rem;
        }

        .tech-stack {
            background: white;
            padding: 80px 0;
        }

        .tech-item {
            text-align: center;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .tech-logo {
            font-size: 4rem;
            margin-bottom: 1rem;
            display: block;
        }

        .footer {
            background: var(--primary-color);
            color: white;
            padding: 40px 0;
            text-align: center;
        }

        .btn-demo {
            background: var(--accent-color);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 30px;
            font-size: 1.1rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn-demo:hover {
            background: #c0392b;
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }

        .hidden {
            display: none;
        }

        /* RTL/LTR Switching */
        [dir="ltr"] {
            font-family: 'Roboto', 'Cairo', sans-serif;
        }

        [dir="rtl"] {
            font-family: 'Cairo', 'Roboto', sans-serif;
        }

        .color-primary { color: var(--primary-color); }
        .color-secondary { color: var(--secondary-color); }
        .color-success { color: var(--success-color); }
        .color-warning { color: var(--warning-color); }
        .color-danger { color: var(--accent-color); }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .section-title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Language Toggle -->
    <div class="language-toggle">
        <button onclick="toggleLanguage()" id="langBtn">
            <i class="fas fa-globe me-2"></i>
            <span id="langText">English</span>
        </button>
    </div>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <h1 class="hero-title" data-ar="نظام إدارة المطعم المتكامل" data-en="Complete Restaurant Management System">
                نظام إدارة المطعم المتكامل
            </h1>
            <p class="hero-subtitle" data-ar="حل شامل ومتطور لإدارة جميع عمليات المطعم بكفاءة وسهولة" data-en="A comprehensive and advanced solution for managing all restaurant operations efficiently and easily">
                حل شامل ومتطور لإدارة جميع عمليات المطعم بكفاءة وسهولة
            </p>
            <div class="mt-4">
                <a href="dashboard" class="btn-demo">
                    <i class="fas fa-rocket me-2"></i>
                    <span data-ar="تجربة النظام" data-en="Try the System">تجربة النظام</span>
                </a>
                <a href="#features" class="btn-demo">
                    <i class="fas fa-info-circle me-2"></i>
                    <span data-ar="اكتشف المميزات" data-en="Discover Features">اكتشف المميزات</span>
                </a>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item">
                        <span class="stat-number">15+</span>
                        <div class="stat-label" data-ar="واجهة متخصصة" data-en="Specialized Interface">واجهة متخصصة</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item">
                        <span class="stat-number">50+</span>
                        <div class="stat-label" data-ar="ميزة متقدمة" data-en="Advanced Feature">ميزة متقدمة</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item">
                        <span class="stat-number">100%</span>
                        <div class="stat-label" data-ar="متجاوب مع الأجهزة" data-en="Device Responsive">متجاوب مع الأجهزة</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="stat-item">
                        <span class="stat-number">24/7</span>
                        <div class="stat-label" data-ar="عمل مستمر" data-en="Continuous Operation">عمل مستمر</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Features Section -->
    <section id="features" class="py-5">
        <div class="container">
            <h2 class="section-title" data-ar="المميزات الرئيسية" data-en="Main Features">المميزات الرئيسية</h2>

            <div class="row">
                <!-- Order Management -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <i class="fas fa-clipboard-list feature-icon color-primary"></i>
                        <h3 class="feature-title" data-ar="إدارة الطلبات المتقدمة" data-en="Advanced Order Management">إدارة الطلبات المتقدمة</h3>
                        <p class="feature-description" data-ar="نظام شامل لإدارة جميع أنواع الطلبات: طلبات الطاولات، الطلبات الخارجية، والدرايف ثرو مع إمكانية تعليق الدفع وإضافة منتجات للطلبات المعلقة" data-en="Comprehensive system for managing all types of orders: table orders, takeaway orders, and drive-through with payment suspension and adding products to suspended orders">
                            نظام شامل لإدارة جميع أنواع الطلبات: طلبات الطاولات، الطلبات الخارجية، والدرايف ثرو مع إمكانية تعليق الدفع وإضافة منتجات للطلبات المعلقة
                        </p>
                    </div>
                </div>

                <!-- Multi-Interface System -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <i class="fas fa-users-cog feature-icon color-secondary"></i>
                        <h3 class="feature-title" data-ar="واجهات متعددة متخصصة" data-en="Multiple Specialized Interfaces">واجهات متعددة متخصصة</h3>
                        <p class="feature-description" data-ar="واجهة الكاشير لمعالجة الطلبات والدفع، واجهة الشيف لمتابعة الطلبات في المطبخ، وواجهة المدير للإشراف الكامل على العمليات" data-en="Cashier interface for processing orders and payments, chef interface for tracking kitchen orders, and admin interface for complete operations oversight">
                            واجهة الكاشير لمعالجة الطلبات والدفع، واجهة الشيف لمتابعة الطلبات في المطبخ، وواجهة المدير للإشراف الكامل على العمليات
                        </p>
                    </div>
                </div>

                <!-- Real-time Notifications -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <i class="fas fa-bell feature-icon color-warning"></i>
                        <h3 class="feature-title" data-ar="إشعارات فورية" data-en="Real-time Notifications">إشعارات فورية</h3>
                        <p class="feature-description" data-ar="نظام إشعارات متطور يعمل في الوقت الفعلي لتنبيه الفريق بالطلبات الجديدة، تحديثات الحالة، والعمليات المهمة" data-en="Advanced real-time notification system to alert the team about new orders, status updates, and important operations">
                            نظام إشعارات متطور يعمل في الوقت الفعلي لتنبيه الفريق بالطلبات الجديدة، تحديثات الحالة، والعمليات المهمة
                        </p>
                    </div>
                </div>

                <!-- Backup System -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <i class="fas fa-shield-alt feature-icon color-success"></i>
                        <h3 class="feature-title" data-ar="نظام النسخ الاحتياطي المتقدم" data-en="Advanced Backup System">نظام النسخ الاحتياطي المتقدم</h3>
                        <p class="feature-description" data-ar="نسخ احتياطي تلقائي ويدوي مع إمكانية العمل في الخلفية، إرسال النسخ بالإيميل، واستعادة قاعدة البيانات بسهولة" data-en="Automatic and manual backup with background processing, email backup delivery, and easy database restoration">
                            نسخ احتياطي تلقائي ويدوي مع إمكانية العمل في الخلفية، إرسال النسخ بالإيميل، واستعادة قاعدة البيانات بسهولة
                        </p>
                    </div>
                </div>

                <!-- Reports & Analytics -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <i class="fas fa-chart-bar feature-icon color-danger"></i>
                        <h3 class="feature-title" data-ar="تقارير وتحليلات شاملة" data-en="Comprehensive Reports & Analytics">تقارير وتحليلات شاملة</h3>
                        <p class="feature-description" data-ar="تقارير مفصلة للمبيعات، الطلبات، والمنتجات مع إحصائيات يومية وشهرية وإمكانية التصدير" data-en="Detailed reports for sales, orders, and products with daily and monthly statistics and export capabilities">
                            تقارير مفصلة للمبيعات، الطلبات، والمنتجات مع إحصائيات يومية وشهرية وإمكانية التصدير
                        </p>
                    </div>
                </div>

                <!-- Responsive Design -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <i class="fas fa-mobile-alt feature-icon color-primary"></i>
                        <h3 class="feature-title" data-ar="تصميم متجاوب" data-en="Responsive Design">تصميم متجاوب</h3>
                        <p class="feature-description" data-ar="يعمل بكفاءة على جميع الأجهزة: الكمبيوتر، التابلت، والهواتف الذكية مع تحسين خاص للتابلت والآيباد" data-en="Works efficiently on all devices: computers, tablets, and smartphones with special optimization for tablets and iPads">
                            يعمل بكفاءة على جميع الأجهزة: الكمبيوتر، التابلت، والهواتف الذكية مع تحسين خاص للتابلت والآيباد
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Technical Features Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <h2 class="section-title" data-ar="المميزات التقنية" data-en="Technical Features">المميزات التقنية</h2>

            <div class="row">
                <!-- Security -->
                <div class="col-lg-3 col-md-6">
                    <div class="feature-card">
                        <i class="fas fa-lock feature-icon color-danger"></i>
                        <h3 class="feature-title" data-ar="أمان متقدم" data-en="Advanced Security">أمان متقدم</h3>
                        <ul class="feature-description">
                            <li data-ar="نظام أدوار وصلاحيات" data-en="Role-based permissions">نظام أدوار وصلاحيات</li>
                            <li data-ar="حماية CSRF" data-en="CSRF protection">حماية CSRF</li>
                            <li data-ar="تشفير البيانات" data-en="Data encryption">تشفير البيانات</li>
                            <li data-ar="جلسات آمنة" data-en="Secure sessions">جلسات آمنة</li>
                        </ul>
                    </div>
                </div>

                <!-- Performance -->
                <div class="col-lg-3 col-md-6">
                    <div class="feature-card">
                        <i class="fas fa-tachometer-alt feature-icon color-success"></i>
                        <h3 class="feature-title" data-ar="أداء عالي" data-en="High Performance">أداء عالي</h3>
                        <ul class="feature-description">
                            <li data-ar="معالجة في الخلفية" data-en="Background processing">معالجة في الخلفية</li>
                            <li data-ar="تحسين قاعدة البيانات" data-en="Database optimization">تحسين قاعدة البيانات</li>
                            <li data-ar="ذاكرة تخزين مؤقت" data-en="Caching system">ذاكرة تخزين مؤقت</li>
                            <li data-ar="استجابة سريعة" data-en="Fast response">استجابة سريعة</li>
                        </ul>
                    </div>
                </div>

                <!-- Scalability -->
                <div class="col-lg-3 col-md-6">
                    <div class="feature-card">
                        <i class="fas fa-expand-arrows-alt feature-icon color-secondary"></i>
                        <h3 class="feature-title" data-ar="قابلية التوسع" data-en="Scalability">قابلية التوسع</h3>
                        <ul class="feature-description">
                            <li data-ar="دعم عدة فروع" data-en="Multi-branch support">دعم عدة فروع</li>
                            <li data-ar="إضافة مستخدمين" data-en="Add unlimited users">إضافة مستخدمين</li>
                            <li data-ar="توسيع المنتجات" data-en="Expand products">توسيع المنتجات</li>
                            <li data-ar="نمو مع العمل" data-en="Grow with business">نمو مع العمل</li>
                        </ul>
                    </div>
                </div>

                <!-- Integration -->
                <div class="col-lg-3 col-md-6">
                    <div class="feature-card">
                        <i class="fas fa-plug feature-icon color-warning"></i>
                        <h3 class="feature-title" data-ar="التكامل" data-en="Integration">التكامل</h3>
                        <ul class="feature-description">
                            <li data-ar="طابعات الفواتير" data-en="Receipt printers">طابعات الفواتير</li>
                            <li data-ar="أنظمة الدفع" data-en="Payment systems">أنظمة الدفع</li>
                            <li data-ar="البريد الإلكتروني" data-en="Email integration">البريد الإلكتروني</li>
                            <li data-ar="التخزين السحابي" data-en="Cloud storage">التخزين السحابي</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Technology Stack -->
    <section class="tech-stack">
        <div class="container">
            <h2 class="section-title" data-ar="التقنيات المستخدمة" data-en="Technology Stack">التقنيات المستخدمة</h2>

            <div class="row">
                <div class="col-lg-2 col-md-4 col-sm-6">
                    <div class="tech-item">
                        <i class="fab fa-laravel tech-logo color-danger"></i>
                        <h5 data-ar="Laravel" data-en="Laravel">Laravel</h5>
                        <small data-ar="إطار العمل الخلفي" data-en="Backend Framework">إطار العمل الخلفي</small>
                    </div>
                </div>

                <div class="col-lg-2 col-md-4 col-sm-6">
                    <div class="tech-item">
                        <i class="fab fa-bootstrap tech-logo color-primary"></i>
                        <h5 data-ar="Bootstrap" data-en="Bootstrap">Bootstrap</h5>
                        <small data-ar="واجهة المستخدم" data-en="UI Framework">واجهة المستخدم</small>
                    </div>
                </div>

                <div class="col-lg-2 col-md-4 col-sm-6">
                    <div class="tech-item">
                        <i class="fas fa-database tech-logo color-secondary"></i>
                        <h5 data-ar="MySQL" data-en="MySQL">MySQL</h5>
                        <small data-ar="قاعدة البيانات" data-en="Database">قاعدة البيانات</small>
                    </div>
                </div>

                <div class="col-lg-2 col-md-4 col-sm-6">
                    <div class="tech-item">
                        <i class="fab fa-js-square tech-logo color-warning"></i>
                        <h5 data-ar="JavaScript" data-en="JavaScript">JavaScript</h5>
                        <small data-ar="التفاعل الأمامي" data-en="Frontend Interaction">التفاعل الأمامي</small>
                    </div>
                </div>

                <div class="col-lg-2 col-md-4 col-sm-6">
                    <div class="tech-item">
                        <i class="fab fa-php tech-logo color-primary"></i>
                        <h5 data-ar="PHP" data-en="PHP">PHP</h5>
                        <small data-ar="لغة البرمجة" data-en="Programming Language">لغة البرمجة</small>
                    </div>
                </div>

                <div class="col-lg-2 col-md-4 col-sm-6">
                    <div class="tech-item">
                        <i class="fab fa-html5 tech-logo color-danger"></i>
                        <h5 data-ar="HTML5" data-en="HTML5">HTML5</h5>
                        <small data-ar="بنية الصفحات" data-en="Page Structure">بنية الصفحات</small>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5 data-ar="نظام إدارة المطعم المتكامل" data-en="Complete Restaurant Management System">نظام إدارة المطعم المتكامل</h5>
                    <p data-ar="حل شامل ومتطور لإدارة جميع عمليات المطعم بكفاءة وسهولة" data-en="A comprehensive and advanced solution for managing all restaurant operations efficiently and easily">
                        حل شامل ومتطور لإدارة جميع عمليات المطعم بكفاءة وسهولة
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <h5 data-ar="ابدأ الآن" data-en="Get Started">ابدأ الآن</h5>
                    <a href="dashboard" class="btn-demo">
                        <i class="fas fa-play me-2"></i>
                        <span data-ar="تجربة النظام" data-en="Try the System">تجربة النظام</span>
                    </a>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="mb-0" data-ar="© 2024 نظام إدارة المطعم. جميع الحقوق محفوظة | للدعم الفني: 0096899553103" data-en="© 2024 Restaurant Management System. All rights reserved | Technical Support: 0096899553103">
                    © 2024 نظام إدارة المطعم. جميع الحقوق محفوظة |
                    <i class="fas fa-phone me-1"></i>
                    للدعم الفني: 0096899553103
                </p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        let currentLang = 'ar';

        function toggleLanguage() {
            const html = document.documentElement;
            const langBtn = document.getElementById('langBtn');
            const langText = document.getElementById('langText');

            if (currentLang === 'ar') {
                // Switch to English
                currentLang = 'en';
                html.setAttribute('lang', 'en');
                html.setAttribute('dir', 'ltr');
                langText.textContent = 'العربية';

                // Update all elements with data attributes
                updateLanguageContent('en');

                // Update Bootstrap RTL/LTR
                const bootstrapLink = document.querySelector('link[href*="bootstrap"]');
                bootstrapLink.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css';

            } else {
                // Switch to Arabic
                currentLang = 'ar';
                html.setAttribute('lang', 'ar');
                html.setAttribute('dir', 'rtl');
                langText.textContent = 'English';

                // Update all elements with data attributes
                updateLanguageContent('ar');

                // Update Bootstrap RTL/LTR
                const bootstrapLink = document.querySelector('link[href*="bootstrap"]');
                bootstrapLink.href = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css';
            }
        }

        function updateLanguageContent(lang) {
            const elements = document.querySelectorAll('[data-ar][data-en]');
            elements.forEach(element => {
                if (lang === 'ar') {
                    element.textContent = element.getAttribute('data-ar');
                } else {
                    element.textContent = element.getAttribute('data-en');
                }
            });
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all feature cards
        document.addEventListener('DOMContentLoaded', () => {
            const cards = document.querySelectorAll('.feature-card, .tech-item');
            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        });

        // Add hover effects for tech items
        document.querySelectorAll('.tech-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>