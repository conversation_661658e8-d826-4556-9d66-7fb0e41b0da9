<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة #{{ $invoice->invoice_number }}</title>
    <style>
        @page {
            size: 56mm auto;
            margin: 0;
        }
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 3mm;
            font-size: 8px;
            width: 50mm;
        }
        .receipt {
            width: 100%;
        }
        .header {
            text-align: center;
            margin-bottom: 3mm;
            border-bottom: 1px dashed #000;
            padding-bottom: 2mm;
        }
        .header h1 {
            font-size: 12px;
            margin: 0 0 1mm 0;
        }
        .header p {
            margin: 0;
            font-size: 8px;
        }
        .info {
            margin-bottom: 3mm;
            font-size: 8px;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1mm;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 3mm;
            font-size: 8px;
        }
        table th, table td {
            text-align: right;
            padding: 0.5mm;
        }
        table th {
            border-bottom: 1px solid #000;
        }
        .totals {
            margin-top: 2mm;
            border-top: 1px dashed #000;
            padding-top: 2mm;
            font-size: 8px;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1mm;
        }
        .footer {
            text-align: center;
            margin-top: 3mm;
            border-top: 1px dashed #000;
            padding-top: 2mm;
            font-size: 8px;
        }
        .no-print {
            display: none;
        }
        @media print {
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="no-print" style="padding: 10px; background: #f0f0f0; margin-bottom: 10px;">
        <button onclick="window.print()">طباعة</button>
        <a href="{{ route('invoices.show', $invoice->id) }}">العودة</a>
    </div>

    <div class="receipt">
        <div class="header">
            @php
                $restaurantInfo = \App\Models\Setting::getRestaurantInfo();
                $logo = $restaurantInfo['logo'];
                $printLogo = \App\Models\Setting::get('print_logo', true);
            @endphp

            @if($printLogo && $logo)
                <div style="text-align: center; margin-bottom: 6px;">
                    <img src="{{ asset('storage/' . $logo) }}" alt="شعار المطعم" style="max-height: 20px; max-width: 100px;">
                </div>
            @endif

            <h1>{{ $restaurantInfo['name'] }}</h1>
            <p>{{ $restaurantInfo['address'] }}</p>
            <p>هاتف: {{ $restaurantInfo['phone'] }}</p>
            @if($restaurantInfo['email'])
            <p>{{ $restaurantInfo['email'] }}</p>
            @endif
            @if($restaurantInfo['tax_number'])
            <p>الرقم الضريبي: {{ $restaurantInfo['tax_number'] }}</p>
            @endif
            <p>فاتورة ضريبية مبسطة</p>
            <p>رقم الفاتورة: {{ $invoice->invoice_number }}</p>
            <p>التاريخ: {{ $invoice->created_at->format('Y-m-d H:i') }}</p>
        </div>

        <div class="info">
            <div class="info-row">
                <span>رقم الطلب:</span>
                <span>{{ $invoice->order->id ?? 'غير محدد' }}</span>
            </div>
            <div class="info-row">
                <span>
                    @if($invoice->order && $invoice->order->table)
                        الطاولة:
                    @elseif($invoice->order && $invoice->order->order_type == 'takeaway')
                        نوع الطلب:
                    @endif
                </span>
                <span>
                    @if($invoice->order && $invoice->order->table)
                        {{ $invoice->order->table->name }}
                    @elseif($invoice->order && $invoice->order->order_type == 'takeaway')
                        طلب خارجي
                    @endif
                </span>
            </div>
            @if($invoice->order && $invoice->order->order_type == 'takeaway' && $invoice->order->car_number)
            <div class="info-row">
                <span>رقم السيارة:</span>
                <span>{{ $invoice->order->car_number }}</span>
            </div>
            @endif
            @if($invoice->order && $invoice->order->order_type == 'takeaway' && $invoice->order->customer_phone)
            <div class="info-row">
                <span>رقم الهاتف:</span>
                <span>{{ $invoice->order->customer_phone }}</span>
            </div>
            @endif
            <div class="info-row">
                <span>طريقة الدفع:</span>
                <span>
                    @if($invoice->payment_method == 'cash')
                        نقداً
                    @elseif($invoice->payment_method == 'card')
                        بطاقة
                    @else
                        {{ $invoice->payment_method }}
                    @endif
                </span>
            </div>
        </div>

        <table>
            <thead>
                <tr>
                    <th style="width: 40%;">المنتج</th>
                    <th style="width: 20%;">السعر</th>
                    <th style="width: 15%;">الكمية</th>
                    <th style="width: 25%;">المجموع</th>
                </tr>
            </thead>
            <tbody>
                @if($invoice->order && $invoice->order->orderItems->count() > 0)
                    @foreach($invoice->order->orderItems as $item)
                    <tr>
                        <td>
                            @if($item->product)
                                {{ $item->product->name }}
                                @if($item->notes)
                                    <br><small>{{ $item->notes }}</small>
                                @endif
                            @else
                                منتج محذوف
                            @endif
                        </td>
                        <td>{{ number_format($item->unit_price, 2) }} {{ $restaurantInfo['currency'] }}</td>
                        <td>{{ $item->quantity }}</td>
                        <td>{{ number_format($item->subtotal, 2) }} {{ $restaurantInfo['currency'] }}</td>
                    </tr>
                    @endforeach
                @endif
            </tbody>
        </table>

        <div class="totals">
            <div class="total-row">
                <span>المبلغ الإجمالي:</span>
                <span>{{ number_format($invoice->total_amount ?? 0, 2) }} {{ $restaurantInfo['currency'] }}</span>
            </div>
            @if($invoice->tax_amount && $invoice->tax_amount > 0)
            <div class="total-row">
                <span>ضريبة القيمة المضافة ({{ $restaurantInfo['tax_rate'] }}%):</span>
                <span>{{ number_format($invoice->tax_amount, 2) }} {{ $restaurantInfo['currency'] }}</span>
            </div>
            @endif
            @if($invoice->discount_amount && $invoice->discount_amount > 0)
            <div class="total-row">
                <span>الخصم:</span>
                <span>{{ number_format($invoice->discount_amount, 2) }} {{ $restaurantInfo['currency'] }}</span>
            </div>
            @endif
            <div class="total-row">
                <span>المبلغ النهائي:</span>
                <span>{{ number_format($invoice->final_amount ?? $invoice->total_amount ?? 0, 2) }} {{ $restaurantInfo['currency'] }}</span>
            </div>
            <div class="total-row">
                <span>المبلغ المدفوع:</span>
                <span>{{ number_format($invoice->paid_amount ?? 0, 2) }} {{ $restaurantInfo['currency'] }}</span>
            </div>
            @if($invoice->remaining_amount && $invoice->remaining_amount > 0)
            <div class="total-row">
                <span>المبلغ المتبقي:</span>
                <span>{{ number_format($invoice->remaining_amount, 2) }} {{ $restaurantInfo['currency'] }}</span>
            </div>
            @endif
        </div>

        <div class="footer">
            <p>شكراً لزيارتكم!</p>
            <p>نتطلع لرؤيتكم مرة أخرى</p>
            <p>{{ now()->format('Y-m-d H:i:s') }}</p>
        </div>
    </div>
</body>
</html>
