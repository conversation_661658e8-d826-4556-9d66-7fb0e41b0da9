<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\Product;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all categories
        $categories = Category::all();
        
        // Define products for each category
        $productsByCategory = [
            'مقبلات' => [
                ['name' => 'حمص', 'description' => 'حمص مع زيت زيتون وصنوبر', 'price' => 25.00],
                ['name' => 'متبل', 'description' => 'متبل باذنجان مع طحينة', 'price' => 25.00],
                ['name' => 'تبولة', 'description' => 'سلطة البقدونس مع البرغل', 'price' => 30.00],
                ['name' => 'فتوش', 'description' => 'سلطة الخضار مع الخبز المحمص', 'price' => 30.00],
                ['name' => 'بابا غنوج', 'description' => 'سلطة الباذنجان المشوي', 'price' => 25.00],
                ['name' => 'سمبوسك', 'description' => 'عجينة مقلية محشوة باللحم', 'price' => 35.00],
                ['name' => 'كبة', 'description' => 'كبة مقلية محشوة باللحم والصنوبر', 'price' => 40.00],
            ],
            'سلطات' => [
                ['name' => 'سلطة خضراء', 'description' => 'خضروات طازجة مع صلصة الليمون', 'price' => 35.00],
                ['name' => 'سلطة سيزر', 'description' => 'خس روماني مع صلصة السيزر والدجاج', 'price' => 45.00],
                ['name' => 'سلطة يونانية', 'description' => 'خضروات مع جبنة الفيتا والزيتون', 'price' => 40.00],
                ['name' => 'سلطة الشمندر', 'description' => 'شمندر مع جبنة الماعز', 'price' => 35.00],
            ],
            'أطباق رئيسية' => [
                ['name' => 'كبسة لحم', 'description' => 'أرز بخاري مع قطع اللحم', 'price' => 85.00],
                ['name' => 'مندي دجاج', 'description' => 'أرز مندي مع دجاج مشوي', 'price' => 75.00],
                ['name' => 'برياني', 'description' => 'أرز برياني مع اللحم والبهارات', 'price' => 90.00],
                ['name' => 'مقلوبة', 'description' => 'أرز مع الباذنجان والدجاج', 'price' => 80.00],
                ['name' => 'ملوخية', 'description' => 'ملوخية مع الدجاج والأرز', 'price' => 70.00],
            ],
            'مشاوي' => [
                ['name' => 'شيش طاووق', 'description' => 'قطع دجاج مشوية متبلة', 'price' => 65.00],
                ['name' => 'كباب', 'description' => 'لحم مفروم مشوي مع البهارات', 'price' => 70.00],
                ['name' => 'كفتة', 'description' => 'لحم مفروم مشوي مع البصل والبقدونس', 'price' => 70.00],
                ['name' => 'ريش', 'description' => 'ريش غنم مشوية', 'price' => 120.00],
                ['name' => 'مشاوي مشكلة', 'description' => 'تشكيلة من المشاوي المختلفة', 'price' => 150.00],
            ],
            'حلويات' => [
                ['name' => 'كنافة', 'description' => 'كنافة بالجبن والقطر', 'price' => 40.00],
                ['name' => 'بقلاوة', 'description' => 'بقلاوة بالفستق الحلبي', 'price' => 35.00],
                ['name' => 'قطايف', 'description' => 'قطايف محشوة بالقشطة أو الجوز', 'price' => 30.00],
                ['name' => 'أم علي', 'description' => 'حلوى مصرية من العجين والحليب والمكسرات', 'price' => 35.00],
            ],
            'مشروبات ساخنة' => [
                ['name' => 'قهوة عربية', 'description' => 'قهوة عربية مع الهيل', 'price' => 15.00],
                ['name' => 'شاي', 'description' => 'شاي مع النعناع', 'price' => 10.00],
                ['name' => 'قهوة تركية', 'description' => 'قهوة تركية مركزة', 'price' => 15.00],
            ],
            'مشروبات باردة' => [
                ['name' => 'كولا', 'description' => 'مشروب غازي', 'price' => 10.00],
                ['name' => 'سفن أب', 'description' => 'مشروب غازي', 'price' => 10.00],
                ['name' => 'مياه معدنية', 'description' => 'مياه معدنية نقية', 'price' => 5.00],
            ],
            'عصائر طازجة' => [
                ['name' => 'عصير برتقال', 'description' => 'عصير برتقال طازج', 'price' => 20.00],
                ['name' => 'عصير مانجو', 'description' => 'عصير مانجو طازج', 'price' => 25.00],
                ['name' => 'عصير فراولة', 'description' => 'عصير فراولة طازج', 'price' => 25.00],
                ['name' => 'ليموناضة', 'description' => 'عصير ليمون منعش', 'price' => 15.00],
            ],
        ];
        
        // Create products for each category
        foreach ($categories as $category) {
            $products = $productsByCategory[$category->name] ?? [];
            
            foreach ($products as $productData) {
                Product::create([
                    'name' => $productData['name'],
                    'description' => $productData['description'],
                    'price' => $productData['price'],
                    'category_id' => $category->id,
                    'is_available' => true,
                ]);
            }
        }
        
        // Create some random products
        Product::factory()->count(10)->create();
    }
}
