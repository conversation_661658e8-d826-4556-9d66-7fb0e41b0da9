@extends('layouts.app')

@section('title', 'إدارة المنتجات')

@section('content')
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>إدارة المنتجات</h2>
            <p class="text-muted">إجمالي المنتجات: {{ $products->total() }}</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('products.create') }}" class="btn btn-primary">
                <i class="fas fa-plus-circle me-1"></i> إضافة منتج جديد
            </a>
        </div>
    </div>

    <!-- فلاتر البحث والتصفية -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('products.index') }}" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="{{ request('search') }}" placeholder="البحث في المنتجات...">
                </div>
                <div class="col-md-2">
                    <label for="category_id" class="form-label">الفئة</label>
                    <select class="form-select" id="category_id" name="category_id">
                        <option value="">جميع الفئات</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}" {{ request('category_id') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="availability" class="form-label">الحالة</label>
                    <select class="form-select" id="availability" name="availability">
                        <option value="">جميع الحالات</option>
                        <option value="available" {{ request('availability') == 'available' ? 'selected' : '' }}>متاح</option>
                        <option value="unavailable" {{ request('availability') == 'unavailable' ? 'selected' : '' }}>غير متاح</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="sort_by" class="form-label">ترتيب حسب</label>
                    <select class="form-select" id="sort_by" name="sort_by">
                        <option value="name" {{ request('sort_by') == 'name' ? 'selected' : '' }}>الاسم</option>
                        <option value="price" {{ request('sort_by') == 'price' ? 'selected' : '' }}>السعر</option>
                        <option value="created_at" {{ request('sort_by') == 'created_at' ? 'selected' : '' }}>تاريخ الإنشاء</option>
                    </select>
                </div>
                <div class="col-md-1">
                    <label for="sort_order" class="form-label">الاتجاه</label>
                    <select class="form-select" id="sort_order" name="sort_order">
                        <option value="asc" {{ request('sort_order') == 'asc' ? 'selected' : '' }}>تصاعدي</option>
                        <option value="desc" {{ request('sort_order') == 'desc' ? 'selected' : '' }}>تنازلي</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="per_page" class="form-label">عدد النتائج</label>
                    <select class="form-select" id="per_page" name="per_page">
                        <option value="15" {{ request('per_page') == 15 ? 'selected' : '' }}>15</option>
                        <option value="25" {{ request('per_page') == 25 ? 'selected' : '' }}>25</option>
                        <option value="30" {{ request('per_page', 30) == 30 ? 'selected' : '' }}>30</option>
                        <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50</option>
                        <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100</option>
                    </select>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i> بحث
                    </button>
                    <a href="{{ route('products.index') }}" class="btn btn-secondary">
                        <i class="fas fa-times me-1"></i> مسح الفلاتر
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- عرض النتائج -->
    <div class="card shadow-sm">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">قائمة المنتجات</h5>
            <div class="text-muted">
                عرض {{ $products->firstItem() ?? 0 }} - {{ $products->lastItem() ?? 0 }} من {{ $products->total() }} منتج
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>الصورة</th>
                            <th>الاسم</th>
                            <th>التصنيف</th>
                            <th>السعر</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($products as $product)
                        <tr>
                            <td>{{ $product->id }}</td>
                            <td>
                                @if($product->image)
                                    <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}" width="50" height="50" class="img-thumbnail">
                                @else
                                    <span class="badge bg-secondary">لا توجد صورة</span>
                                @endif
                            </td>
                            <td>{{ $product->name }}</td>
                            <td>{{ $product->category->name ?? 'غير مصنف' }}</td>
                            <td>{{ number_format($product->price, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                            <td>
                                <form action="{{ route('products.toggle-availability', $product->id) }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('PATCH')
                                    <button type="submit" class="btn btn-sm {{ $product->is_available ? 'btn-success' : 'btn-danger' }}">
                                        {{ $product->is_available ? 'متاح' : 'غير متاح' }}
                                    </button>
                                </form>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('products.show', $product->id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('products.edit', $product->id) }}" class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ $product->id }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>

                                <!-- Modal for Delete Confirmation -->
                                <div class="modal fade" id="deleteModal{{ $product->id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ $product->id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ $product->id }}">تأكيد الحذف</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                هل أنت متأكد من رغبتك في حذف المنتج <strong>{{ $product->name }}</strong>؟
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                <form action="{{ route('products.destroy', $product->id) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-danger">حذف</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center">لا توجد منتجات</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        @if($products->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">
                    عرض {{ $products->firstItem() }} - {{ $products->lastItem() }} من {{ $products->total() }} منتج
                </div>
                <div>
                    {{ $products->appends(request()->query())->links('custom-pagination') }}
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

<script>
// تحديث الصفحة عند تغيير عدد النتائج
document.getElementById('per_page').addEventListener('change', function() {
    this.form.submit();
});

// تحديث الصفحة عند تغيير الترتيب
document.getElementById('sort_by').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('sort_order').addEventListener('change', function() {
    this.form.submit();
});
</script>
@endsection
