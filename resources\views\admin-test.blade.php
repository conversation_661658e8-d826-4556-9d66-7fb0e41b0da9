<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صفحة مدير الاختبار</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 2.5em;
        }
        .section {
            margin-bottom: 25px;
            padding: 20px;
            border-radius: 10px;
            border-right: 5px solid #667eea;
        }
        .info {
            background-color: #e7f3ff;
        }
        .success {
            background-color: #d4edda;
            border-color: #28a745;
        }
        .error {
            background-color: #f8d7da;
            border-color: #dc3545;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffc107;
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            text-decoration: none;
            display: inline-block;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #e83e8c);
        }
        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-top: 4px solid #667eea;
        }
        .status-icon {
            font-size: 1.5em;
            margin-left: 10px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            border: 1px solid #dee2e6;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 5px;
            border-radius: 3px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛠️ صفحة مدير الاختبار</h1>
            <p>اختبار شامل لنظام النسخ الاحتياطي</p>
        </div>

        <!-- معلومات المستخدم -->
        <div class="section info">
            <h2>👤 معلومات المستخدم الحالي</h2>
            @auth
                @php $user = auth()->user(); @endphp
                <div class="grid">
                    <div class="card">
                        <h3>البيانات الأساسية</h3>
                        <p><strong>الاسم:</strong> {{ $user->name }}</p>
                        <p><strong>البريد الإلكتروني:</strong> {{ $user->email }}</p>
                        <p><strong>معرف المستخدم:</strong> {{ $user->id }}</p>
                        <p><strong>تاريخ الإنشاء:</strong> {{ $user->created_at->format('Y-m-d H:i:s') }}</p>
                    </div>
                    <div class="card">
                        <h3>معلومات الدور</h3>
                        <p><strong>معرف الدور:</strong> {{ $user->role_id }}</p>
                        <p><strong>اسم الدور:</strong> {{ $user->role }}</p>
                        <p><strong>هل هو مدير؟</strong> 
                            @if($user->hasRole('admin'))
                                <span class="status-icon">✅</span> نعم
                            @else
                                <span class="status-icon">❌</span> لا
                            @endif
                        </p>
                        <p><strong>هل هو مدير مطعم؟</strong> 
                            @if($user->hasRole('manager'))
                                <span class="status-icon">✅</span> نعم
                            @else
                                <span class="status-icon">❌</span> لا
                            @endif
                        </p>
                    </div>
                </div>
            @else
                <div class="section error">
                    <h3>❌ غير مسجل دخول</h3>
                    <p>يجب تسجيل الدخول للوصول لهذه الصفحة</p>
                    <a href="{{ route('login') }}" class="btn">تسجيل الدخول</a>
                </div>
            @endauth
        </div>

        @auth
        <!-- اختبار الصلاحيات -->
        <div class="section">
            <h2>🔐 اختبار الصلاحيات</h2>
            @php
                $canBackup = $user->hasRole('admin') || $user->hasRole('manager');
                $roleDetails = $user->getUserRole();
            @endphp
            
            @if($canBackup)
                <div class="section success">
                    <h3><span class="status-icon">✅</span> لديك صلاحية للنسخ الاحتياطي</h3>
                    <p>يمكنك إنشاء وإدارة النسخ الاحتياطية</p>
                    <p><strong>الدور المطلوب:</strong> admin أو manager</p>
                    <p><strong>دورك الحالي:</strong> <span class="highlight">{{ $user->role }}</span></p>
                </div>
            @else
                <div class="section error">
                    <h3><span class="status-icon">❌</span> ليس لديك صلاحية للنسخ الاحتياطي</h3>
                    <p>تحتاج إلى دور admin أو manager للوصول للنسخ الاحتياطي</p>
                    <p><strong>دورك الحالي:</strong> <span class="highlight">{{ $user->role }}</span></p>
                </div>
            @endif

            @if($roleDetails)
                <div class="card">
                    <h3>تفاصيل الدور من قاعدة البيانات</h3>
                    <p><strong>معرف الدور:</strong> {{ $roleDetails->id }}</p>
                    <p><strong>اسم الدور:</strong> {{ $roleDetails->name }}</p>
                    <p><strong>وصف الدور:</strong> {{ $roleDetails->description }}</p>
                </div>
            @endif
        </div>

        <!-- اختبار النظام -->
        <div class="section">
            <h2>⚙️ اختبار النظام</h2>
            <div class="grid">
                <div class="card">
                    <h3>معلومات النظام</h3>
                    <p><strong>إصدار PHP:</strong> {{ PHP_VERSION }}</p>
                    <p><strong>إصدار Laravel:</strong> {{ app()->version() }}</p>
                    <p><strong>البيئة:</strong> {{ app()->environment() }}</p>
                    <p><strong>التوقيت:</strong> {{ now()->format('Y-m-d H:i:s') }}</p>
                </div>
                
                <div class="card">
                    <h3>مجلد النسخ الاحتياطية</h3>
                    @php
                        $backupDir = storage_path('app/backups');
                        $dirExists = \Illuminate\Support\Facades\File::exists($backupDir);
                        $dirWritable = is_writable($backupDir);
                    @endphp
                    <p><strong>المسار:</strong> {{ $backupDir }}</p>
                    <p><strong>هل موجود؟</strong> 
                        @if($dirExists)
                            <span class="status-icon">✅</span> نعم
                        @else
                            <span class="status-icon">❌</span> لا
                        @endif
                    </p>
                    <p><strong>قابل للكتابة؟</strong> 
                        @if($dirWritable)
                            <span class="status-icon">✅</span> نعم
                        @else
                            <span class="status-icon">❌</span> لا
                        @endif
                    </p>
                </div>
            </div>
        </div>

        <!-- اختبار إنشاء نسخة احتياطية -->
        @if($canBackup)
        <div class="section">
            <h2>💾 اختبار إنشاء نسخة احتياطية</h2>
            @php
                try {
                    // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
                    if (!$dirExists) {
                        \Illuminate\Support\Facades\File::makeDirectory($backupDir, 0755, true);
                    }
                    
                    // إنشاء ملف نسخة احتياطية تجريبية
                    $timestamp = now()->format('Y-m-d_H-i-s');
                    $filename = "admin_test_backup_{$timestamp}.sql";
                    $filepath = storage_path("app/backups/{$filename}");
                    
                    $content = "-- نسخة احتياطية تجريبية من صفحة مدير الاختبار\n";
                    $content .= "-- التاريخ: " . now()->format('Y-m-d H:i:s') . "\n";
                    $content .= "-- المستخدم: {$user->name} (ID: {$user->id})\n";
                    $content .= "-- الدور: {$user->role}\n";
                    $content .= "-- البريد الإلكتروني: {$user->email}\n";
                    $content .= "\n-- إحصائيات النظام:\n";
                    $content .= "-- عدد المستخدمين: " . \App\Models\User::count() . "\n";
                    
                    if (class_exists('\App\Models\Order')) {
                        $content .= "-- عدد الطلبات: " . \App\Models\Order::count() . "\n";
                    }
                    
                    if (class_exists('\App\Models\Product')) {
                        $content .= "-- عدد المنتجات: " . \App\Models\Product::count() . "\n";
                    }
                    
                    $content .= "\n-- تم إنشاء هذه النسخة للاختبار فقط من صفحة مدير الاختبار\n";
                    $content .= "-- يمكن حذف هذا الملف بأمان\n";
                    
                    \Illuminate\Support\Facades\File::put($filepath, $content);
                    
                    // إنشاء سجل في قاعدة البيانات
                    $backup = \App\Models\Backup::create([
                        'filename' => $filename,
                        'type' => 'database',
                        'status' => 'completed',
                        'description' => 'نسخة احتياطية تجريبية من صفحة مدير الاختبار',
                        'created_by' => $user->id,
                        'is_scheduled' => false,
                        'started_at' => now(),
                        'completed_at' => now(),
                        'file_size' => \Illuminate\Support\Facades\File::size($filepath),
                    ]);
                    
                    $testSuccess = true;
                    $testMessage = 'تم إنشاء النسخة الاحتياطية بنجاح!';
                    $backupDetails = [
                        'معرف النسخة' => $backup->id,
                        'اسم الملف' => $backup->filename,
                        'حجم الملف' => $backup->formatted_size,
                        'مسار الملف' => $filepath,
                        'تاريخ الإنشاء' => $backup->created_at->format('Y-m-d H:i:s'),
                        'الحالة' => $backup->status,
                        'النوع' => $backup->type
                    ];
                    
                } catch (\Exception $e) {
                    $testSuccess = false;
                    $testMessage = 'فشل في إنشاء النسخة الاحتياطية';
                    $errorDetails = [
                        'نوع الخطأ' => get_class($e),
                        'رسالة الخطأ' => $e->getMessage(),
                        'الملف' => $e->getFile(),
                        'السطر' => $e->getLine(),
                        'التتبع' => $e->getTraceAsString()
                    ];
                }
            @endphp
            
            @if($testSuccess)
                <div class="section success">
                    <h3><span class="status-icon">✅</span> {{ $testMessage }}</h3>
                    <div class="grid">
                        @foreach($backupDetails as $key => $value)
                            <p><strong>{{ $key }}:</strong> {{ $value }}</p>
                        @endforeach
                    </div>
                    <a href="{{ route('backup.index') }}" class="btn btn-success">عرض النسخ الاحتياطية</a>
                </div>
            @else
                <div class="section error">
                    <h3><span class="status-icon">❌</span> {{ $testMessage }}</h3>
                    <div class="card">
                        <h4>تفاصيل الخطأ:</h4>
                        @foreach($errorDetails as $key => $value)
                            <p><strong>{{ $key }}:</strong></p>
                            <pre>{{ $value }}</pre>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
        @endif

        <!-- أزرار التنقل -->
        <div class="section">
            <h2>🔗 روابط مفيدة</h2>
            <a href="{{ route('dashboard') }}" class="btn">لوحة التحكم</a>
            @if($canBackup)
                <a href="{{ route('backup.index') }}" class="btn btn-success">النسخ الاحتياطية</a>
            @endif
            <a href="{{ route('test.backup') }}" class="btn btn-warning">صفحة الاختبار المتقدمة</a>
            <a href="{{ route('simple.test') }}" class="btn btn-warning">الاختبار البسيط</a>
        </div>
        @endauth
    </div>
</body>
</html>
