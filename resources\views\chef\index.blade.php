@extends('layouts.chef')

@section('title', 'واجهة الشيف')

@section('styles')
<style>
    .chef-container {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 56px);
    }

    .chef-header {
        display: flex;
        flex-direction: column;
        gap: 15px;
        padding: 10px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }

    .chef-header > div:first-child {
        text-align: center;
    }

    .chef-header h2 {
        font-size: 1.5rem;
        margin-bottom: 5px;
    }

    /* Tablet and larger screens */
    @media (min-width: 768px) {
        .chef-header {
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
        }

        .chef-header > div:first-child {
            text-align: right;
        }

        .chef-header h2 {
            font-size: 2rem;
        }
    }

    .chef-main {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .orders-column {
        display: flex;
        flex-direction: column;
        padding: 10px;
        border-bottom: 1px solid #dee2e6;
        overflow-y: auto;
        max-height: 50vh;
    }

    .orders-column:last-child {
        border-bottom: none;
    }

    /* Tablet and larger screens */
    @media (min-width: 768px) {
        .chef-main {
            flex-direction: row;
        }

        .orders-column {
            flex: 1;
            padding: 15px;
            border-right: 1px solid #dee2e6;
            border-bottom: none;
            max-height: none;
        }

        .orders-column:last-child {
            border-right: none;
        }
    }

    .column-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid #dee2e6;
    }

    .column-title {
        font-size: 1.2rem;
        font-weight: bold;
    }

    .column-count {
        background-color: #6c757d;
        color: white;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
    }

    .order-card {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 15px;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .order-card:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }

    .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }

    .order-number {
        font-weight: bold;
        font-size: 1.1rem;
    }

    .order-time {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .order-info {
        padding: 10px 15px;
        border-bottom: 1px solid #dee2e6;
    }

    .order-info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
    }

    .order-info-label {
        font-weight: bold;
        color: #6c757d;
    }

    .order-items {
        padding: 10px 15px;
    }

    .order-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .order-item:last-child {
        border-bottom: none;
    }

    .item-name {
        font-weight: bold;
    }

    .item-quantity {
        background-color: #e9ecef;
        border-radius: 4px;
        padding: 2px 8px;
        font-weight: bold;
    }

    .item-notes {
        font-size: 0.85rem;
        color: #6c757d;
        margin-top: 3px;
    }

    .order-actions {
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 10px 15px;
        background-color: #f8f9fa;
        border-top: 1px solid #dee2e6;
    }

    .btn-action {
        width: 100%;
        padding: 8px 12px;
        font-size: 0.9rem;
    }

    /* Mobile landscape and larger */
    @media (min-width: 576px) {
        .order-actions {
            flex-direction: row;
            justify-content: space-between;
            gap: 5px;
        }

        .btn-action {
            flex: 1;
            margin: 0 5px;
        }

        .btn-action:first-child {
            margin-right: 0;
        }

        .btn-action:last-child {
            margin-left: 0;
        }
    }

    .pending-column .column-header {
        border-bottom-color: #dc3545;
    }

    .pending-column .column-count {
        background-color: #dc3545;
    }

    .preparing-column .column-header {
        border-bottom-color: #fd7e14;
    }

    .preparing-column .column-count {
        background-color: #fd7e14;
    }

    .ready-column .column-header {
        border-bottom-color: #28a745;
    }

    .ready-column .column-count {
        background-color: #28a745;
    }

    .order-type-badge {
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .order-type-dine_in {
        background-color: #cfe2ff;
        color: #0d6efd;
    }

    .order-type-takeaway {
        background-color: #d1e7dd;
        color: #198754;
    }

    .no-orders {
        text-align: center;
        padding: 30px 0;
        color: #6c757d;
    }

    .no-orders i {
        font-size: 3rem;
        margin-bottom: 15px;
        opacity: 0.5;
    }

    /* تنسيقات للوضع الليلي */
    body.dark-mode .chef-header,
    body.dark-mode .order-header,
    body.dark-mode .order-actions {
        background-color: #2a2a2a;
        border-color: #444;
    }

    body.dark-mode .order-card {
        background-color: #1e1e1e;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    body.dark-mode .order-info,
    body.dark-mode .order-items {
        border-color: #444;
    }

    body.dark-mode .order-item {
        border-color: #333;
    }

    body.dark-mode .item-quantity {
        background-color: #333;
    }

    body.dark-mode .order-type-dine_in {
        background-color: #0d47a1;
        color: #e0e0e0;
    }

    body.dark-mode .order-type-takeaway {
        background-color: #1b5e20;
        color: #e0e0e0;
    }

    /* تنسيقات مؤشرات التحديث */
    #autoUpdateIndicator {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
        animation: fadeInOut 2s ease-in-out;
    }

    @keyframes fadeInOut {
        0% { opacity: 0; }
        50% { opacity: 1; }
        100% { opacity: 0; }
    }

    .btn-success {
        transition: background-color 0.5s ease;
    }

    /* تأثير الوميض للطلبات الجديدة */
    @keyframes newOrderPulse {
        0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); transform: scale(1); }
        50% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); transform: scale(1.03); }
        100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); transform: scale(1); }
    }

    .new-order-highlight {
        animation: newOrderPulse 1s ease-in-out infinite;
        border: 2px solid #dc3545 !important;
        background-color: rgba(220, 53, 69, 0.05);
    }
</style>
@endsection

@section('content')
<div class="chef-container">
    <div class="chef-header">
        <div>
            <h2>واجهة الشيف</h2>
            <p class="text-muted">
                {{ now()->format('Y/m/d') }} - {{ auth()->user()->name }}
                <span id="autoUpdateIndicator" class="badge bg-success d-none">
                    <i class="fas fa-sync-alt fa-spin me-1"></i> تحديث تلقائي
                </span>
            </p>
        </div>
        <div>
            <button class="btn btn-primary" id="refreshOrdersBtn">
                <i class="fas fa-sync-alt me-1"></i> <span id="refreshBtnText">تحديث الطلبات</span>
                <span id="refreshSpinner" class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
            </button>
            <button class="btn btn-secondary" id="settingsBtn">
                <i class="fas fa-cog me-1"></i> الإعدادات
            </button>
        </div>
    </div>

    <div class="chef-main">
        <!-- عمود الطلبات قيد الانتظار -->
        <div class="orders-column pending-column">
            <div class="column-header">
                <div class="column-title">قيد الانتظار / قيد التنفيذ</div>
                <div class="column-count" id="pendingCount">{{ count($pendingOrders) }}</div>
            </div>
            <div id="pendingOrdersContainer">
                @if(count($pendingOrders) > 0)
                    @foreach($pendingOrders as $order)
                        <div class="order-card" data-id="{{ $order->id }}">
                            <div class="order-header">
                                <div class="order-number">#{{ $order->id }}</div>
                                <div class="order-time">{{ $order->created_at->diffForHumans() }}</div>
                            </div>
                            <div class="order-info">
                                <div class="order-info-row">
                                    <div class="order-info-label">النوع:</div>
                                    <div class="order-type-badge order-type-{{ $order->order_type }}">
                                        {{ $order->order_type == 'dine_in' ? 'طلب داخلي' : 'طلب خارجي' }}
                                    </div>
                                </div>
                                <div class="order-info-row">
                                    <div class="order-info-label">{{ $order->order_type == 'dine_in' ? 'الطاولة:' : 'العميل:' }}</div>
                                    <div>{{ $order->order_type == 'dine_in' ? ($order->table ? $order->table->name : '-') : ($order->customer_name ?? '-') }}</div>
                                </div>
                                @if($order->order_type == 'takeaway')
                                <div class="order-info-row">
                                    <div class="order-info-label">رقم الهاتف:</div>
                                    <div>{{ $order->customer_phone ?? '-' }}</div>
                                </div>
                                @if($order->car_number)
                                <div class="order-info-row">
                                    <div class="order-info-label">رقم السيارة:</div>
                                    <div>{{ $order->car_number }}</div>
                                </div>
                                @endif
                                @endif
                                <div class="order-info-row">
                                    <div class="order-info-label">النادل:</div>
                                    <div>{{ $order->user ? $order->user->name : '-' }}</div>
                                </div>
                            </div>
                            <div class="order-items">
                                @foreach($order->orderItems as $item)
                                    <div class="order-item">
                                        <div>
                                            <div class="item-name">{{ $item->product->name }}</div>
                                            @if($item->notes)
                                                <div class="item-notes">{{ $item->notes }}</div>
                                            @endif
                                        </div>
                                        <div class="item-quantity">{{ $item->quantity }}</div>
                                    </div>
                                @endforeach
                            </div>
                            <div class="order-actions">
                                <button class="btn btn-warning btn-action start-preparing-btn" data-id="{{ $order->id }}">
                                    <i class="fas fa-utensils me-1"></i> بدء التحضير
                                </button>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="no-orders">
                        <i class="fas fa-clipboard-list"></i>
                        <p>لا توجد طلبات قيد الانتظار أو قيد التنفيذ</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- عمود الطلبات قيد التحضير -->
        <div class="orders-column preparing-column">
            <div class="column-header">
                <div class="column-title">قيد التحضير</div>
                <div class="column-count" id="preparingCount">{{ count($preparingOrders) }}</div>
            </div>
            <div id="preparingOrdersContainer">
                @if(count($preparingOrders) > 0)
                    @foreach($preparingOrders as $order)
                        <div class="order-card" data-id="{{ $order->id }}">
                            <div class="order-header">
                                <div class="order-number">#{{ $order->id }}</div>
                                <div class="order-time">{{ $order->created_at->diffForHumans() }}</div>
                            </div>
                            <div class="order-info">
                                <div class="order-info-row">
                                    <div class="order-info-label">النوع:</div>
                                    <div class="order-type-badge order-type-{{ $order->order_type }}">
                                        {{ $order->order_type == 'dine_in' ? 'طلب داخلي' : 'طلب خارجي' }}
                                    </div>
                                </div>
                                <div class="order-info-row">
                                    <div class="order-info-label">{{ $order->order_type == 'dine_in' ? 'الطاولة:' : 'العميل:' }}</div>
                                    <div>{{ $order->order_type == 'dine_in' ? ($order->table ? $order->table->name : '-') : ($order->customer_name ?? '-') }}</div>
                                </div>
                                @if($order->order_type == 'takeaway')
                                <div class="order-info-row">
                                    <div class="order-info-label">رقم الهاتف:</div>
                                    <div>{{ $order->customer_phone ?? '-' }}</div>
                                </div>
                                @if($order->car_number)
                                <div class="order-info-row">
                                    <div class="order-info-label">رقم السيارة:</div>
                                    <div>{{ $order->car_number }}</div>
                                </div>
                                @endif
                                @endif
                                <div class="order-info-row">
                                    <div class="order-info-label">النادل:</div>
                                    <div>{{ $order->user ? $order->user->name : '-' }}</div>
                                </div>
                            </div>
                            <div class="order-items">
                                @foreach($order->orderItems as $item)
                                    <div class="order-item">
                                        <div>
                                            <div class="item-name">{{ $item->product->name }}</div>
                                            @if($item->notes)
                                                <div class="item-notes">{{ $item->notes }}</div>
                                            @endif
                                        </div>
                                        <div class="item-quantity">{{ $item->quantity }}</div>
                                    </div>
                                @endforeach
                            </div>
                            <div class="order-actions">
                                <button class="btn btn-success btn-action mark-ready-btn" data-id="{{ $order->id }}">
                                    <i class="fas fa-check me-1"></i> تم التحضير
                                </button>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="no-orders">
                        <i class="fas fa-utensils"></i>
                        <p>لا توجد طلبات قيد التحضير</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- عمود الطلبات الجاهزة -->
        <div class="orders-column ready-column">
            <div class="column-header">
                <div class="column-title">جاهزة للتسليم</div>
                <div class="column-count" id="readyCount">{{ count($readyOrders) }}</div>
            </div>
            <div id="readyOrdersContainer">
                @if(count($readyOrders) > 0)
                    @foreach($readyOrders as $order)
                        <div class="order-card" data-id="{{ $order->id }}">
                            <div class="order-header">
                                <div class="order-number">#{{ $order->id }}</div>
                                <div class="order-time">{{ $order->created_at->diffForHumans() }}</div>
                            </div>
                            <div class="order-info">
                                <div class="order-info-row">
                                    <div class="order-info-label">النوع:</div>
                                    <div class="order-type-badge order-type-{{ $order->order_type }}">
                                        {{ $order->order_type == 'dine_in' ? 'طلب داخلي' : 'طلب خارجي' }}
                                    </div>
                                </div>
                                <div class="order-info-row">
                                    <div class="order-info-label">{{ $order->order_type == 'dine_in' ? 'الطاولة:' : 'العميل:' }}</div>
                                    <div>{{ $order->order_type == 'dine_in' ? ($order->table ? $order->table->name : '-') : ($order->customer_name ?? '-') }}</div>
                                </div>
                                @if($order->order_type == 'takeaway')
                                <div class="order-info-row">
                                    <div class="order-info-label">رقم الهاتف:</div>
                                    <div>{{ $order->customer_phone ?? '-' }}</div>
                                </div>
                                @if($order->car_number)
                                <div class="order-info-row">
                                    <div class="order-info-label">رقم السيارة:</div>
                                    <div>{{ $order->car_number }}</div>
                                </div>
                                @endif
                                @endif
                                <div class="order-info-row">
                                    <div class="order-info-label">النادل:</div>
                                    <div>{{ $order->user ? $order->user->name : '-' }}</div>
                                </div>
                            </div>
                            <div class="order-items">
                                @foreach($order->orderItems as $item)
                                    <div class="order-item">
                                        <div>
                                            <div class="item-name">{{ $item->product->name }}</div>
                                            @if($item->notes)
                                                <div class="item-notes">{{ $item->notes }}</div>
                                            @endif
                                        </div>
                                        <div class="item-quantity">{{ $item->quantity }}</div>
                                    </div>
                                @endforeach
                            </div>
                            <div class="order-actions">
                                <button class="btn btn-primary btn-action mark-delivered-btn" data-id="{{ $order->id }}">
                                    <i class="fas fa-truck me-1"></i> تم التسليم
                                </button>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="no-orders">
                        <i class="fas fa-check-circle"></i>
                        <p>لا توجد طلبات جاهزة للتسليم</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Modal الإعدادات -->
<div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="settingsModalLabel">إعدادات النظام</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="list-group">
                    <a href="{{ route('dashboard') }}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-tachometer-alt me-2 text-success"></i>
                                <strong>لوحة التحكم</strong>
                            </div>
                            <i class="fas fa-chevron-left"></i>
                        </div>
                        <small class="text-muted">الانتقال إلى لوحة التحكم الرئيسية</small>
                    </a>
                    <div class="list-group-item">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="darkModeSwitch">
                            <label class="form-check-label" for="darkModeSwitch">الوضع الليلي</label>
                        </div>
                        <small class="text-muted">تفعيل/تعطيل الوضع الليلي للواجهة</small>
                    </div>
                    <div class="list-group-item">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="soundsSwitch" checked>
                            <label class="form-check-label" for="soundsSwitch">أصوات التنبيهات</label>
                        </div>
                        <small class="text-muted">تفعيل/تعطيل أصوات التنبيهات</small>
                    </div>
                    <div class="list-group-item">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoRefreshSwitch" checked>
                            <label class="form-check-label" for="autoRefreshSwitch">تحديث تلقائي</label>
                        </div>
                        <small class="text-muted">تحديث الطلبات تلقائيًا كل 30 ثانية</small>
                    </div>
                    <div class="list-group-item">
                        <div class="mb-2"><strong>حجم الخط</strong></div>
                        <div class="btn-group w-100" role="group">
                            <input type="radio" class="btn-check" name="fontSizeOptions" id="fontSizeSmall" autocomplete="off">
                            <label class="btn btn-outline-primary" for="fontSizeSmall">صغير</label>

                            <input type="radio" class="btn-check" name="fontSizeOptions" id="fontSizeMedium" autocomplete="off" checked>
                            <label class="btn btn-outline-primary" for="fontSizeMedium">متوسط</label>

                            <input type="radio" class="btn-check" name="fontSizeOptions" id="fontSizeLarge" autocomplete="off">
                            <label class="btn btn-outline-primary" for="fontSizeLarge">كبير</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="saveSettingsBtn">حفظ التغييرات</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal تفاصيل الطلب -->
<div class="modal fade" id="orderDetailsModal" tabindex="-1" aria-labelledby="orderDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="orderDetailsModalLabel">تفاصيل الطلب #<span id="orderDetailId"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>نوع الطلب:</strong> <span id="orderDetailType"></span></p>
                        <p><strong>الطاولة/العميل:</strong> <span id="orderDetailTableOrCustomer"></span></p>
                        <p><strong>الوقت:</strong> <span id="orderDetailTime"></span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>الحالة:</strong> <span id="orderDetailStatus"></span></p>
                        <p><strong>النادل:</strong> <span id="orderDetailWaiter"></span></p>
                        <p><strong>ملاحظات:</strong> <span id="orderDetailNotes"></span></p>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped" id="orderItemsTable">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>المنتج</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>المجموع</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody id="orderItemsTableBody">
                            <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                        </tbody>
                        <tfoot>
                            <tr>
                                <th colspan="4" class="text-end">المجموع:</th>
                                <th id="orderDetailTotal">0.00 ر.س</th>
                                <th></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                <div class="mt-3">
                    <div class="mb-3">
                        <label for="orderStatusNotes" class="form-label">إضافة ملاحظات</label>
                        <textarea class="form-control" id="orderStatusNotes" rows="2" placeholder="أضف ملاحظات للطلب..."></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer" id="orderDetailActions">
                <!-- سيتم إضافة أزرار الإجراءات هنا بواسطة JavaScript -->
            </div>
        </div>
    </div>
</div>
@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // متغيرات عامة
        let autoRefreshInterval;
        const newOrderSound = new Audio('{{ asset('sounds/new-order.mp3') }}');

        // طلب إذن الإشعارات
        requestNotificationPermission();

        // تحميل الإعدادات المحفوظة
        loadSavedSettings();

        // دالة لطلب إذن الإشعارات
        function requestNotificationPermission() {
            if ("Notification" in window) {
                if (Notification.permission !== "granted" && Notification.permission !== "denied") {
                    Notification.requestPermission().then(permission => {
                        if (permission === "granted") {
                            console.log("تم منح إذن الإشعارات");
                        }
                    });
                }
            }
        }

        // بدء التحديث التلقائي إذا كان مفعلاً
        startAutoRefresh();

        // إضافة مستمعي الأحداث للأزرار
        document.getElementById('refreshOrdersBtn').addEventListener('click', loadOrders);
        document.getElementById('settingsBtn').addEventListener('click', function() {
            const settingsModal = new bootstrap.Modal(document.getElementById('settingsModal'));
            settingsModal.show();
        });

        // إضافة مستمعي الأحداث لأزرار تغيير حالة الطلب
        document.addEventListener('click', function(event) {
            // بدء تحضير طلب
            if (event.target.classList.contains('start-preparing-btn') ||
                event.target.parentElement.classList.contains('start-preparing-btn')) {
                const button = event.target.classList.contains('start-preparing-btn') ?
                    event.target : event.target.parentElement;
                const orderId = button.getAttribute('data-id');
                updateOrderStatus(orderId, 'preparing');
            }

            // تعيين طلب كجاهز
            if (event.target.classList.contains('mark-ready-btn') ||
                event.target.parentElement.classList.contains('mark-ready-btn')) {
                const button = event.target.classList.contains('mark-ready-btn') ?
                    event.target : event.target.parentElement;
                const orderId = button.getAttribute('data-id');
                updateOrderStatus(orderId, 'ready');
            }

            // تعيين طلب كتم تسليمه
            if (event.target.classList.contains('mark-delivered-btn') ||
                event.target.parentElement.classList.contains('mark-delivered-btn')) {
                const button = event.target.classList.contains('mark-delivered-btn') ?
                    event.target : event.target.parentElement;
                const orderId = button.getAttribute('data-id');
                updateOrderStatus(orderId, 'delivered');
            }

            // فتح تفاصيل الطلب عند النقر على بطاقة الطلب
            if (event.target.closest('.order-card') &&
                !event.target.closest('.btn-action')) {
                const card = event.target.closest('.order-card');
                const orderId = card.getAttribute('data-id');
                showOrderDetails(orderId);
            }
        });

        // إضافة مستمع الحدث لزر حفظ الإعدادات
        document.getElementById('saveSettingsBtn').addEventListener('click', function() {
            // حفظ إعدادات الوضع الليلي
            const darkMode = document.getElementById('darkModeSwitch').checked;
            localStorage.setItem('darkMode', darkMode);

            // حفظ إعدادات أصوات التنبيهات
            const sounds = document.getElementById('soundsSwitch').checked;
            localStorage.setItem('sounds', sounds);

            // حفظ إعدادات التحديث التلقائي
            const autoRefresh = document.getElementById('autoRefreshSwitch').checked;
            localStorage.setItem('autoRefresh', autoRefresh);

            // حفظ إعدادات حجم الخط
            let fontSize = 'medium';
            if (document.getElementById('fontSizeSmall').checked) {
                fontSize = 'small';
            } else if (document.getElementById('fontSizeLarge').checked) {
                fontSize = 'large';
            }
            localStorage.setItem('fontSize', fontSize);

            // تطبيق الإعدادات
            applySettings();

            // إعادة تشغيل التحديث التلقائي إذا كان مفعلاً
            startAutoRefresh();

            alert('تم حفظ الإعدادات بنجاح');
            bootstrap.Modal.getInstance(document.getElementById('settingsModal')).hide();
        });

        // دالة لتحميل الطلبات
        function loadOrders() {
            // إظهار مؤشر التحميل
            const refreshBtn = document.getElementById('refreshOrdersBtn');
            const refreshBtnText = document.getElementById('refreshBtnText');
            const refreshSpinner = document.getElementById('refreshSpinner');

            refreshBtn.disabled = true;
            refreshBtnText.textContent = 'جاري التحديث...';
            refreshSpinner.classList.remove('d-none');

            fetch('{{ route("chef.orders.active") }}')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateOrdersUI(data, true); // true لتشغيل الصوت إذا كان هناك طلبات جديدة
                    } else {
                        console.error('Error loading orders:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                })
                .finally(() => {
                    // إخفاء مؤشر التحميل
                    refreshBtn.disabled = false;
                    refreshBtnText.textContent = 'تحديث الطلبات';
                    refreshSpinner.classList.add('d-none');

                    // إضافة تأثير وميض للإشارة إلى اكتمال التحديث
                    refreshBtn.classList.add('btn-success');
                    refreshBtn.classList.remove('btn-primary');

                    setTimeout(() => {
                        refreshBtn.classList.remove('btn-success');
                        refreshBtn.classList.add('btn-primary');
                    }, 1000);
                });
        }

        // دالة لتحديث واجهة المستخدم بالطلبات
        function updateOrdersUI(data, playSound = true) {
            console.log('بدء تحديث واجهة المستخدم...');

            // حفظ عدد الطلبات الحالية قبل التحديث
            const currentPendingCount = parseInt(document.getElementById('pendingCount').textContent);

            // حفظ معرفات الطلبات الحالية
            const currentPendingIds = Array.from(document.querySelectorAll('#pendingOrdersContainer .order-card'))
                .map(card => card.getAttribute('data-id'));

            console.log('معرفات الطلبات الحالية في الواجهة:', currentPendingIds);

            // تحديث عدادات الطلبات
            document.getElementById('pendingCount').textContent = data.pendingOrders.length;
            document.getElementById('preparingCount').textContent = data.preparingOrders.length;
            document.getElementById('readyCount').textContent = data.readyOrders.length;

            // تحديث قائمة الطلبات قيد الانتظار
            updateOrdersList('pendingOrdersContainer', data.pendingOrders, 'pending');

            // تحديث قائمة الطلبات قيد التحضير
            updateOrdersList('preparingOrdersContainer', data.preparingOrders, 'preparing');

            // تحديث قائمة الطلبات الجاهزة
            updateOrdersList('readyOrdersContainer', data.readyOrders, 'ready');

            // تحديد الطلبات الجديدة (الطلبات التي لم تكن موجودة في التحديث السابق)
            const newOrderIds = data.pendingOrders
                .map(order => order.id.toString())
                .filter(id => !currentPendingIds.includes(id));

            console.log('معرفات الطلبات الجديدة:', newOrderIds);

            // تشغيل صوت وعرض إشعار إذا كان هناك طلبات جديدة
            const soundsEnabled = localStorage.getItem('sounds') !== 'false';
            if (newOrderIds.length > 0) {
                console.log(`تم العثور على ${newOrderIds.length} طلب جديد`);

                // إضافة تأثير وميض للطلبات الجديدة
                newOrderIds.forEach(id => {
                    const orderCard = document.querySelector(`#pendingOrdersContainer .order-card[data-id="${id}"]`);
                    if (orderCard) {
                        console.log(`إضافة تأثير وميض للطلب رقم ${id}`);
                        // إضافة فئة للتأثير المرئي
                        orderCard.classList.add('new-order-highlight');

                        // إزالة الفئة بعد 5 ثوانٍ
                        setTimeout(() => {
                            orderCard.classList.remove('new-order-highlight');
                        }, 5000);
                    } else {
                        console.log(`لم يتم العثور على عنصر HTML للطلب رقم ${id}`);
                    }
                });

                // تشغيل صوت التنبيه إذا كان مسموحًا
                if (playSound && soundsEnabled) {
                    console.log('تشغيل صوت التنبيه');
                    newOrderSound.play();

                    // عرض إشعار إذا كان المتصفح يدعم الإشعارات
                    if ("Notification" in window && Notification.permission === "granted") {
                        console.log('عرض إشعار المتصفح');
                        new Notification("طلبات جديدة", {
                            body: `لديك ${newOrderIds.length} طلب جديد قيد الانتظار`,
                            icon: "{{ asset('favicon.ico') }}"
                        });
                    }
                }
            } else {
                console.log('لم يتم العثور على طلبات جديدة');
            }

            console.log('اكتمل تحديث واجهة المستخدم');
        }

        // دالة لتحديث قائمة الطلبات
        function updateOrdersList(containerId, orders, status) {
            const container = document.getElementById(containerId);

            // حفظ معرفات الطلبات الحالية
            const currentOrderIds = Array.from(container.querySelectorAll('.order-card'))
                .map(card => card.getAttribute('data-id'));

            // حفظ معرفات الطلبات الجديدة
            const newOrderIds = orders.map(order => order.id.toString());

            // إذا لم تكن هناك طلبات، عرض رسالة "لا توجد طلبات"
            if (orders.length === 0) {
                let noOrdersIcon = 'clipboard-list';
                if (status === 'preparing') noOrdersIcon = 'utensils';
                if (status === 'ready') noOrdersIcon = 'check-circle';

                let noOrdersMessage = 'لا توجد طلبات قيد الانتظار أو قيد التنفيذ';
                if (status === 'preparing') noOrdersMessage = 'لا توجد طلبات قيد التحضير';
                if (status === 'ready') noOrdersMessage = 'لا توجد طلبات جاهزة للتسليم';

                container.innerHTML = `
                    <div class="no-orders">
                        <i class="fas fa-${noOrdersIcon}"></i>
                        <p>${noOrdersMessage}</p>
                    </div>
                `;
                return;
            }

            // إزالة رسالة "لا توجد طلبات" إذا كانت موجودة
            const noOrdersElement = container.querySelector('.no-orders');
            if (noOrdersElement) {
                container.removeChild(noOrdersElement);
            }

            // إزالة الطلبات التي لم تعد موجودة
            currentOrderIds.forEach(id => {
                if (!newOrderIds.includes(id)) {
                    const orderCard = container.querySelector(`.order-card[data-id="${id}"]`);
                    if (orderCard) {
                        // إضافة تأثير تلاشي قبل الإزالة
                        orderCard.style.opacity = '0';
                        orderCard.style.transform = 'translateY(20px)';
                        orderCard.style.transition = 'opacity 0.5s ease, transform 0.5s ease';

                        // إزالة العنصر بعد انتهاء التأثير
                        setTimeout(() => {
                            orderCard.remove();
                        }, 500);
                    }
                }
            });

            // إنشاء قاموس للطلبات الحالية للوصول السريع
            const orderCardsMap = {};
            currentOrderIds.forEach(id => {
                const card = container.querySelector(`.order-card[data-id="${id}"]`);
                if (card) {
                    orderCardsMap[id] = card;
                }
            });

            // إضافة أو تحديث الطلبات
            orders.forEach((order, index) => {
                const orderId = order.id.toString();
                let orderCard = orderCardsMap[orderId];
                const isNewOrder = !orderCard;

                // إذا كان الطلب غير موجود، قم بإنشائه
                if (isNewOrder) {
                    orderCard = document.createElement('div');
                    orderCard.className = 'order-card';
                    orderCard.setAttribute('data-id', orderId);

                    // تعيين أسلوب البداية للتأثير
                    orderCard.style.opacity = '0';
                    orderCard.style.transform = 'translateY(-20px)';
                    orderCard.style.transition = 'opacity 0.5s ease, transform 0.5s ease';

                    // إضافة الطلب الجديد في المكان المناسب (حسب الترتيب)
                    let inserted = false;

                    // ترتيب الطلبات حسب وقت الإنشاء (الأحدث في الأعلى)
                    const orderCards = Array.from(container.querySelectorAll('.order-card'));
                    for (let i = 0; i < orderCards.length; i++) {
                        const existingCard = orderCards[i];
                        const existingId = existingCard.getAttribute('data-id');
                        const existingIndex = orders.findIndex(o => o.id.toString() === existingId);

                        if (existingIndex > index) {
                            container.insertBefore(orderCard, existingCard);
                            inserted = true;
                            break;
                        }
                    }

                    if (!inserted) {
                        container.appendChild(orderCard);
                    }

                    // تفعيل التأثير بعد إضافة العنصر للصفحة
                    setTimeout(() => {
                        orderCard.style.opacity = '1';
                        orderCard.style.transform = 'translateY(0)';
                    }, 10);
                }

                // تحديث محتوى الطلب
                const orderTime = new Date(order.created_at);
                const now = new Date();
                const diffTime = Math.abs(now - orderTime);
                const diffMinutes = Math.floor(diffTime / (1000 * 60));
                const diffHours = Math.floor(diffMinutes / 60);

                let timeAgo;
                if (diffHours > 0) {
                    timeAgo = `منذ ${diffHours} ساعة ${diffMinutes % 60} دقيقة`;
                } else {
                    timeAgo = `منذ ${diffMinutes} دقيقة`;
                }

                // تحديد نص الزر حسب حالة الطلب أو حالة التحضير
                let actionButton = '';

                // إذا كان الطلب معلق، نستخدم حالة التحضير لتحديد الزر المناسب
                if (order.status === 'suspended' && order.preparation_status) {
                    switch (order.preparation_status) {
                        case 'pending':
                            actionButton = `
                                <button class="btn btn-warning btn-action start-preparing-btn" data-id="${orderId}">
                                    <i class="fas fa-utensils me-1"></i> بدء التحضير
                                </button>
                            `;
                            break;
                        case 'preparing':
                            actionButton = `
                                <button class="btn btn-success btn-action mark-ready-btn" data-id="${orderId}">
                                    <i class="fas fa-check me-1"></i> تم التحضير
                                </button>
                            `;
                            break;
                        case 'ready':
                            actionButton = `
                                <button class="btn btn-primary btn-action mark-delivered-btn" data-id="${orderId}">
                                    <i class="fas fa-truck me-1"></i> تم التسليم
                                </button>
                            `;
                            break;
                    }
                } else {
                    // استخدام الحالة العادية
                    if (status === 'pending' || status === 'suspended') {
                        actionButton = `
                            <button class="btn btn-warning btn-action start-preparing-btn" data-id="${orderId}">
                                <i class="fas fa-utensils me-1"></i> بدء التحضير
                            </button>
                        `;
                    } else if (status === 'preparing') {
                        actionButton = `
                            <button class="btn btn-success btn-action mark-ready-btn" data-id="${orderId}">
                                <i class="fas fa-check me-1"></i> تم التحضير
                            </button>
                        `;
                    } else if (status === 'ready') {
                        actionButton = `
                            <button class="btn btn-primary btn-action mark-delivered-btn" data-id="${orderId}">
                                <i class="fas fa-truck me-1"></i> تم التسليم
                            </button>
                        `;
                    }
                }

                // إنشاء عناصر الطلب
                let orderItems = '';
                order.order_items.forEach(item => {
                    orderItems += `
                        <div class="order-item">
                            <div>
                                <div class="item-name">${item.product.name}</div>
                                ${item.notes ? `<div class="item-notes">${item.notes}</div>` : ''}
                            </div>
                            <div class="item-quantity">${item.quantity}</div>
                        </div>
                    `;
                });

                // تحديث محتوى بطاقة الطلب
                orderCard.innerHTML = `
                    <div class="order-header">
                        <div class="order-number">#${order.id}</div>
                        <div class="order-time">${timeAgo}</div>
                    </div>
                    <div class="order-info">
                        <div class="order-info-row">
                            <div class="order-info-label">النوع:</div>
                            <div class="order-type-badge order-type-${order.order_type}">
                                ${order.order_type === 'dine_in' ? 'طلب داخلي' : 'طلب خارجي'}
                            </div>
                        </div>
                        <div class="order-info-row">
                            <div class="order-info-label">${order.order_type === 'dine_in' ? 'الطاولة:' : 'العميل:'}</div>
                            <div>${order.order_type === 'dine_in' ? (order.table ? order.table.name : '-') : (order.customer_name || '-')}</div>
                        </div>
                        <div class="order-info-row">
                            <div class="order-info-label">النادل:</div>
                            <div>${order.user ? order.user.name : '-'}</div>
                        </div>
                    </div>
                    <div class="order-items">
                        ${orderItems}
                    </div>
                    <div class="order-actions">
                        ${actionButton}
                    </div>
                `;
            });
        }

        // دالة لتحديث حالة الطلب
        function updateOrderStatus(orderId, status) {
            const notes = document.getElementById('orderStatusNotes') ?
                document.getElementById('orderStatusNotes').value : '';

            fetch(`{{ url('chef/orders') }}/${orderId}/status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    status: status,
                    notes: notes
                })
            })
            .then(response => {
                console.log('استجابة الخادم:', response);
                if (!response.ok) {
                    throw new Error('استجابة الخادم غير ناجحة: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                console.log('بيانات الاستجابة:', data);
                if (data.success) {
                    // إغلاق نافذة التفاصيل إذا كانت مفتوحة
                    const orderDetailsModal = document.getElementById('orderDetailsModal');
                    if (orderDetailsModal && bootstrap.Modal.getInstance(orderDetailsModal)) {
                        bootstrap.Modal.getInstance(orderDetailsModal).hide();
                    }

                    // تحديث قوائم الطلبات
                    loadOrders();
                } else {
                    alert('حدث خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء تحديث حالة الطلب');
            });
        }

        // دالة لعرض تفاصيل الطلب
        function showOrderDetails(orderId) {
            fetch(`{{ url('chef/orders') }}/${orderId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const order = data.order;

                        // تعبئة بيانات الطلب في النافذة المنبثقة
                        document.getElementById('orderDetailId').textContent = order.id;
                        document.getElementById('orderDetailType').textContent = order.order_type === 'dine_in' ? 'طلب داخلي' : 'طلب خارجي';
                        document.getElementById('orderDetailTableOrCustomer').textContent = order.order_type === 'dine_in' ?
                            (order.table ? order.table.name : '-') : (order.customer_name || '-');

                        // إضافة معلومات إضافية للطلبات الخارجية
                        const leftColumn = document.querySelector('#orderDetailsModal .modal-body .row.mb-3 .col-md-6:first-child');
                        // إزالة أي معلومات إضافية سابقة
                        const existingAdditionalInfo = leftColumn.querySelectorAll('p:nth-child(n+4)');
                        existingAdditionalInfo.forEach(el => el.remove());

                        if (order.order_type === 'takeaway') {
                            if (order.customer_phone) {
                                const phoneElement = document.createElement('p');
                                phoneElement.innerHTML = `<strong>رقم الهاتف:</strong> ${order.customer_phone}`;
                                leftColumn.appendChild(phoneElement);
                            }
                            if (order.car_number) {
                                const carElement = document.createElement('p');
                                carElement.innerHTML = `<strong>رقم السيارة:</strong> ${order.car_number}`;
                                leftColumn.appendChild(carElement);
                            }
                        }

                        // تنسيق التاريخ والوقت
                        const orderDate = new Date(order.created_at);
                        document.getElementById('orderDetailTime').textContent = orderDate.toLocaleString('ar-SA');

                        // تنسيق الحالة
                        let statusText = '';
                        let statusClass = '';
                        // تحديد الحالة المعروضة (إما الحالة الفعلية أو حالة التحضير)
                        let displayStatus = order.status;

                        // إذا كان الطلب معلق ولديه حالة تحضير، نعرض كلاهما
                        if (order.status === 'suspended' && order.preparation_status) {
                            switch (order.preparation_status) {
                                case 'pending':
                                    statusText = 'معلق (قيد الانتظار)';
                                    statusClass = 'bg-secondary';
                                    break;
                                case 'preparing':
                                    statusText = 'معلق (قيد التحضير)';
                                    statusClass = 'bg-info';
                                    break;
                                case 'ready':
                                    statusText = 'معلق (جاهز للتسليم)';
                                    statusClass = 'bg-success';
                                    break;
                                case 'delivered':
                                    statusText = 'معلق (تم التحضير)';
                                    statusClass = 'bg-primary';
                                    break;
                                default:
                                    statusText = 'معلق';
                                    statusClass = 'bg-secondary';
                            }
                        } else {
                            // عرض الحالة العادية
                            switch (order.status) {
                                case 'pending':
                                    statusText = 'قيد الانتظار';
                                    statusClass = 'bg-warning';
                                    break;
                                case 'preparing':
                                    statusText = 'قيد التحضير';
                                    statusClass = 'bg-info';
                                    break;
                                case 'ready':
                                    statusText = 'جاهز للتسليم';
                                    statusClass = 'bg-success';
                                    break;
                                case 'delivered':
                                    statusText = 'تم التسليم';
                                    statusClass = 'bg-primary';
                                    break;
                                case 'completed':
                                    statusText = 'مكتمل';
                                    statusClass = 'bg-success';
                                    break;
                                case 'cancelled':
                                    statusText = 'ملغي';
                                    statusClass = 'bg-danger';
                                    break;
                                case 'suspended':
                                    statusText = 'معلق';
                                    statusClass = 'bg-secondary';
                                    break;
                            }
                        }
                        document.getElementById('orderDetailStatus').innerHTML = `<span class="badge ${statusClass}">${statusText}</span>`;

                        document.getElementById('orderDetailWaiter').textContent = order.user ? order.user.name : '-';
                        document.getElementById('orderDetailNotes').textContent = order.notes || '-';

                        // تعبئة عناصر الطلب
                        const tableBody = document.getElementById('orderItemsTableBody');
                        tableBody.innerHTML = '';

                        let totalAmount = 0;
                        order.order_items.forEach((item, index) => {
                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>${index + 1}</td>
                                <td>${item.product.name}</td>
                                <td>${parseFloat(item.unit_price).toFixed(2)} ر.س</td>
                                <td>${item.quantity}</td>
                                <td>${parseFloat(item.subtotal).toFixed(2)} ر.س</td>
                                <td>${item.notes || '-'}</td>
                            `;
                            tableBody.appendChild(row);
                            totalAmount += parseFloat(item.subtotal);
                        });

                        document.getElementById('orderDetailTotal').textContent = totalAmount.toFixed(2) + ' ر.س';

                        // إضافة أزرار الإجراءات حسب حالة الطلب
                        const actionsContainer = document.getElementById('orderDetailActions');
                        actionsContainer.innerHTML = `
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        `;

                        // تحديد الزر المناسب بناءً على حالة الطلب أو حالة التحضير
                        if (order.status === 'suspended' && order.preparation_status) {
                            // استخدام حالة التحضير للطلبات المعلقة
                            switch (order.preparation_status) {
                                case 'pending':
                                    actionsContainer.innerHTML += `
                                        <button type="button" class="btn btn-warning start-preparing-btn" data-id="${order.id}">
                                            <i class="fas fa-utensils me-1"></i> بدء التحضير
                                        </button>
                                    `;
                                    break;
                                case 'preparing':
                                    actionsContainer.innerHTML += `
                                        <button type="button" class="btn btn-success mark-ready-btn" data-id="${order.id}">
                                            <i class="fas fa-check me-1"></i> تم التحضير
                                        </button>
                                    `;
                                    break;
                                case 'ready':
                                    actionsContainer.innerHTML += `
                                        <button type="button" class="btn btn-primary mark-delivered-btn" data-id="${order.id}">
                                            <i class="fas fa-truck me-1"></i> تم التسليم
                                        </button>
                                    `;
                                    break;
                            }
                        } else {
                            // استخدام الحالة العادية
                            if (order.status === 'pending' || order.status === 'suspended') {
                                actionsContainer.innerHTML += `
                                    <button type="button" class="btn btn-warning start-preparing-btn" data-id="${order.id}">
                                        <i class="fas fa-utensils me-1"></i> بدء التحضير
                                    </button>
                                `;
                            } else if (order.status === 'preparing') {
                                actionsContainer.innerHTML += `
                                    <button type="button" class="btn btn-success mark-ready-btn" data-id="${order.id}">
                                        <i class="fas fa-check me-1"></i> تم التحضير
                                    </button>
                                `;
                            } else if (order.status === 'ready') {
                                actionsContainer.innerHTML += `
                                    <button type="button" class="btn btn-primary mark-delivered-btn" data-id="${order.id}">
                                        <i class="fas fa-truck me-1"></i> تم التسليم
                                    </button>
                                `;
                            }
                        }

                        // عرض النافذة المنبثقة
                        const orderDetailsModal = new bootstrap.Modal(document.getElementById('orderDetailsModal'));
                        orderDetailsModal.show();
                    } else {
                        alert('حدث خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء تحميل تفاصيل الطلب');
                });
        }

        // دالة لتحميل الإعدادات المحفوظة
        function loadSavedSettings() {
            // تحميل إعدادات الوضع الليلي
            const darkMode = localStorage.getItem('darkMode') === 'true';
            document.getElementById('darkModeSwitch').checked = darkMode;

            // تحميل إعدادات أصوات التنبيهات
            const sounds = localStorage.getItem('sounds') !== 'false'; // افتراضيًا مفعل
            document.getElementById('soundsSwitch').checked = sounds;

            // تحميل إعدادات التحديث التلقائي
            const autoRefresh = localStorage.getItem('autoRefresh') !== 'false'; // افتراضيًا مفعل
            document.getElementById('autoRefreshSwitch').checked = autoRefresh;

            // تحميل إعدادات حجم الخط
            const fontSize = localStorage.getItem('fontSize') || 'medium';
            if (fontSize === 'small') {
                document.getElementById('fontSizeSmall').checked = true;
            } else if (fontSize === 'large') {
                document.getElementById('fontSizeLarge').checked = true;
            } else {
                document.getElementById('fontSizeMedium').checked = true;
            }

            // تطبيق الإعدادات
            applySettings();
        }

        // دالة لتطبيق الإعدادات
        function applySettings() {
            // تطبيق إعدادات الوضع الليلي
            const darkMode = localStorage.getItem('darkMode') === 'true';
            if (darkMode) {
                document.body.classList.add('dark-mode');
            } else {
                document.body.classList.remove('dark-mode');
            }

            // تطبيق إعدادات حجم الخط
            const fontSize = localStorage.getItem('fontSize') || 'medium';
            document.body.classList.remove('font-small', 'font-medium', 'font-large');
            document.body.classList.add('font-' + fontSize);
        }

        // دالة لبدء التحديث التلقائي
        function startAutoRefresh() {
            // إيقاف التحديث التلقائي الحالي إذا كان موجودًا
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }

            // لا نحتاج إلى تعيين فاصل زمني جديد هنا لأننا نستخدم الفاصل الزمني المعين في بداية الكود
            // الذي يقوم بتحديث الطلبات كل 10 ثوانٍ
        }

        // تحميل الطلبات عند تحميل الصفحة
        loadOrders();

        // تحديث الطلبات تلقائيًا كل 3 ثوانٍ
        setInterval(function() {
            if (localStorage.getItem('autoRefresh') !== 'false') {
                loadOrdersSilently();
            }
        }, 3000);

        // دالة لتحميل الطلبات بصمت (بدون تنبيهات صوتية)
        function loadOrdersSilently() {
            // إظهار مؤشر التحديث التلقائي
            const autoUpdateIndicator = document.getElementById('autoUpdateIndicator');
            autoUpdateIndicator.classList.remove('d-none');

            console.log('تحديث تلقائي للطلبات...');

            fetch('{{ route("chef.orders.active") }}')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('تم استلام البيانات بنجاح:', {
                            pendingOrders: data.pendingOrders.length,
                            preparingOrders: data.preparingOrders.length,
                            readyOrders: data.readyOrders.length
                        });

                        // طباعة معرفات الطلبات قيد الانتظار / قيد التنفيذ
                        console.log('معرفات الطلبات قيد الانتظار / قيد التنفيذ:',
                            data.pendingOrders.map(order => ({
                                id: order.id,
                                status: order.status,
                                created_at: order.created_at
                            }))
                        );

                        updateOrdersUI(data, false); // false لعدم تشغيل الصوت
                    } else {
                        console.error('Error loading orders:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                })
                .finally(() => {
                    // إخفاء مؤشر التحديث التلقائي بعد ثانيتين
                    setTimeout(() => {
                        autoUpdateIndicator.classList.add('d-none');
                    }, 2000);
                });
        }
    });
</script>
@endsection