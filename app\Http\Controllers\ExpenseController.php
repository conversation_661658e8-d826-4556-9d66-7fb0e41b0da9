<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Expense;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ExpenseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Expense::with('user')->latest();

        // البحث
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // فلترة حسب الفئة
        if ($request->filled('category')) {
            $query->byCategory($request->category);
        }

        // فلترة حسب التاريخ
        if ($request->filled('start_date') && $request->filled('end_date')) {
            $query->byDateRange($request->start_date, $request->end_date);
        }

        $expenses = $query->paginate(15);

        // إحصائيات
        $totalExpenses = Expense::sum('amount');
        $monthlyExpenses = Expense::whereMonth('expense_date', now()->month)
                                 ->whereYear('expense_date', now()->year)
                                 ->sum('amount');
        $todayExpenses = Expense::whereDate('expense_date', today())->sum('amount');

        $categories = Expense::getCategories();

        return view('expenses.index', compact(
            'expenses',
            'totalExpenses',
            'monthlyExpenses',
            'todayExpenses',
            'categories'
        ));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Expense::getCategories();
        $paymentMethods = Expense::getPaymentMethods();
        $statuses = Expense::getStatuses();

        return view('expenses.create', compact('categories', 'paymentMethods', 'statuses'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'amount' => 'required|numeric|min:0',
            'category' => 'required|string',
            'expense_date' => 'required|date',
            'payment_method' => 'required|string',
            'receipt_number' => 'nullable|string|max:255',
            'vendor' => 'nullable|string|max:255',
            'status' => 'required|string',
            'notes' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $expense = new Expense($request->all());
            $expense->user_id = Auth::id();
            $expense->save();

            return redirect()->route('expenses.index')
                ->with('success', 'تم إضافة المصروف بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء إضافة المصروف: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Expense $expense)
    {
        return view('expenses.show', compact('expense'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Expense $expense)
    {
        $categories = Expense::getCategories();
        $paymentMethods = Expense::getPaymentMethods();
        $statuses = Expense::getStatuses();

        return view('expenses.edit', compact('expense', 'categories', 'paymentMethods', 'statuses'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Expense $expense)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'amount' => 'required|numeric|min:0',
            'category' => 'required|string',
            'expense_date' => 'required|date',
            'payment_method' => 'required|string',
            'receipt_number' => 'nullable|string|max:255',
            'vendor' => 'nullable|string|max:255',
            'status' => 'required|string',
            'notes' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $expense->update($request->all());

            return redirect()->route('expenses.index')
                ->with('success', 'تم تحديث المصروف بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء تحديث المصروف: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Expense $expense)
    {
        try {
            $expense->delete();

            return redirect()->route('expenses.index')
                ->with('success', 'تم حذف المصروف بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء حذف المصروف: ' . $e->getMessage());
        }
    }

    /**
     * تصدير المصروفات
     */
    public function export(Request $request)
    {
        $query = Expense::with('user');

        // تطبيق الفلاتر
        if ($request->filled('category')) {
            $query->byCategory($request->category);
        }

        if ($request->filled('start_date') && $request->filled('end_date')) {
            $query->byDateRange($request->start_date, $request->end_date);
        }

        $expenses = $query->get();

        $csvData = [];
        $csvData[] = ['التاريخ', 'العنوان', 'الفئة', 'المبلغ', 'طريقة الدفع', 'المورد', 'الحالة', 'المستخدم'];

        foreach ($expenses as $expense) {
            $csvData[] = [
                $expense->expense_date->format('Y-m-d'),
                $expense->title,
                $expense->category_name,
                $expense->amount,
                $expense->payment_method_name,
                $expense->vendor ?? '-',
                $expense->status_name,
                $expense->user->name
            ];
        }

        $filename = 'expenses_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF)); // UTF-8 BOM

            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
