<?php

namespace Database\Factories;

use App\Models\Role;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => static::$password ??= Hash::make('password'),
            'role_id' => Role::inRandomOrder()->first()->id ?? 1,
            'remember_token' => Str::random(10),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    /**
     * Indicate that the user is an admin.
     */
    public function admin(): static
    {
        return $this->state(function (array $attributes) {
            $adminRole = Role::where('name', 'admin')->first();
            return [
                'role_id' => $adminRole ? $adminRole->id : 1,
            ];
        });
    }

    /**
     * Indicate that the user is a waiter.
     */
    public function waiter(): static
    {
        return $this->state(function (array $attributes) {
            $waiterRole = Role::where('name', 'waiter')->first();
            return [
                'role_id' => $waiterRole ? $waiterRole->id : 3,
            ];
        });
    }

    /**
     * Indicate that the user is a cashier.
     */
    public function cashier(): static
    {
        return $this->state(function (array $attributes) {
            $cashierRole = Role::where('name', 'cashier')->first();
            return [
                'role_id' => $cashierRole ? $cashierRole->id : 4,
            ];
        });
    }
}
