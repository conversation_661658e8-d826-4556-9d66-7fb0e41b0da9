<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create categories with specific names
        $categories = [
            ['name' => 'مقبلات', 'description' => 'أطباق صغيرة تقدم قبل الوجبة الرئيسية'],
            ['name' => 'سلطات', 'description' => 'سلطات طازجة ومتنوعة'],
            ['name' => 'أطباق رئيسية', 'description' => 'أطباق رئيسية متنوعة'],
            ['name' => 'مشاوي', 'description' => 'تشكيلة من المشاوي الشهية'],
            ['name' => 'حلويات', 'description' => 'حلويات شرقية وغربية'],
            ['name' => 'مشروبات ساخنة', 'description' => 'مشروبات ساخنة متنوعة'],
            ['name' => 'مشروبات باردة', 'description' => 'مشروبات باردة منعشة'],
            ['name' => 'عصائر طازجة', 'description' => 'عصائر طبيعية 100%'],
            ['name' => 'وجبات سريعة', 'description' => 'وجبات سريعة التحضير'],
            ['name' => 'أطباق جانبية', 'description' => 'أطباق تقدم مع الوجبات الرئيسية'],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
