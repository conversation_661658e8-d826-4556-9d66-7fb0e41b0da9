<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // الحصول على دور المدير
        $adminRole = Role::where('name', 'admin')->first();

        // إنشاء مستخدم مدير افتراضي
        User::create([
            'name' => 'مدير النظام',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role_id' => $adminRole->id,
        ]);
    }
}
