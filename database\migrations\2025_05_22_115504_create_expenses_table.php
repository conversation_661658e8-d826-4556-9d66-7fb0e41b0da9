<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expenses', function (Blueprint $table) {
            $table->id();
            $table->string('title'); // عنوان المصروف
            $table->text('description')->nullable(); // وصف المصروف
            $table->decimal('amount', 10, 2); // المبلغ
            $table->string('category'); // فئة المصروف (مواد خام، رواتب، إيجار، صيانة، أخرى)
            $table->date('expense_date'); // تاريخ المصروف
            $table->string('payment_method')->default('cash'); // طريقة الدفع (نقدي، بطاقة، تحويل)
            $table->string('receipt_number')->nullable(); // رقم الإيصال
            $table->string('vendor')->nullable(); // اسم المورد
            $table->string('status')->default('paid'); // الحالة (مدفوع، معلق، ملغي)
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // المستخدم الذي أضاف المصروف
            $table->text('notes')->nullable(); // ملاحظات إضافية
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expenses');
    }
};
