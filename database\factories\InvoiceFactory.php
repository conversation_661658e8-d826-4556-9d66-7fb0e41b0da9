<?php

namespace Database\Factories;

use App\Models\Invoice;
use App\Models\Order;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Invoice>
 */
class InvoiceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $order = Order::where('status', 'completed')->inRandomOrder()->first() ?? Order::factory()->completed()->create();
        $totalAmount = $order->total_amount > 0 ? $order->total_amount : $this->faker->randomFloat(2, 50, 500);
        $taxRate = 0.15; // 15% tax
        $taxAmount = $totalAmount * $taxRate;
        $discountAmount = $this->faker->optional(0.3)->randomFloat(2, 0, $totalAmount * 0.2) ?? 0;
        $finalAmount = $totalAmount + $taxAmount - $discountAmount;
        
        return [
            'order_id' => $order->id,
            'user_id' => $order->user_id ?? User::inRandomOrder()->first()->id,
            'invoice_number' => 'INV-' . $this->faker->unique()->numberBetween(1000, 9999),
            'total_amount' => $totalAmount,
            'tax_amount' => $taxAmount,
            'discount_amount' => $discountAmount,
            'final_amount' => $finalAmount,
            'payment_method' => $this->faker->randomElement(['cash', 'card', 'online']),
            'payment_status' => $this->faker->randomElement(['paid', 'pending', 'cancelled']),
            'notes' => $this->faker->optional(0.2)->sentence(),
            'created_at' => $order->updated_at,
            'updated_at' => $order->updated_at,
        ];
    }

    /**
     * Indicate that the invoice is paid.
     */
    public function paid(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => 'paid',
        ]);
    }
}
