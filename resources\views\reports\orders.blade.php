@extends('layouts.app')

@section('title', 'تقرير الطلبات')

@section('styles')
<style>
    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 20px;
    }
    .report-card {
        transition: all 0.3s;
    }
    .report-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
</style>
@endsection

@section('content')
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>تقرير الطلبات</h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('reports.sales') }}" class="btn btn-secondary">
                <i class="fas fa-chart-line me-1"></i> تقرير المبيعات
            </a>
        </div>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="{{ route('reports.orders') }}" method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ $startDate }}">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ $endDate }}">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter me-1"></i> تصفية
                    </button>
                    <a href="{{ route('reports.orders') }}" class="btn btn-secondary">
                        <i class="fas fa-redo me-1"></i> إعادة تعيين
                    </a>
                </div>
            </form>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card bg-primary text-white report-card">
                <div class="card-body">
                    <h5 class="card-title">إجمالي الطلبات</h5>
                    <p class="card-text display-6">{{ $totalOrders }}</p>
                    <p class="card-text">إجمالي المبلغ: {{ number_format($totalOrdersAmount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card bg-success text-white report-card">
                <div class="card-body">
                    <h5 class="card-title">متوسط قيمة الطلب</h5>
                    <p class="card-text display-6">{{ $totalOrders > 0 ? number_format($totalOrdersAmount / $totalOrders, 2) : 0 }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</p>
                    <p class="card-text">عدد الطلبات المكتملة: {{ $completedOrders }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">حالة الطلبات</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="orderStatusChart"></canvas>
                    </div>
                    <div class="row text-center mt-3">
                        <div class="col-md-2">
                            <div class="card bg-secondary text-white">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-0">قيد الانتظار</h6>
                                    <p class="card-text mb-0">{{ $pendingOrders }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-primary text-white">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-0">قيد التحضير</h6>
                                    <p class="card-text mb-0">{{ $preparingOrders }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-info text-white">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-0">جاهز</h6>
                                    <p class="card-text mb-0">{{ $readyOrders }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-success text-white">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-0">تم التوصيل</h6>
                                    <p class="card-text mb-0">{{ $deliveredOrders }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-success text-white">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-0">مكتمل</h6>
                                    <p class="card-text mb-0">{{ $completedOrders }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-danger text-white">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-0">ملغي</h6>
                                    <p class="card-text mb-0">{{ $cancelledOrders }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">الطلبات اليومية</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="dailyOrdersChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chair me-2"></i>
                        الطلبات حسب الطاولة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>الطاولة</th>
                                    <th>عدد الطلبات</th>
                                    <th>إجمالي المبلغ</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($ordersByTable as $tableData)
                                <tr>
                                    <td>{{ $tableData['table_name'] }}</td>
                                    <td>{{ $tableData['count'] }}</td>
                                    <td>{{ number_format($tableData['total'], 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="3" class="text-center">لا توجد بيانات</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- معلومات النتائج للطاولات -->
                    @if($ordersByTable->hasPages())
                        <div class="d-flex justify-content-between align-items-center mt-3 mb-2">
                            <div class="text-muted">
                                <small>
                                    <i class="fas fa-info-circle me-1"></i>
                                    عرض {{ $ordersByTable->firstItem() ?? 0 }} - {{ $ordersByTable->lastItem() ?? 0 }} من {{ $ordersByTable->total() }} طاولة
                                </small>
                            </div>
                            <div class="text-muted">
                                <small>
                                    <i class="fas fa-chair me-1"></i>
                                    {{ $ordersByTable->perPage() }} طاولة في الصفحة
                                </small>
                            </div>
                        </div>

                        <!-- Pagination للطاولات -->
                        <div class="d-flex justify-content-center">
                            {{ $ordersByTable->appends(request()->except('table_page'))->links('custom-pagination') }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-tie me-2"></i>
                        الطلبات حسب النادل
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>النادل</th>
                                    <th>عدد الطلبات</th>
                                    <th>إجمالي المبلغ</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($ordersByWaiter as $waiterData)
                                <tr>
                                    <td>{{ $waiterData['waiter_name'] }}</td>
                                    <td>{{ $waiterData['count'] }}</td>
                                    <td>{{ number_format($waiterData['total'], 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="3" class="text-center">لا توجد بيانات</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clipboard-list me-2"></i>
                        قائمة الطلبات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>التاريخ</th>
                                    <th>الطاولة</th>
                                    <th>النادل</th>
                                    <th>الحالة</th>
                                    <th>المبلغ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($orders as $order)
                                <tr>
                                    <td>{{ $order->id }}</td>
                                    <td>{{ $order->created_at->format('Y-m-d H:i') }}</td>
                                    <td>{{ $order->table->name ?? 'غير محدد' }}</td>
                                    <td>{{ $order->user->name ?? 'غير محدد' }}</td>
                                    <td>
                                        @if($order->status == 'pending')
                                            <span class="badge bg-secondary">قيد الانتظار</span>
                                        @elseif($order->status == 'preparing')
                                            <span class="badge bg-primary">قيد التحضير</span>
                                        @elseif($order->status == 'ready')
                                            <span class="badge bg-info">جاهز</span>
                                        @elseif($order->status == 'delivered')
                                            <span class="badge bg-success">تم التوصيل</span>
                                        @elseif($order->status == 'completed')
                                            <span class="badge bg-success">مكتمل</span>
                                        @elseif($order->status == 'cancelled')
                                            <span class="badge bg-danger">ملغي</span>
                                        @endif
                                    </td>
                                    <td>{{ number_format($order->total_amount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                    <td>
                                        <a href="{{ route('orders.show', $order->id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center">لا توجد بيانات</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- معلومات النتائج -->
                    <div class="d-flex justify-content-between align-items-center mt-3 mb-3">
                        <div class="text-muted">
                            <small>
                                <i class="fas fa-info-circle me-1"></i>
                                عرض {{ $orders->firstItem() ?? 0 }} - {{ $orders->lastItem() ?? 0 }} من {{ $orders->total() }} طلب
                            </small>
                        </div>
                        <div class="text-muted">
                            <small>
                                <i class="fas fa-clipboard-list me-1"></i>
                                {{ $orders->perPage() }} طلب في الصفحة
                            </small>
                        </div>
                    </div>

                    <!-- Pagination -->
                    @if($orders->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $orders->appends(request()->query())->links('custom-pagination') }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // حالة الطلبات
        const orderStatusCtx = document.getElementById('orderStatusChart').getContext('2d');
        const orderStatusChart = new Chart(orderStatusCtx, {
            type: 'pie',
            data: {
                labels: ['قيد الانتظار', 'قيد التحضير', 'جاهز', 'تم التوصيل', 'مكتمل', 'ملغي'],
                datasets: [{
                    data: [
                        {{ $pendingOrders }},
                        {{ $preparingOrders }},
                        {{ $readyOrders }},
                        {{ $deliveredOrders }},
                        {{ $completedOrders }},
                        {{ $cancelledOrders }}
                    ],
                    backgroundColor: ['#6c757d', '#0d6efd', '#0dcaf0', '#20c997', '#198754', '#dc3545'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // الطلبات اليومية
        const dailyOrdersCtx = document.getElementById('dailyOrdersChart').getContext('2d');
        const dailyOrdersChart = new Chart(dailyOrdersCtx, {
            type: 'bar',
            data: {
                labels: [
                    @foreach($dailyOrders as $date => $data)
                        '{{ $date }}',
                    @endforeach
                ],
                datasets: [{
                    label: 'عدد الطلبات',
                    data: [
                        @foreach($dailyOrders as $data)
                            {{ $data['count'] }},
                        @endforeach
                    ],
                    backgroundColor: '#0d6efd',
                    borderWidth: 1,
                    yAxisID: 'y'
                }, {
                    label: 'إجمالي المبلغ',
                    data: [
                        @foreach($dailyOrders as $data)
                            {{ $data['total'] }},
                        @endforeach
                    ],
                    backgroundColor: '#198754',
                    borderWidth: 1,
                    type: 'line',
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'عدد الطلبات'
                        }
                    },
                    y1: {
                        beginAtZero: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'إجمالي المبلغ ({{ \App\Models\Setting::get('currency', 'ر.س') }})'
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    });
</script>
@endsection
