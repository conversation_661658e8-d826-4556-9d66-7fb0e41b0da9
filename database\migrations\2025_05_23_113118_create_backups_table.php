<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('backups', function (Blueprint $table) {
            $table->id();
            $table->string('filename');
            $table->string('type'); // full, database, files
            $table->string('status')->default('pending'); // pending, running, completed, failed
            $table->bigInteger('file_size')->nullable(); // حجم الملف بالبايت
            $table->text('description')->nullable();
            $table->json('metadata')->nullable(); // معلومات إضافية
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->text('error_message')->nullable();
            $table->boolean('is_scheduled')->default(false); // هل هو نسخ مجدول أم يدوي
            $table->boolean('is_encrypted')->default(false);
            $table->boolean('sent_via_email')->default(false);
            $table->boolean('uploaded_to_cloud')->default(false);
            $table->timestamps();

            $table->index(['type', 'status']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('backups');
    }
};
