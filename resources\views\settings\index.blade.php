@extends('layouts.app')

@section('title', 'إعدادات النظام')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-cogs me-2"></i>
                        إعدادات النظام
                    </h3>
                    <div>
                        <a href="{{ route('settings.edit') }}" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i>
                            تعديل الإعدادات
                        </a>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="{{ route('settings.export') }}">
                                        <i class="fas fa-download me-1"></i>
                                        تصدير الإعدادات
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item text-danger" href="#" onclick="resetSettings()">
                                        <i class="fas fa-undo me-1"></i>
                                        إعادة تعيين للافتراضي
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-1"></i>
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-1"></i>
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <!-- معلومات المطعم -->
                    @if(isset($settings['restaurant']))
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-store me-2"></i>
                                معلومات المطعم
                            </h5>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold">اسم المطعم:</label>
                                <p class="text-muted">{{ $settings['restaurant']->where('key', 'restaurant_name')->first()->value ?? 'غير محدد' }}</p>
                            </div>
                            <div class="info-group">
                                <label class="fw-bold">العنوان:</label>
                                <p class="text-muted">{{ $settings['restaurant']->where('key', 'restaurant_address')->first()->value ?? 'غير محدد' }}</p>
                            </div>
                            <div class="info-group">
                                <label class="fw-bold">رقم الهاتف:</label>
                                <p class="text-muted">{{ $settings['restaurant']->where('key', 'restaurant_phone')->first()->value ?? 'غير محدد' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold">البريد الإلكتروني:</label>
                                <p class="text-muted">{{ $settings['restaurant']->where('key', 'restaurant_email')->first()->value ?? 'غير محدد' }}</p>
                            </div>
                            <div class="info-group">
                                <label class="fw-bold">الموقع الإلكتروني:</label>
                                <p class="text-muted">{{ $settings['restaurant']->where('key', 'restaurant_website')->first()->value ?? 'غير محدد' }}</p>
                            </div>
                            <div class="info-group">
                                <label class="fw-bold">الشعار:</label>
                                @php
                                    $logo = $settings['restaurant']->where('key', 'restaurant_logo')->first()->value ?? null;
                                @endphp
                                @if($logo)
                                    <div class="mt-2">
                                        <img src="{{ asset('storage/' . $logo) }}" alt="شعار المطعم" class="img-thumbnail" style="max-height: 100px;">
                                    </div>
                                @else
                                    <p class="text-muted">لم يتم رفع شعار</p>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- الإعدادات المالية -->
                    @if(isset($settings['financial']))
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-success border-bottom pb-2">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                الإعدادات المالية
                            </h5>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold">رمز العملة:</label>
                                <p class="text-muted">{{ $settings['financial']->where('key', 'currency')->first()->value ?? 'غير محدد' }}</p>
                            </div>
                            <div class="info-group">
                                <label class="fw-bold">كود العملة:</label>
                                <p class="text-muted">{{ $settings['financial']->where('key', 'currency_code')->first()->value ?? 'غير محدد' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold">معدل الضريبة:</label>
                                <p class="text-muted">{{ $settings['financial']->where('key', 'tax_rate')->first()->value ?? '0' }}%</p>
                            </div>
                            <div class="info-group">
                                <label class="fw-bold">الرقم الضريبي:</label>
                                <p class="text-muted">{{ $settings['financial']->where('key', 'tax_number')->first()->value ?? 'غير محدد' }}</p>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- إعدادات الطباعة -->
                    @if(isset($settings['printing']))
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-info border-bottom pb-2">
                                <i class="fas fa-print me-2"></i>
                                إعدادات الطباعة
                            </h5>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold">حجم الطباعة الافتراضي:</label>
                                <p class="text-muted">{{ $settings['printing']->where('key', 'default_print_size')->first()->value ?? 'غير محدد' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <label class="fw-bold">طباعة الشعار:</label>
                                <p class="text-muted">
                                    @php
                                        $printLogo = $settings['printing']->where('key', 'print_logo')->first()->value ?? '0';
                                    @endphp
                                    {{ $printLogo == '1' ? 'مفعل' : 'معطل' }}
                                </p>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لتأكيد إعادة التعيين -->
<div class="modal fade" id="resetModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد إعادة التعيين</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من أنك تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟</p>
                <p class="text-danger"><strong>تحذير:</strong> سيتم فقدان جميع التخصيصات الحالية!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form action="{{ route('settings.reset') }}" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">إعادة تعيين</button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.info-group {
    margin-bottom: 1rem;
}

.info-group label {
    display: block;
    margin-bottom: 0.25rem;
}

.info-group p {
    margin-bottom: 0;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
}
</style>

<script>
function resetSettings() {
    var resetModal = new bootstrap.Modal(document.getElementById('resetModal'));
    resetModal.show();
}
</script>
@endsection
