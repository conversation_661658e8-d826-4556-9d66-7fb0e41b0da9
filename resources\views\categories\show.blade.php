@extends('layouts.app')

@section('title', 'عرض التصنيف')

@section('content')
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>عرض التصنيف: {{ $category->name }}</h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('categories.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
            </a>
            <a href="{{ route('categories.edit', $category->id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">معلومات التصنيف</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        @if($category->image)
                            <img src="{{ asset('storage/' . $category->image) }}" alt="{{ $category->name }}" class="img-fluid rounded" style="max-height: 200px;">
                        @else
                            <div class="bg-light p-5 rounded">
                                <i class="fas fa-image fa-3x text-muted"></i>
                                <p class="mt-2 text-muted">لا توجد صورة</p>
                            </div>
                        @endif
                    </div>

                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 30%">الاسم</th>
                            <td>{{ $category->name }}</td>
                        </tr>
                        <tr>
                            <th>الوصف</th>
                            <td>{{ $category->description ?? 'لا يوجد وصف' }}</td>
                        </tr>
                        <tr>
                            <th>عدد المنتجات</th>
                            <td>{{ $category->products->count() }}</td>
                        </tr>
                        <tr>
                            <th>تاريخ الإنشاء</th>
                            <td>{{ $category->created_at->format('Y-m-d H:i') }}</td>
                        </tr>
                        <tr>
                            <th>آخر تحديث</th>
                            <td>{{ $category->updated_at->format('Y-m-d H:i') }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">المنتجات في هذا التصنيف</h5>
                    <a href="{{ route('products.create') }}?category_id={{ $category->id }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus-circle me-1"></i> إضافة منتج
                    </a>
                </div>
                <div class="card-body">
                    @if($category->products->count() > 0)
                        <div class="row">
                            @foreach($category->products as $product)
                                <div class="col-md-4 mb-3">
                                    <div class="card h-100">
                                        @if($product->image)
                                            <img src="{{ asset('storage/' . $product->image) }}" class="card-img-top" alt="{{ $product->name }}" style="height: 150px; object-fit: cover;">
                                        @else
                                            <div class="bg-light text-center p-3" style="height: 150px;">
                                                <i class="fas fa-image fa-3x text-muted mt-4"></i>
                                            </div>
                                        @endif
                                        <div class="card-body">
                                            <h5 class="card-title">{{ $product->name }}</h5>
                                            <p class="card-text text-muted small">{{ Str::limit($product->description, 50) }}</p>
                                            <p class="card-text fw-bold">{{ number_format($product->price, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</p>
                                            <div class="d-flex justify-content-between">
                                                <span class="badge {{ $product->is_available ? 'bg-success' : 'bg-danger' }}">
                                                    {{ $product->is_available ? 'متاح' : 'غير متاح' }}
                                                </span>
                                                <a href="{{ route('products.show', $product->id) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="alert alert-info">
                            لا توجد منتجات في هذا التصنيف
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
