<!--
نظام إدارة المطعم المتكامل
Restaurant Management System

Copyright (c) 2024 [اسمك أو اسم شركتك]
جميع الحقوق محفوظة - All Rights Reserved

هذا النظام محمي بموجب قوانين حقوق الطبع والنشر الدولية والمحلية
This system is protected under international and local copyright laws
-->
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ \App\Models\Setting::get('restaurant_name', config('app.name', 'نظام إدارة المطعم')) }} - @yield('title')</title>

    <!-- Copyright Notice -->
    <meta name="copyright" content="Copyright (c) 2024 نظام إدارة المطعم المتكامل">
    <meta name="author" content="مطور النظام - للدعم الفني: 0096899553103">
    <meta name="description" content="نظام إدارة المطعم المتكامل - Restaurant Management System">
    <meta name="contact" content="0096899553103">
    <meta name="robots" content="noindex, nofollow">

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
        }
        .sidebar .nav-link:hover {
            color: #fff;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .main-content {
            padding: 20px;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card-dashboard {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
        }
        .card-dashboard:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
    </style>

    @yield('styles')
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            @auth
                <!-- Sidebar -->
                <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                    <div class="position-sticky pt-3">
                        <div class="text-center mb-4">
                            @php
                                $logo = \App\Models\Setting::get('restaurant_logo');
                            @endphp
                            @if($logo)
                                <img src="{{ asset('storage/' . $logo) }}"
                                     alt="{{ \App\Models\Setting::get('restaurant_name', 'المطعم') }}"
                                     style="height: 35px; margin-bottom: 8px;"
                                     class="img-fluid">
                            @else
                                <!-- Debug: الشعار غير موجود -->
                                <div class="text-muted small">لا يوجد شعار</div>
                            @endif
                            <h5>{{ \App\Models\Setting::get('restaurant_name', 'نظام إدارة المطعم') }}</h5>
                        </div>
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}" href="{{ route('dashboard') }}">
                                    <i class="fas fa-tachometer-alt me-2"></i>
                                    لوحة التحكم
                                </a>
                            </li>

                            @if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('manager'))
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('users.*') ? 'active' : '' }}" href="{{ route('users.index') }}">
                                    <i class="fas fa-users me-2"></i>
                                    المستخدمون
                                </a>
                            </li>
                            @endif

                            @if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('manager'))
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('tables.*') ? 'active' : '' }}" href="{{ route('tables.index') }}">
                                    <i class="fas fa-chair me-2"></i>
                                    الطاولات
                                </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('categories.*') ? 'active' : '' }}" href="{{ route('categories.index') }}">
                                    <i class="fas fa-th-list me-2"></i>
                                    التصنيفات
                                </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('products.*') ? 'active' : '' }}" href="{{ route('products.index') }}">
                                    <i class="fas fa-utensils me-2"></i>
                                    المنتجات
                                </a>
                            </li>
                            @endif

                            @if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('manager') || auth()->user()->hasRole('cashier'))
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('orders.*') ? 'active' : '' }}" href="{{ route('orders.index') }}">
                                    <i class="fas fa-clipboard-list me-2"></i>
                                    الطلبات
                                </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('invoices.*') ? 'active' : '' }}" href="{{ route('invoices.index') }}">
                                    <i class="fas fa-file-invoice-dollar me-2"></i>
                                    الفواتير
                                </a>
                            </li>
                            @endif

                            @if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('manager'))
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle {{ request()->routeIs('reports.*') ? 'active' : '' }}" href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    التقارير
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="reportsDropdown">
                                    <li><a class="dropdown-item" href="{{ route('reports.sales') }}">تقرير المبيعات</a></li>
                                    <li><a class="dropdown-item" href="{{ route('reports.orders') }}">تقرير الطلبات</a></li>
                                    <li><a class="dropdown-item" href="{{ route('reports.products') }}">تقرير المنتجات</a></li>
                                </ul>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('expenses.*') ? 'active' : '' }}" href="{{ route('expenses.index') }}">
                                    <i class="fas fa-money-bill-wave me-2"></i>
                                    إدارة المصروفات
                                </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('purchases.*') ? 'active' : '' }}" href="{{ route('purchases.index') }}">
                                    <i class="fas fa-shopping-cart me-2"></i>
                                    إدارة المشتريات
                                </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('settings.*') ? 'active' : '' }}" href="{{ route('settings.index') }}">
                                    <i class="fas fa-cogs me-2"></i>
                                    إعدادات النظام
                                </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('backup.*') ? 'active' : '' }}" href="{{ route('backup.index') }}">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    النسخ الاحتياطي
                                </a>
                            </li>


                            @endif

                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle {{ request()->routeIs('waiter.*') || request()->routeIs('chef.*') ? 'active' : '' }}" href="#" id="interfacesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-desktop me-2"></i>
                                    واجهات النظام
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="interfacesDropdown">
                                    @if(auth()->user()->hasRole('cashier') || auth()->user()->hasRole('admin') || auth()->user()->hasRole('manager'))
                                    <li>
                                        <a class="dropdown-item {{ request()->routeIs('waiter.*') ? 'active' : '' }}" href="{{ route('waiter.index') }}">
                                            <i class="fas fa-concierge-bell me-2"></i>
                                            واجهة الكاشير
                                        </a>
                                    </li>
                                    @endif
                                    @if(auth()->user()->hasRole('kitchen') || auth()->user()->hasRole('admin') || auth()->user()->hasRole('manager'))
                                    <li>
                                        <a class="dropdown-item {{ request()->routeIs('chef.*') ? 'active' : '' }}" href="{{ route('chef.index') }}">
                                            <i class="fas fa-utensils me-2"></i>
                                            واجهة الشيف
                                        </a>
                                    </li>
                                    @endif
                                </ul>
                            </li>

                            @if(auth()->user()->hasRole('waiter') || auth()->user()->hasRole('cashier'))
                            <li class="nav-item d-md-none">
                                <a class="nav-link {{ request()->routeIs('waiter.*') ? 'active' : '' }}" href="{{ route('waiter.index') }}">
                                    <i class="fas fa-concierge-bell me-2"></i>
                                    واجهة النادل
                                </a>
                            </li>
                            @endif

                            @if(auth()->user()->hasRole('kitchen'))
                            <li class="nav-item d-md-none">
                                <a class="nav-link {{ request()->routeIs('chef.*') ? 'active' : '' }}" href="{{ route('chef.index') }}">
                                    <i class="fas fa-utensils me-2"></i>
                                    واجهة الشيف
                                </a>
                            </li>
                            @endif
                        </ul>
                    </div>
                </div>
            @endauth

            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- Top Navbar -->
                <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
                    <div class="container-fluid">
                        @auth
                            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".sidebar" aria-controls="sidebar" aria-expanded="false" aria-label="Toggle navigation">
                                <span class="navbar-toggler-icon"></span>
                            </button>
                        @endauth

                        <a class="navbar-brand" href="{{ url('/') }}">{{ \App\Models\Setting::get('restaurant_name', config('app.name', 'نظام إدارة المطعم')) }}</a>

                        <div class="d-flex">
                            @auth
                                <div class="dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-user me-2"></i>{{ Auth::user()->name }}
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                            </a>
                                            <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                                @csrf
                                            </form>
                                        </li>
                                    </ul>
                                </div>
                            @else
                                <a href="{{ route('login') }}" class="btn btn-outline-primary">تسجيل الدخول</a>
                            @endauth
                        </div>
                    </div>
                </nav>

                <!-- Content -->
                <div class="container-fluid">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @yield('content')
                </div>
            </main>
        </div>
    </div>

    <!-- Copyright Footer -->
    <footer class="bg-light border-top mt-auto py-3">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <small class="text-muted">
                        <i class="fas fa-copyright me-1"></i>
                        {{ date('Y') }} نظام إدارة المطعم المتكامل - جميع الحقوق محفوظة |
                        <i class="fas fa-phone me-1"></i>
                        للدعم الفني: 0096899553103
                    </small>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        الإصدار 1.0.0 |
                        @auth
                        @if(auth()->user()->hasRole('admin'))
                        <span class="text-muted">
                            <i class="fas fa-certificate me-1"></i>
                            مرخص ومطور محلياً
                        </span>
                        @endif
                        @endauth
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    @yield('scripts')
</body>
</html>
