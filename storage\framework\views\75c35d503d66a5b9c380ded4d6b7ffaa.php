<?php $__env->startSection('title', 'عرض الفاتورة'); ?>

<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>عرض الفاتورة: <?php echo e($invoice->invoice_number); ?></h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?php echo e(route('invoices.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
            </a>
            <?php if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('manager')): ?>
            <a href="<?php echo e(route('invoices.edit', $invoice->id)); ?>" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
            <?php endif; ?>
            <div class="dropdown d-inline-block">
                <button class="btn btn-primary dropdown-toggle" type="button" id="printDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-print me-1"></i> طباعة
                </button>
                <ul class="dropdown-menu" aria-labelledby="printDropdown">
                    <li><a class="dropdown-item" href="<?php echo e(route('invoices.print', $invoice->id)); ?>" target="_blank">طباعة A4</a></li>
                    <li><a class="dropdown-item" href="<?php echo e(route('invoices.print.85mm', $invoice->id)); ?>" target="_blank">طباعة 85مم</a></li>
                    <li><a class="dropdown-item" href="<?php echo e(route('invoices.print.56mm', $invoice->id)); ?>" target="_blank">طباعة 56مم</a></li>
                </ul>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">معلومات الفاتورة</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 40%">رقم الفاتورة</th>
                            <td><?php echo e($invoice->invoice_number); ?></td>
                        </tr>
                        <tr>
                            <th>الطلب</th>
                            <td>
                                <?php if($invoice->order): ?>
                                    <a href="<?php echo e(route('orders.show', $invoice->order->id)); ?>">طلب #<?php echo e($invoice->order->id); ?></a>
                                <?php else: ?>
                                    غير محدد
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>الطاولة</th>
                            <td>
                                <?php if($invoice->order && $invoice->order->table): ?>
                                    <?php echo e($invoice->order->table->name); ?>

                                <?php elseif($invoice->order && $invoice->order->order_type == 'takeaway'): ?>
                                    <span class="badge bg-info">طلب خارجي</span>
                                <?php else: ?>
                                    غير محدد
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php if($invoice->order && $invoice->order->order_type == 'takeaway' && $invoice->order->car_number): ?>
                        <tr>
                            <th>رقم السيارة</th>
                            <td><?php echo e($invoice->order->car_number); ?></td>
                        </tr>
                        <?php endif; ?>
                        <?php if($invoice->order && $invoice->order->order_type == 'takeaway' && $invoice->order->customer_phone): ?>
                        <tr>
                            <th>رقم الهاتف</th>
                            <td><?php echo e($invoice->order->customer_phone); ?></td>
                        </tr>
                        <?php endif; ?>
                        <tr>
                            <th>الكاشير</th>
                            <td>
                                <?php if($invoice->user): ?>
                                    <?php echo e($invoice->user->name); ?>

                                <?php else: ?>
                                    غير محدد
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>طريقة الدفع</th>
                            <td>
                                <?php if($invoice->payment_method == 'cash'): ?>
                                    <span class="badge bg-success">نقداً</span>
                                <?php elseif($invoice->payment_method == 'credit_card'): ?>
                                    <span class="badge bg-info">بطاقة ائتمان</span>
                                <?php elseif($invoice->payment_method == 'debit_card'): ?>
                                    <span class="badge bg-primary">بطاقة خصم</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>حالة الدفع</th>
                            <td>
                                <?php if($invoice->payment_status == 'paid'): ?>
                                    <span class="badge bg-success">مدفوعة</span>
                                <?php elseif($invoice->payment_status == 'partial'): ?>
                                    <span class="badge bg-warning">مدفوعة جزئياً</span>
                                <?php elseif($invoice->payment_status == 'unpaid'): ?>
                                    <span class="badge bg-danger">غير مدفوعة</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>تاريخ الإنشاء</th>
                            <td><?php echo e($invoice->created_at->format('Y-m-d H:i')); ?></td>
                        </tr>
                    </table>

                    <?php if($invoice->notes): ?>
                    <div class="alert alert-info mt-3">
                        <h6 class="alert-heading">ملاحظات:</h6>
                        <p class="mb-0"><?php echo e($invoice->notes); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">ملخص المبالغ</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <table class="table table-bordered">
                                <tr>
                                    <th>المبلغ الإجمالي</th>
                                    <td class="text-end"><?php echo e(number_format($invoice->total_amount, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></td>
                                </tr>
                                <tr>
                                    <th>المبلغ المدفوع</th>
                                    <td class="text-end"><?php echo e(number_format($invoice->paid_amount, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></td>
                                </tr>
                                <tr class="<?php echo e($invoice->remaining_amount > 0 ? 'table-danger' : 'table-success'); ?>">
                                    <th>المبلغ المتبقي</th>
                                    <td class="text-end fw-bold"><?php echo e(number_format($invoice->remaining_amount, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">عناصر الطلب</h5>
                </div>
                <div class="card-body">
                    <?php if($invoice->order && $invoice->order->orderItems->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>#</th>
                                        <th>المنتج</th>
                                        <th>السعر</th>
                                        <th>الكمية</th>
                                        <th>المجموع</th>
                                        <th>ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $invoice->order->orderItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($index + 1); ?></td>
                                        <td>
                                            <?php if($item->product): ?>
                                                <?php echo e($item->product->name); ?>

                                            <?php else: ?>
                                                منتج محذوف
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e(number_format($item->unit_price, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></td>
                                        <td><?php echo e($item->quantity); ?></td>
                                        <td><?php echo e(number_format($item->subtotal, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></td>
                                        <td><?php echo e($item->notes ?? '-'); ?></td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th colspan="4" class="text-end">المجموع الكلي:</th>
                                        <th><?php echo e(number_format($invoice->total_amount, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            لا توجد عناصر في هذا الطلب
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\restaurant\resources\views/invoices/show.blade.php ENDPATH**/ ?>