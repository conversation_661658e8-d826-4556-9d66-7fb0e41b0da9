@extends('layouts.app')

@section('title', 'إدارة الطاولات')

@section('content')
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>إدارة الطاولات</h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('tables.create') }}" class="btn btn-primary">
                <i class="fas fa-plus-circle me-1"></i> إضافة طاولة جديدة
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card-group">
                <div class="card border-success me-3" style="max-width: 12rem;">
                    <div class="card-body text-center">
                        <h5 class="card-title">متاحة</h5>
                        <p class="card-text display-4">{{ $tables->where('status', 'available')->count() }}</p>
                    </div>
                </div>
                <div class="card border-danger me-3" style="max-width: 12rem;">
                    <div class="card-body text-center">
                        <h5 class="card-title">مشغولة</h5>
                        <p class="card-text display-4">{{ $tables->where('status', 'occupied')->count() }}</p>
                    </div>
                </div>
                <div class="card border-warning" style="max-width: 12rem;">
                    <div class="card-body text-center">
                        <h5 class="card-title">محجوزة</h5>
                        <p class="card-text display-4">{{ $tables->where('status', 'reserved')->count() }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>الاسم</th>
                            <th>السعة</th>
                            <th>الحالة</th>
                            <th>الوصف</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($tables as $table)
                        <tr>
                            <td>{{ $table->id }}</td>
                            <td>{{ $table->name }}</td>
                            <td>{{ $table->capacity }} أشخاص</td>
                            <td>
                                @if($table->status == 'available')
                                    <span class="badge bg-success">متاحة</span>
                                @elseif($table->status == 'occupied')
                                    <span class="badge bg-danger">مشغولة</span>
                                @elseif($table->status == 'reserved')
                                    <span class="badge bg-warning">محجوزة</span>
                                @endif
                            </td>
                            <td>{{ $table->description ?? '-' }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('tables.show', $table->id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('tables.edit', $table->id) }}" class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ $table->id }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>

                                <!-- Modal for Delete Confirmation -->
                                <div class="modal fade" id="deleteModal{{ $table->id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ $table->id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ $table->id }}">تأكيد الحذف</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                هل أنت متأكد من رغبتك في حذف الطاولة <strong>{{ $table->name }}</strong>؟
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                <form action="{{ route('tables.destroy', $table->id) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-danger">حذف</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="text-center">لا توجد طاولات</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection
