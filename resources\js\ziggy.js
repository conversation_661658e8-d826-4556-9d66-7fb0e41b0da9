const Ziggy = {"url":"http:\/\/localhost","port":null,"defaults":{},"routes":{"login":{"uri":"login","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"users.index":{"uri":"users","methods":["GET","HEAD"]},"users.create":{"uri":"users\/create","methods":["GET","HEAD"]},"users.store":{"uri":"users","methods":["POST"]},"users.show":{"uri":"users\/{user}","methods":["GET","HEAD"],"parameters":["user"]},"users.edit":{"uri":"users\/{user}\/edit","methods":["GET","HEAD"],"parameters":["user"]},"users.update":{"uri":"users\/{user}","methods":["PUT","PATCH"],"parameters":["user"]},"users.destroy":{"uri":"users\/{user}","methods":["DELETE"],"parameters":["user"]},"roles.index":{"uri":"roles","methods":["GET","HEAD"]},"roles.create":{"uri":"roles\/create","methods":["GET","HEAD"]},"roles.store":{"uri":"roles","methods":["POST"]},"roles.show":{"uri":"roles\/{role}","methods":["GET","HEAD"],"parameters":["role"]},"roles.edit":{"uri":"roles\/{role}\/edit","methods":["GET","HEAD"],"parameters":["role"]},"roles.update":{"uri":"roles\/{role}","methods":["PUT","PATCH"],"parameters":["role"]},"roles.destroy":{"uri":"roles\/{role}","methods":["DELETE"],"parameters":["role"]},"tables.index":{"uri":"tables","methods":["GET","HEAD"]},"tables.create":{"uri":"tables\/create","methods":["GET","HEAD"]},"tables.store":{"uri":"tables","methods":["POST"]},"tables.show":{"uri":"tables\/{table}","methods":["GET","HEAD"],"parameters":["table"]},"tables.edit":{"uri":"tables\/{table}\/edit","methods":["GET","HEAD"],"parameters":["table"]},"tables.update":{"uri":"tables\/{table}","methods":["PUT","PATCH"],"parameters":["table"]},"tables.destroy":{"uri":"tables\/{table}","methods":["DELETE"],"parameters":["table"]},"categories.index":{"uri":"categories","methods":["GET","HEAD"]},"categories.create":{"uri":"categories\/create","methods":["GET","HEAD"]},"categories.store":{"uri":"categories","methods":["POST"]},"categories.show":{"uri":"categories\/{category}","methods":["GET","HEAD"],"parameters":["category"]},"categories.edit":{"uri":"categories\/{category}\/edit","methods":["GET","HEAD"],"parameters":["category"]},"categories.update":{"uri":"categories\/{category}","methods":["PUT","PATCH"],"parameters":["category"]},"categories.destroy":{"uri":"categories\/{category}","methods":["DELETE"],"parameters":["category"]},"products.index":{"uri":"products","methods":["GET","HEAD"]},"products.create":{"uri":"products\/create","methods":["GET","HEAD"]},"products.store":{"uri":"products","methods":["POST"]},"products.show":{"uri":"products\/{product}","methods":["GET","HEAD"],"parameters":["product"]},"products.edit":{"uri":"products\/{product}\/edit","methods":["GET","HEAD"],"parameters":["product"]},"products.update":{"uri":"products\/{product}","methods":["PUT","PATCH"],"parameters":["product"]},"products.destroy":{"uri":"products\/{product}","methods":["DELETE"],"parameters":["product"]},"products.toggle-availability":{"uri":"products\/{product}\/toggle-availability","methods":["PATCH"],"parameters":["product"]},"orders.index":{"uri":"orders","methods":["GET","HEAD"]},"orders.create":{"uri":"orders\/create","methods":["GET","HEAD"]},"orders.store":{"uri":"orders","methods":["POST"]},"orders.show":{"uri":"orders\/{order}","methods":["GET","HEAD"],"parameters":["order"]},"orders.edit":{"uri":"orders\/{order}\/edit","methods":["GET","HEAD"],"parameters":["order"]},"orders.update":{"uri":"orders\/{order}","methods":["PUT","PATCH"],"parameters":["order"]},"orders.destroy":{"uri":"orders\/{order}","methods":["DELETE"],"parameters":["order"]},"orders.items":{"uri":"orders\/{order}\/items","methods":["GET","HEAD"],"parameters":["order"]},"orders.status":{"uri":"orders\/{order}\/status","methods":["POST"],"parameters":["order"]},"order-items.index":{"uri":"order-items","methods":["GET","HEAD"]},"order-items.create":{"uri":"order-items\/create","methods":["GET","HEAD"]},"order-items.store":{"uri":"order-items","methods":["POST"]},"order-items.show":{"uri":"order-items\/{order_item}","methods":["GET","HEAD"],"parameters":["order_item"]},"order-items.edit":{"uri":"order-items\/{order_item}\/edit","methods":["GET","HEAD"],"parameters":["order_item"]},"order-items.update":{"uri":"order-items\/{order_item}","methods":["PUT","PATCH"],"parameters":["order_item"]},"order-items.destroy":{"uri":"order-items\/{order_item}","methods":["DELETE"],"parameters":["order_item"]},"invoices.index":{"uri":"invoices","methods":["GET","HEAD"]},"invoices.create":{"uri":"invoices\/create","methods":["GET","HEAD"]},"invoices.store":{"uri":"invoices","methods":["POST"]},"invoices.show":{"uri":"invoices\/{invoice}","methods":["GET","HEAD"],"parameters":["invoice"]},"invoices.edit":{"uri":"invoices\/{invoice}\/edit","methods":["GET","HEAD"],"parameters":["invoice"]},"invoices.update":{"uri":"invoices\/{invoice}","methods":["PUT","PATCH"],"parameters":["invoice"]},"invoices.destroy":{"uri":"invoices\/{invoice}","methods":["DELETE"],"parameters":["invoice"]},"invoices.print":{"uri":"invoices\/{invoice}\/print","methods":["GET","HEAD"],"parameters":["invoice"]},"invoices.print.85mm":{"uri":"invoices\/{invoice}\/print\/85mm","methods":["GET","HEAD"],"parameters":["invoice"]},"invoices.print.56mm":{"uri":"invoices\/{invoice}\/print\/56mm","methods":["GET","HEAD"],"parameters":["invoice"]},"reports.sales":{"uri":"reports\/sales","methods":["GET","HEAD"]},"reports.orders":{"uri":"reports\/orders","methods":["GET","HEAD"]},"reports.products":{"uri":"reports\/products","methods":["GET","HEAD"]},"settings.index":{"uri":"settings","methods":["GET","HEAD"]},"settings.edit":{"uri":"settings\/edit","methods":["GET","HEAD"]},"settings.update":{"uri":"settings","methods":["PUT"]},"settings.delete-logo":{"uri":"settings\/logo","methods":["DELETE"]},"settings.reset":{"uri":"settings\/reset","methods":["DELETE"]},"settings.export":{"uri":"settings\/export","methods":["GET","HEAD"]},"expenses.index":{"uri":"expenses","methods":["GET","HEAD"]},"expenses.create":{"uri":"expenses\/create","methods":["GET","HEAD"]},"expenses.store":{"uri":"expenses","methods":["POST"]},"expenses.show":{"uri":"expenses\/{expense}","methods":["GET","HEAD"],"parameters":["expense"],"bindings":{"expense":"id"}},"expenses.edit":{"uri":"expenses\/{expense}\/edit","methods":["GET","HEAD"],"parameters":["expense"],"bindings":{"expense":"id"}},"expenses.update":{"uri":"expenses\/{expense}","methods":["PUT","PATCH"],"parameters":["expense"],"bindings":{"expense":"id"}},"expenses.destroy":{"uri":"expenses\/{expense}","methods":["DELETE"],"parameters":["expense"],"bindings":{"expense":"id"}},"expenses.export":{"uri":"expenses-export","methods":["GET","HEAD"]},"purchases.index":{"uri":"purchases","methods":["GET","HEAD"]},"purchases.create":{"uri":"purchases\/create","methods":["GET","HEAD"]},"purchases.store":{"uri":"purchases","methods":["POST"]},"purchases.show":{"uri":"purchases\/{purchase}","methods":["GET","HEAD"],"parameters":["purchase"],"bindings":{"purchase":"id"}},"purchases.edit":{"uri":"purchases\/{purchase}\/edit","methods":["GET","HEAD"],"parameters":["purchase"],"bindings":{"purchase":"id"}},"purchases.update":{"uri":"purchases\/{purchase}","methods":["PUT","PATCH"],"parameters":["purchase"],"bindings":{"purchase":"id"}},"purchases.destroy":{"uri":"purchases\/{purchase}","methods":["DELETE"],"parameters":["purchase"],"bindings":{"purchase":"id"}},"purchases.export":{"uri":"purchases-export","methods":["GET","HEAD"]},"backup.index":{"uri":"backup","methods":["GET","HEAD"]},"backup.create":{"uri":"backup\/create","methods":["POST"]},"backup.create-sync":{"uri":"backup\/create-sync","methods":["POST"]},"backup.download":{"uri":"backup\/download\/{filename}","methods":["GET","HEAD"],"parameters":["filename"]},"backup.delete":{"uri":"backup\/delete\/{filename}","methods":["DELETE"],"parameters":["filename"]},"backup.delete.post":{"uri":"backup\/delete\/{filename}","methods":["POST"],"parameters":["filename"]},"backup.cleanup.pending":{"uri":"backup\/cleanup-pending","methods":["POST"]},"backup.restore":{"uri":"backup\/restore","methods":["POST"]},"backup.restore-direct":{"uri":"backup\/restore-direct","methods":["POST"]},"waiter.index":{"uri":"waiter","methods":["GET","HEAD"]},"waiter.orders.create":{"uri":"waiter\/orders","methods":["POST"]},"waiter.orders.active":{"uri":"waiter\/orders\/active","methods":["GET","HEAD"]},"waiter.orders.suspend":{"uri":"waiter\/orders\/suspend","methods":["POST"]},"waiter.orders.get":{"uri":"waiter\/orders\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"waiter.orders.pay":{"uri":"waiter\/orders\/{id}\/pay","methods":["POST"],"parameters":["id"]},"waiter.orders.status":{"uri":"waiter\/orders\/{id}\/status","methods":["POST"],"parameters":["id"]},"waiter.orders.status.put":{"uri":"waiter\/orders\/{id}\/status","methods":["PUT"],"parameters":["id"]},"waiter.orders.add-product":{"uri":"waiter\/orders\/{id}\/add-product","methods":["POST"],"parameters":["id"]},"waiter.kitchen-order.create":{"uri":"waiter\/kitchen-order","methods":["POST"]},"waiter.kitchen-order.print":{"uri":"waiter\/kitchen-order\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"waiter.orders.remove-product":{"uri":"waiter\/orders\/{id}\/remove-product","methods":["POST"],"parameters":["id"]},"waiter.notifications.get":{"uri":"waiter\/notifications","methods":["GET","HEAD"]},"waiter.notifications.mark-as-read":{"uri":"waiter\/notifications\/{id}\/read","methods":["POST"],"parameters":["id"]},"waiter.notifications.mark-all-as-read":{"uri":"waiter\/notifications\/read-all","methods":["POST"]},"chef.index":{"uri":"chef","methods":["GET","HEAD"]},"chef.orders.active":{"uri":"chef\/orders\/active","methods":["GET","HEAD"]},"chef.orders.get":{"uri":"chef\/orders\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"chef.orders.status":{"uri":"chef\/orders\/{id}\/status","methods":["POST"],"parameters":["id"]},"notifications.get":{"uri":"notifications","methods":["GET","HEAD"]},"notifications.mark-as-read":{"uri":"notifications\/{id}\/read","methods":["POST"],"parameters":["id"]},"notifications.mark-all-as-read":{"uri":"notifications\/read-all","methods":["POST"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
