<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PurchaseItem extends Model
{
    protected $fillable = [
        'purchase_id',
        'item_name',
        'item_description',
        'unit',
        'quantity',
        'unit_price',
        'total_price',
        'expiry_date',
        'category'
    ];

    protected $casts = [
        'quantity' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'expiry_date' => 'date'
    ];

    /**
     * العلاقة مع المشترى
     */
    public function purchase(): BelongsTo
    {
        return $this->belongsTo(Purchase::class);
    }

    /**
     * الوحدات المتاحة
     */
    public static function getUnits(): array
    {
        return [
            'kg' => 'كيلوجرام',
            'g' => 'جرام',
            'l' => 'لتر',
            'ml' => 'مليلتر',
            'piece' => 'قطعة',
            'box' => 'علبة',
            'bag' => 'كيس',
            'bottle' => 'زجاجة',
            'can' => 'علبة معدنية',
            'pack' => 'عبوة'
        ];
    }

    /**
     * فئات الأصناف
     */
    public static function getCategories(): array
    {
        return [
            'meat' => 'لحوم',
            'vegetables' => 'خضروات',
            'fruits' => 'فواكه',
            'dairy' => 'منتجات الألبان',
            'grains' => 'حبوب',
            'spices' => 'بهارات',
            'beverages' => 'مشروبات',
            'cleaning' => 'مواد تنظيف',
            'packaging' => 'مواد تعبئة',
            'other' => 'أخرى'
        ];
    }

    /**
     * الحصول على اسم الوحدة
     */
    public function getUnitNameAttribute(): string
    {
        $units = self::getUnits();
        return $units[$this->unit] ?? $this->unit;
    }

    /**
     * الحصول على اسم الفئة
     */
    public function getCategoryNameAttribute(): string
    {
        $categories = self::getCategories();
        return $categories[$this->category] ?? $this->category;
    }

    /**
     * التحقق من انتهاء الصلاحية
     */
    public function getIsExpiredAttribute(): bool
    {
        return $this->expiry_date && $this->expiry_date->isPast();
    }

    /**
     * التحقق من قرب انتهاء الصلاحية (خلال 30 يوم)
     */
    public function getIsExpiringSoonAttribute(): bool
    {
        return $this->expiry_date && $this->expiry_date->diffInDays(now()) <= 30 && !$this->is_expired;
    }
}
