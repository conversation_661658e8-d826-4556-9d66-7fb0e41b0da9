<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النسخ الاحتياطي</title>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .user-info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار نظام النسخ الاحتياطي</h1>
        
        <div class="user-info">
            <h3>معلومات المستخدم الحالي:</h3>
            <?php if(auth()->guard()->check()): ?>
                <p><strong>الاسم:</strong> <?php echo e(auth()->user()->name); ?></p>
                <p><strong>البريد الإلكتروني:</strong> <?php echo e(auth()->user()->email); ?></p>
                <p><strong>الدور:</strong> <?php echo e(auth()->user()->role); ?></p>
                <p><strong>معرف الدور:</strong> <?php echo e(auth()->user()->role_id); ?></p>
                <p><strong>هل هو مدير؟</strong> <?php echo e(auth()->user()->hasRole('admin') ? 'نعم' : 'لا'); ?></p>
            <?php else: ?>
                <p style="color: red;">غير مسجل دخول</p>
            <?php endif; ?>
        </div>

        <div>
            <h3>اختبار الصلاحيات:</h3>
            <button class="btn" onclick="testPermissions()">فحص الصلاحيات</button>
            <button class="btn" onclick="simpleBackupTest()">اختبار صلاحيات النسخ الاحتياطي</button>
        </div>

        <div>
            <h3>اختبار إنشاء نسخة احتياطية (الطريقة العادية):</h3>
            <button class="btn" onclick="testBackup('database')">نسخة احتياطية لقاعدة البيانات</button>
            <button class="btn" onclick="testBackup('files')">نسخة احتياطية للملفات</button>
            <button class="btn" onclick="testBackup('full')">نسخة احتياطية كاملة</button>
        </div>

        <div>
            <h3>اختبار إنشاء نسخة احتياطية (بدون middleware):</h3>
            <button class="btn" onclick="testBackupDirect('database')">نسخة احتياطية لقاعدة البيانات (مباشر)</button>
            <button class="btn" onclick="testBackupDirect('files')">نسخة احتياطية للملفات (مباشر)</button>
            <button class="btn" onclick="testBackupDirect('full')">نسخة احتياطية كاملة (مباشر)</button>
        </div>

        <div id="result" class="result"></div>
    </div>

    <script>
        // إعداد CSRF token
        const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        function testPermissions() {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result';
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'جاري فحص الصلاحيات...';

            fetch('/test-permissions', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h4>نتائج فحص الصلاحيات:</h4>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            })
            .catch(error => {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>خطأ في فحص الصلاحيات:</h4>
                    <p>${error.message}</p>
                `;
            });
        }

        function simpleBackupTest() {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result';
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'جاري اختبار صلاحيات النسخ الاحتياطي...';

            fetch('/simple-backup-test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': token,
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>✅ اختبار الصلاحيات نجح!</h4>
                        <p><strong>الرسالة:</strong> ${data.message}</p>
                        <p><strong>المستخدم:</strong> ${data.user}</p>
                        <p><strong>الدور:</strong> ${data.role}</p>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h4>❌ اختبار الصلاحيات فشل!</h4>
                        <p><strong>الخطأ:</strong> ${data.error}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            })
            .catch(error => {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>خطأ في الاتصال:</h4>
                    <p>${error.message}</p>
                `;
            });
        }

        function testBackupDirect(type) {
            const resultDiv = document.getElementById('result');
            const buttons = document.querySelectorAll('.btn');

            // تعطيل الأزرار
            buttons.forEach(btn => btn.disabled = true);

            // إظهار رسالة التحميل
            resultDiv.className = 'result';
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'جاري إنشاء النسخة الاحتياطية (مباشر)...';

            fetch('/test-backup-direct', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': token,
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    backup_type: type,
                    description: 'اختبار النسخ الاحتياطي المباشر - ' + type
                })
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(`HTTP ${response.status}: ${data.error || 'خطأ غير معروف'}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>نجح إنشاء النسخة الاحتياطية (مباشر)!</h4>
                        <p><strong>الرسالة:</strong> ${data.message}</p>
                        <p><strong>اسم الملف:</strong> ${data.backup_path}</p>
                        <p><strong>معرف النسخة:</strong> ${data.backup_id}</p>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h4>فشل في إنشاء النسخة الاحتياطية (مباشر)</h4>
                        <p><strong>الرسالة:</strong> ${data.message || data.error}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            })
            .catch(error => {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>حدث خطأ (مباشر):</h4>
                    <p><strong>الخطأ:</strong> ${error.message}</p>
                `;
            })
            .finally(() => {
                // إعادة تفعيل الأزرار
                buttons.forEach(btn => btn.disabled = false);
            });
        }

        function testBackup(type) {
            const resultDiv = document.getElementById('result');
            const buttons = document.querySelectorAll('.btn');
            
            // تعطيل الأزرار
            buttons.forEach(btn => btn.disabled = true);
            
            // إظهار رسالة التحميل
            resultDiv.className = 'result';
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'جاري إنشاء النسخة الاحتياطية...';

            fetch('/backup/create-sync', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': token,
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    backup_type: type,
                    description: 'اختبار النسخ الاحتياطي - ' + type
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>نجح إنشاء النسخة الاحتياطية!</h4>
                        <p><strong>الرسالة:</strong> ${data.message}</p>
                        <p><strong>اسم الملف:</strong> ${data.backup_path}</p>
                        <p><strong>معرف النسخة:</strong> ${data.backup_id}</p>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h4>فشل في إنشاء النسخة الاحتياطية</h4>
                        <p><strong>الرسالة:</strong> ${data.message}</p>
                    `;
                }
            })
            .catch(error => {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>حدث خطأ:</h4>
                    <p><strong>الخطأ:</strong> ${error.message}</p>
                    <p><strong>تفاصيل إضافية (وضع التطوير):</strong></p>
                    <pre>${JSON.stringify(error, null, 2)}</pre>
                `;
            })
            .finally(() => {
                // إعادة تفعيل الأزرار
                buttons.forEach(btn => btn.disabled = false);
            });
        }
    </script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\restaurant\resources\views/test-backup.blade.php ENDPATH**/ ?>