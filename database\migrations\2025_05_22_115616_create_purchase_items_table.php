<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('purchase_id')->constrained()->onDelete('cascade'); // معرف المشترى
            $table->string('item_name'); // اسم الصنف
            $table->text('item_description')->nullable(); // وصف الصنف
            $table->string('unit'); // الوحدة (كيلو، لتر، قطعة، علبة)
            $table->decimal('quantity', 8, 2); // الكمية
            $table->decimal('unit_price', 8, 2); // سعر الوحدة
            $table->decimal('total_price', 10, 2); // إجمالي السعر
            $table->date('expiry_date')->nullable(); // تاريخ الانتهاء
            $table->string('category')->nullable(); // فئة الصنف
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_items');
    }
};
