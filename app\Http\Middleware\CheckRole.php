<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Role;

class CheckRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, $roleName = null): Response
    {
        // التحقق من تسجيل دخول المستخدم
        if (!$request->user()) {
            return redirect()->route('login');
        }

        // إذا لم يتم تحديد دور، تحقق فقط من تسجيل الدخول
        if (!$roleName) {
            return $next($request);
        }

        // التحقق من دور المستخدم
        if ($request->user()->hasRole($roleName)) {
            return $next($request);
        }

        // إذا لم يكن المستخدم لديه الدور المطلوب، قم بتوجيهه إلى لوحة التحكم
        return redirect()->route('dashboard')->with('error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة');
    }
}
