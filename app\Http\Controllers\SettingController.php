<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Setting;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class SettingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // تجميع الإعدادات حسب المجموعة
        $settings = Setting::all()->groupBy('group');

        return view('settings.index', compact('settings'));
    }

    /**
     * عرض صفحة تعديل الإعدادات
     */
    public function edit()
    {
        // تجميع الإعدادات حسب المجموعة
        $settings = Setting::all()->groupBy('group');

        return view('settings.edit', compact('settings'));
    }

    /**
     * تحديث الإعدادات
     */
    public function update(Request $request)
    {
        // التحقق من صحة البيانات
        $validator = Validator::make($request->all(), [
            'restaurant_name' => 'required|string|max:255',
            'restaurant_address' => 'required|string|max:500',
            'restaurant_phone' => 'required|string|max:20',
            'restaurant_email' => 'required|email|max:255',
            'restaurant_website' => 'nullable|url|max:255',
            'currency' => 'required|string|max:10',
            'currency_code' => 'required|string|max:3',
            'tax_rate' => 'required|numeric|min:0|max:100',
            'tax_number' => 'nullable|string|max:50',
            'restaurant_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'default_print_size' => 'required|in:85mm,56mm,A4',
            'print_logo' => 'boolean',
            'auto_print_kitchen_order' => 'boolean'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // تحديث الإعدادات النصية
            $textSettings = [
                'restaurant_name' => ['label' => 'اسم المطعم', 'group' => 'restaurant'],
                'restaurant_address' => ['label' => 'عنوان المطعم', 'group' => 'restaurant'],
                'restaurant_phone' => ['label' => 'رقم الهاتف', 'group' => 'restaurant'],
                'restaurant_email' => ['label' => 'البريد الإلكتروني', 'group' => 'restaurant'],
                'restaurant_website' => ['label' => 'الموقع الإلكتروني', 'group' => 'restaurant'],
                'currency' => ['label' => 'رمز العملة', 'group' => 'financial'],
                'currency_code' => ['label' => 'كود العملة', 'group' => 'financial'],
                'tax_number' => ['label' => 'الرقم الضريبي', 'group' => 'financial'],
                'default_print_size' => ['label' => 'حجم الطباعة الافتراضي', 'group' => 'printing']
            ];

            foreach ($textSettings as $key => $config) {
                if ($request->has($key)) {
                    Setting::set($key, $request->input($key), 'text', $config['label'], $config['group']);
                }
            }

            // تحديث الإعدادات الرقمية
            Setting::set('tax_rate', $request->input('tax_rate'), 'number', 'معدل الضريبة (%)', 'financial');

            // تحديث الإعدادات المنطقية
            Setting::set('print_logo', $request->has('print_logo') ? '1' : '0', 'boolean', 'طباعة الشعار', 'printing');
            Setting::set('auto_print_kitchen_order', $request->has('auto_print_kitchen_order') ? '1' : '0', 'boolean', 'طباعة طلب المطبخ تلقائياً', 'printing');

            // معالجة رفع الشعار
            if ($request->hasFile('restaurant_logo')) {
                $logoPath = Setting::uploadLogo($request->file('restaurant_logo'));
                if ($logoPath) {
                    Setting::set('restaurant_logo', $logoPath, 'image');
                }
            }

            return redirect()->route('settings.index')
                ->with('success', 'تم تحديث الإعدادات بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء تحديث الإعدادات: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * حذف الشعار
     */
    public function deleteLogo()
    {
        try {
            $logoPath = Setting::get('restaurant_logo');

            if ($logoPath && Storage::disk('public')->exists($logoPath)) {
                Storage::disk('public')->delete($logoPath);
            }

            Setting::set('restaurant_logo', null, 'image');

            return response()->json([
                'success' => true,
                'message' => 'تم حذف الشعار بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف الشعار: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * إعادة تعيين الإعدادات للقيم الافتراضية
     */
    public function reset()
    {
        try {
            // حذف جميع الإعدادات الحالية
            Setting::truncate();

            // إعادة تشغيل seeder
            \Artisan::call('db:seed', ['--class' => 'SettingsSeeder']);

            return redirect()->route('settings.index')
                ->with('success', 'تم إعادة تعيين الإعدادات للقيم الافتراضية بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء إعادة تعيين الإعدادات: ' . $e->getMessage());
        }
    }

    /**
     * تصدير الإعدادات
     */
    public function export()
    {
        try {
            $settings = Setting::all();
            $exportData = [];

            foreach ($settings as $setting) {
                $exportData[$setting->key] = [
                    'value' => $setting->value,
                    'type' => $setting->type,
                    'group' => $setting->group,
                    'label' => $setting->label,
                    'description' => $setting->description,
                    'is_public' => $setting->is_public
                ];
            }

            $fileName = 'restaurant_settings_' . date('Y-m-d_H-i-s') . '.json';

            return response()->json($exportData)
                ->header('Content-Disposition', 'attachment; filename="' . $fileName . '"');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء تصدير الإعدادات: ' . $e->getMessage());
        }
    }
}
