#!/bin/bash

# سكريبت نشر مشروع Laravel
echo "🚀 بدء عملية النشر..."

# 1. تحديث الملفات
echo "📁 تحديث الملفات..."
git pull origin main

# 2. تثبيت dependencies
echo "📦 تثبيت المكتبات..."
composer install --no-dev --optimize-autoloader

# 3. تحديث قاعدة البيانات
echo "🗄️ تحديث قاعدة البيانات..."
php artisan migrate --force

# 4. تشغيل seeders
echo "🌱 إضافة البيانات الأساسية..."
php artisan db:seed --force

# 5. ربط storage
echo "🔗 ربط مجلد التخزين..."
php artisan storage:link

# 6. تحسين الأداء
echo "⚡ تحسين الأداء..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 7. تنظيف cache
echo "🧹 تنظيف الذاكرة المؤقتة..."
php artisan cache:clear

# 8. تعيين الصلاحيات
echo "🔐 تعيين الصلاحيات..."
chmod -R 755 storage
chmod -R 755 bootstrap/cache

echo "✅ تم النشر بنجاح!"
