<?php

namespace Database\Seeders;

use App\Models\Table;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create tables with specific names and capacities
        $tables = [
            ['name' => 'طاولة 1', 'capacity' => 2, 'status' => 'available'],
            ['name' => 'طاولة 2', 'capacity' => 4, 'status' => 'available'],
            ['name' => 'طاولة 3', 'capacity' => 4, 'status' => 'available'],
            ['name' => 'طاولة 4', 'capacity' => 6, 'status' => 'available'],
            ['name' => 'طاولة 5', 'capacity' => 8, 'status' => 'available'],
            ['name' => 'طاولة 6', 'capacity' => 2, 'status' => 'available'],
            ['name' => 'طاولة 7', 'capacity' => 4, 'status' => 'available'],
            ['name' => 'طاولة 8', 'capacity' => 6, 'status' => 'available'],
            ['name' => 'طاولة VIP 1', 'capacity' => 10, 'status' => 'available', 'description' => 'طاولة VIP مع خدمة خاصة'],
            ['name' => 'طاولة VIP 2', 'capacity' => 12, 'status' => 'available', 'description' => 'طاولة VIP مع خدمة خاصة'],
        ];

        foreach ($tables as $table) {
            Table::create($table);
        }

        // Create some additional tables
        $additionalTables = [
            ['name' => 'طاولة 9', 'capacity' => 4, 'status' => 'available'],
            ['name' => 'طاولة 10', 'capacity' => 6, 'status' => 'available'],
            ['name' => 'طاولة 11', 'capacity' => 2, 'status' => 'available'],
            ['name' => 'طاولة 12', 'capacity' => 8, 'status' => 'available'],
            ['name' => 'طاولة 13', 'capacity' => 4, 'status' => 'available'],
        ];

        foreach ($additionalTables as $table) {
            Table::create($table);
        }
    }
}
