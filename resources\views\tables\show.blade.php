@extends('layouts.app')

@section('title', 'عرض الطاولة')

@section('content')
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>عرض الطاولة: {{ $table->name }}</h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('tables.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
            </a>
            <a href="{{ route('tables.edit', $table->id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">معلومات الطاولة</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 30%">الاسم</th>
                            <td>{{ $table->name }}</td>
                        </tr>
                        <tr>
                            <th>السعة</th>
                            <td>{{ $table->capacity }} أشخاص</td>
                        </tr>
                        <tr>
                            <th>الحالة</th>
                            <td>
                                @if($table->status == 'available')
                                    <span class="badge bg-success">متاحة</span>
                                @elseif($table->status == 'occupied')
                                    <span class="badge bg-danger">مشغولة</span>
                                @elseif($table->status == 'reserved')
                                    <span class="badge bg-warning">محجوزة</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>الوصف</th>
                            <td>{{ $table->description ?? 'لا يوجد وصف' }}</td>
                        </tr>
                        <tr>
                            <th>تاريخ الإنشاء</th>
                            <td>{{ $table->created_at->format('Y-m-d H:i') }}</td>
                        </tr>
                        <tr>
                            <th>آخر تحديث</th>
                            <td>{{ $table->updated_at->format('Y-m-d H:i') }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">تغيير الحالة</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('tables.update', $table->id) }}" method="POST">
                        @csrf
                        @method('PUT')
                        <input type="hidden" name="name" value="{{ $table->name }}">
                        <input type="hidden" name="capacity" value="{{ $table->capacity }}">
                        <input type="hidden" name="description" value="{{ $table->description }}">
                        
                        <div class="mb-3">
                            <label for="status" class="form-label">الحالة الجديدة</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="available" {{ $table->status == 'available' ? 'selected' : '' }}>متاحة</option>
                                <option value="occupied" {{ $table->status == 'occupied' ? 'selected' : '' }}>مشغولة</option>
                                <option value="reserved" {{ $table->status == 'reserved' ? 'selected' : '' }}>محجوزة</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> تحديث الحالة
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">آخر الطلبات</h5>
                </div>
                <div class="card-body">
                    @if($orders->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>#</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                        <th>المبلغ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($orders as $order)
                                    <tr>
                                        <td>{{ $order->id }}</td>
                                        <td>{{ $order->created_at->format('Y-m-d H:i') }}</td>
                                        <td>
                                            @if($order->status == 'pending')
                                                <span class="badge bg-secondary">قيد الانتظار</span>
                                            @elseif($order->status == 'preparing')
                                                <span class="badge bg-primary">قيد التحضير</span>
                                            @elseif($order->status == 'ready')
                                                <span class="badge bg-info">جاهز</span>
                                            @elseif($order->status == 'delivered')
                                                <span class="badge bg-success">تم التوصيل</span>
                                            @elseif($order->status == 'completed')
                                                <span class="badge bg-success">مكتمل</span>
                                            @elseif($order->status == 'cancelled')
                                                <span class="badge bg-danger">ملغي</span>
                                            @endif
                                        </td>
                                        <td>{{ number_format($order->total_amount, 2) }} ر.س</td>
                                        <td>
                                            <a href="{{ route('orders.show', $order->id) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            لا توجد طلبات لهذه الطاولة
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
