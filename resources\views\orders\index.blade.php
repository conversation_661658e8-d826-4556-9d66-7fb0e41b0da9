@extends('layouts.app')

@section('title', 'إدارة الطلبات')

@section('content')
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>إدارة الطلبات</h2>
            <p class="text-muted">إجمالي الطلبات: {{ $orders->total() }}</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('orders.create') }}" class="btn btn-primary">
                <i class="fas fa-plus-circle me-1"></i> إضافة طلب جديد
            </a>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="row">
                <div class="col-md-2">
                    <div class="card border-secondary text-center">
                        <div class="card-body">
                            <h6 class="card-title">قيد الانتظار</h6>
                            <h4 class="text-secondary">{{ $orders->where('status', 'pending')->count() }}</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card border-primary text-center">
                        <div class="card-body">
                            <h6 class="card-title">قيد التحضير</h6>
                            <h4 class="text-primary">{{ $orders->where('status', 'preparing')->count() }}</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card border-info text-center">
                        <div class="card-body">
                            <h6 class="card-title">جاهز</h6>
                            <h4 class="text-info">{{ $orders->where('status', 'ready')->count() }}</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card border-success text-center">
                        <div class="card-body">
                            <h6 class="card-title">تم التوصيل</h6>
                            <h4 class="text-success">{{ $orders->where('status', 'delivered')->count() }}</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card border-success text-center">
                        <div class="card-body">
                            <h6 class="card-title">مكتمل</h6>
                            <h4 class="text-success">{{ $orders->where('status', 'completed')->count() }}</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card border-danger text-center">
                        <div class="card-body">
                            <h6 class="card-title">ملغي</h6>
                            <h4 class="text-danger">{{ $orders->where('status', 'cancelled')->count() }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث والتصفية -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('orders.index') }}" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="{{ request('search') }}" placeholder="البحث في الطلبات...">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        @foreach($statuses as $key => $name)
                            <option value="{{ $key }}" {{ request('status') == $key ? 'selected' : '' }}>
                                {{ $name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="order_type" class="form-label">نوع الطلب</label>
                    <select class="form-select" id="order_type" name="order_type">
                        <option value="">جميع الأنواع</option>
                        @foreach($orderTypes as $key => $name)
                            <option value="{{ $key }}" {{ request('order_type') == $key ? 'selected' : '' }}>
                                {{ $name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="user_id" class="form-label">النادل</label>
                    <select class="form-select" id="user_id" name="user_id">
                        <option value="">جميع النوادل</option>
                        @foreach($users as $user)
                            <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                {{ $user->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-1">
                    <label for="per_page" class="form-label">عدد النتائج</label>
                    <select class="form-select" id="per_page" name="per_page">
                        <option value="15" {{ request('per_page') == 15 ? 'selected' : '' }}>15</option>
                        <option value="25" {{ request('per_page', 25) == 25 ? 'selected' : '' }}>25</option>
                        <option value="30" {{ request('per_page') == 30 ? 'selected' : '' }}>30</option>
                        <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50</option>
                        <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="sort_by" class="form-label">ترتيب حسب</label>
                    <select class="form-select" id="sort_by" name="sort_by">
                        <option value="created_at" {{ request('sort_by', 'created_at') == 'created_at' ? 'selected' : '' }}>التاريخ</option>
                        <option value="id" {{ request('sort_by') == 'id' ? 'selected' : '' }}>رقم الطلب</option>
                        <option value="total_amount" {{ request('sort_by') == 'total_amount' ? 'selected' : '' }}>المبلغ</option>
                        <option value="status" {{ request('sort_by') == 'status' ? 'selected' : '' }}>الحالة</option>
                    </select>
                </div>

                <!-- صف ثاني للفلاتر الإضافية -->
                <div class="col-md-2">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from"
                           value="{{ request('date_from') }}">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to"
                           value="{{ request('date_to') }}">
                </div>
                <div class="col-md-2">
                    <label for="amount_from" class="form-label">من مبلغ</label>
                    <input type="number" class="form-control" id="amount_from" name="amount_from"
                           value="{{ request('amount_from') }}" step="0.01" min="0">
                </div>
                <div class="col-md-2">
                    <label for="amount_to" class="form-label">إلى مبلغ</label>
                    <input type="number" class="form-control" id="amount_to" name="amount_to"
                           value="{{ request('amount_to') }}" step="0.01" min="0">
                </div>
                <div class="col-md-1">
                    <label for="sort_order" class="form-label">الاتجاه</label>
                    <select class="form-select" id="sort_order" name="sort_order">
                        <option value="desc" {{ request('sort_order', 'desc') == 'desc' ? 'selected' : '' }}>تنازلي</option>
                        <option value="asc" {{ request('sort_order') == 'asc' ? 'selected' : '' }}>تصاعدي</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label>&nbsp;</label>
                    <div class="d-flex">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search me-1"></i> بحث
                        </button>
                        <a href="{{ route('orders.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i> مسح الفلاتر
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- عرض النتائج -->
    <div class="card shadow-sm">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">قائمة الطلبات</h5>
            <div class="text-muted">
                عرض {{ $orders->firstItem() ?? 0 }} - {{ $orders->lastItem() ?? 0 }} من {{ $orders->total() }} طلب
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>رقم الطلب</th>
                            <th>الطاولة/النوع</th>
                            <th>النادل</th>
                            <th>الحالة</th>
                            <th>المبلغ</th>
                            <th>التاريخ والوقت</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($orders as $order)
                        <tr>
                            <td>
                                <strong>#{{ $order->id }}</strong>
                            </td>
                            <td>
                                @if($order->order_type == 'dine_in')
                                    <i class="fas fa-utensils text-primary me-1"></i>
                                    {{ $order->table->name ?? 'غير محدد' }}
                                @elseif($order->order_type == 'takeaway')
                                    <i class="fas fa-shopping-bag text-warning me-1"></i>
                                    طلب خارجي
                                @else
                                    <i class="fas fa-motorcycle text-info me-1"></i>
                                    توصيل
                                @endif
                            </td>
                            <td>{{ $order->user->name ?? 'غير محدد' }}</td>
                            <td>
                                @if($order->status == 'pending')
                                    <span class="badge bg-secondary">قيد الانتظار</span>
                                @elseif($order->status == 'preparing')
                                    <span class="badge bg-primary">قيد التحضير</span>
                                @elseif($order->status == 'ready')
                                    <span class="badge bg-info">جاهز</span>
                                @elseif($order->status == 'delivered')
                                    <span class="badge bg-success">تم التوصيل</span>
                                @elseif($order->status == 'completed')
                                    <span class="badge bg-success">مكتمل</span>
                                @elseif($order->status == 'cancelled')
                                    <span class="badge bg-danger">ملغي</span>
                                @endif
                            </td>
                            <td>
                                <strong>{{ number_format($order->total_amount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</strong>
                            </td>
                            <td>
                                <div>{{ $order->created_at->format('Y-m-d') }}</div>
                                <small class="text-muted">{{ $order->created_at->format('H:i') }}</small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('orders.show', $order->id) }}" class="btn btn-sm btn-info" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @if(!in_array($order->status, ['completed', 'cancelled']))
                                    <a href="{{ route('orders.edit', $order->id) }}" class="btn btn-sm btn-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @endif
                                    @if($order->status == 'cancelled')
                                    <button type="button" class="btn btn-sm btn-danger" title="حذف"
                                            data-bs-toggle="modal" data-bs-target="#deleteModal{{ $order->id }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    @endif
                                </div>

                                <!-- Modal for Delete Confirmation -->
                                <div class="modal fade" id="deleteModal{{ $order->id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ $order->id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ $order->id }}">تأكيد الحذف</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                هل أنت متأكد من رغبتك في حذف الطلب رقم <strong>{{ $order->id }}</strong>؟
                                                <br><small class="text-danger">تحذير: لا يمكن التراجع عن هذا الإجراء!</small>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                <form action="{{ route('orders.destroy', $order->id) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-danger">حذف</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <br>
                                <span class="text-muted">لا توجد طلبات تطابق معايير البحث</span>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        @if($orders->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">
                    عرض {{ $orders->firstItem() }} - {{ $orders->lastItem() }} من {{ $orders->total() }} طلب
                </div>
                <div>
                    {{ $orders->appends(request()->query())->links('custom-pagination') }}
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

<script>
// تحديث الصفحة عند تغيير عدد النتائج
document.getElementById('per_page').addEventListener('change', function() {
    this.form.submit();
});

// تحديث الصفحة عند تغيير الترتيب
document.getElementById('sort_by').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('sort_order').addEventListener('change', function() {
    this.form.submit();
});

// تحديد التاريخ الحالي كافتراضي للبحث
document.addEventListener('DOMContentLoaded', function() {
    // يمكن إضافة المزيد من JavaScript هنا حسب الحاجة
});
</script>
@endsection
