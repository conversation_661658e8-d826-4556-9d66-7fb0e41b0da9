<?php $__env->startSection('title', 'إضافة مشترى جديد'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مشترى جديد
                    </h3>
                    <a href="<?php echo e(route('purchases.index')); ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        العودة للقائمة
                    </a>
                </div>

                <div class="card-body">
                    <?php if($errors->any()): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-1"></i>
                            <strong>يرجى تصحيح الأخطاء التالية:</strong>
                            <ul class="mb-0 mt-2">
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <form action="<?php echo e(route('purchases.store')); ?>" method="POST" id="purchaseForm">
                        <?php echo csrf_field(); ?>

                        <!-- معلومات المورد -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-truck me-2"></i>معلومات المورد</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="supplier_name" class="form-label">اسم المورد <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="supplier_name" name="supplier_name" 
                                                   value="<?php echo e(old('supplier_name')); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="supplier_phone" class="form-label">رقم الهاتف</label>
                                            <input type="text" class="form-control" id="supplier_phone" name="supplier_phone" 
                                                   value="<?php echo e(old('supplier_phone')); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="invoice_number" class="form-label">رقم فاتورة المورد</label>
                                            <input type="text" class="form-control" id="invoice_number" name="invoice_number" 
                                                   value="<?php echo e(old('invoice_number')); ?>">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label for="supplier_address" class="form-label">عنوان المورد</label>
                                            <textarea class="form-control" id="supplier_address" name="supplier_address" rows="2"><?php echo e(old('supplier_address')); ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات المشترى -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-info-circle me-2"></i>معلومات المشترى</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="purchase_date" class="form-label">تاريخ الشراء <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" id="purchase_date" name="purchase_date" 
                                                   value="<?php echo e(old('purchase_date', date('Y-m-d'))); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="delivery_date" class="form-label">تاريخ التسليم</label>
                                            <input type="date" class="form-control" id="delivery_date" name="delivery_date" 
                                                   value="<?php echo e(old('delivery_date')); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="payment_method" class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                                            <select class="form-select" id="payment_method" name="payment_method" required>
                                                <option value="">اختر طريقة الدفع</option>
                                                <?php $__currentLoopData = $paymentMethods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($key); ?>" <?php echo e(old('payment_method') == $key ? 'selected' : ''); ?>>
                                                        <?php echo e($name); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="paid_amount" class="form-label">المبلغ المدفوع <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="paid_amount" name="paid_amount" 
                                                       value="<?php echo e(old('paid_amount', 0)); ?>" step="0.01" min="0" required>
                                                <span class="input-group-text"><?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="payment_status" class="form-label">حالة الدفع <span class="text-danger">*</span></label>
                                            <select class="form-select" id="payment_status" name="payment_status" required>
                                                <?php $__currentLoopData = $paymentStatuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($key); ?>" <?php echo e(old('payment_status', 'pending') == $key ? 'selected' : ''); ?>>
                                                        <?php echo e($name); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="delivery_status" class="form-label">حالة التسليم <span class="text-danger">*</span></label>
                                            <select class="form-select" id="delivery_status" name="delivery_status" required>
                                                <?php $__currentLoopData = $deliveryStatuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($key); ?>" <?php echo e(old('delivery_status', 'pending') == $key ? 'selected' : ''); ?>>
                                                        <?php echo e($name); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="total_display" class="form-label">المجموع الكلي</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="total_display" readonly>
                                                <span class="input-group-text"><?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label for="notes" class="form-label">ملاحظات</label>
                                            <textarea class="form-control" id="notes" name="notes" rows="2"><?php echo e(old('notes')); ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- عناصر المشترى -->
                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5><i class="fas fa-list me-2"></i>عناصر المشترى</h5>
                                <button type="button" class="btn btn-primary btn-sm" onclick="addItem()">
                                    <i class="fas fa-plus me-1"></i>
                                    إضافة صنف
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="itemsTable">
                                        <thead>
                                            <tr>
                                                <th width="20%">اسم الصنف</th>
                                                <th width="15%">الوحدة</th>
                                                <th width="10%">الكمية</th>
                                                <th width="10%">سعر الوحدة</th>
                                                <th width="10%">المجموع</th>
                                                <th width="15%">تاريخ الانتهاء</th>
                                                <th width="15%">الفئة</th>
                                                <th width="5%">حذف</th>
                                            </tr>
                                        </thead>
                                        <tbody id="itemsTableBody">
                                            <!-- سيتم إضافة الصفوف هنا -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end">
                                    <a href="<?php echo e(route('purchases.index')); ?>" class="btn btn-secondary me-2">
                                        <i class="fas fa-times me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ المشترى
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let itemIndex = 0;

// إضافة صنف جديد
function addItem() {
    const tbody = document.getElementById('itemsTableBody');
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>
            <input type="text" class="form-control" name="items[${itemIndex}][item_name]" required>
            <textarea class="form-control mt-1" name="items[${itemIndex}][item_description]" rows="1" placeholder="وصف الصنف"></textarea>
        </td>
        <td>
            <select class="form-select" name="items[${itemIndex}][unit]" required>
                <option value="">اختر الوحدة</option>
                <?php $__currentLoopData = $units; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($key); ?>"><?php echo e($name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </td>
        <td>
            <input type="number" class="form-control quantity" name="items[${itemIndex}][quantity]" 
                   step="0.01" min="0.01" required onchange="calculateRowTotal(this)">
        </td>
        <td>
            <input type="number" class="form-control unit-price" name="items[${itemIndex}][unit_price]" 
                   step="0.01" min="0" required onchange="calculateRowTotal(this)">
        </td>
        <td>
            <input type="text" class="form-control row-total" readonly>
        </td>
        <td>
            <input type="date" class="form-control" name="items[${itemIndex}][expiry_date]">
        </td>
        <td>
            <select class="form-select" name="items[${itemIndex}][category]">
                <option value="">اختر الفئة</option>
                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($key); ?>"><?php echo e($name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </td>
        <td>
            <button type="button" class="btn btn-danger btn-sm" onclick="removeItem(this)">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    tbody.appendChild(row);
    itemIndex++;
}

// حذف صنف
function removeItem(button) {
    button.closest('tr').remove();
    calculateTotal();
}

// حساب مجموع الصف
function calculateRowTotal(input) {
    const row = input.closest('tr');
    const quantity = parseFloat(row.querySelector('.quantity').value) || 0;
    const unitPrice = parseFloat(row.querySelector('.unit-price').value) || 0;
    const total = quantity * unitPrice;
    
    row.querySelector('.row-total').value = total.toFixed(2);
    calculateTotal();
}

// حساب المجموع الكلي
function calculateTotal() {
    let total = 0;
    document.querySelectorAll('.row-total').forEach(input => {
        total += parseFloat(input.value) || 0;
    });
    
    document.getElementById('total_display').value = total.toFixed(2);
}

// إضافة صنف افتراضي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    addItem();
});

// التحقق من صحة النموذج
document.getElementById('purchaseForm').addEventListener('submit', function(e) {
    const items = document.querySelectorAll('#itemsTableBody tr');
    if (items.length === 0) {
        e.preventDefault();
        alert('يجب إضافة صنف واحد على الأقل');
        return false;
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\restaurant\resources\views/purchases/create.blade.php ENDPATH**/ ?>