<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CreateAdminUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:admin {email=<EMAIL>} {password=admin123}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'إنشاء مستخدم مدير جديد';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $password = $this->argument('password');

        // التحقق من وجود دور admin
        $adminRole = \App\Models\Role::where('name', 'admin')->first();
        if (!$adminRole) {
            $this->error('دور المدير غير موجود. يرجى تشغيل php artisan db:seed --class=RoleSeeder أولاً');
            return 1;
        }

        // التحقق من وجود المستخدم
        $existingUser = \App\Models\User::where('email', $email)->first();
        if ($existingUser) {
            $this->info("المستخدم موجود بالفعل: {$existingUser->name} ({$existingUser->email})");
            $this->info("الدور الحالي: {$existingUser->role_name}");

            if (!$existingUser->hasRole('admin')) {
                $existingUser->role_id = $adminRole->id;
                $existingUser->save();
                $this->info("تم تحديث دور المستخدم إلى مدير");
            }

            return 0;
        }

        // إنشاء مستخدم جديد
        $user = \App\Models\User::create([
            'name' => 'مدير النظام',
            'email' => $email,
            'password' => bcrypt($password),
            'role_id' => $adminRole->id,
        ]);

        $this->info("تم إنشاء مستخدم مدير جديد:");
        $this->info("الاسم: {$user->name}");
        $this->info("الإيميل: {$user->email}");
        $this->info("كلمة المرور: {$password}");
        $this->info("الدور: {$user->role_name}");

        return 0;
    }
}
