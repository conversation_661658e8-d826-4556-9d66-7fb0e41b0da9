@extends('layouts.app')

@section('title', 'تقرير المنتجات')

@section('styles')
<style>
    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 20px;
    }
    .report-card {
        transition: all 0.3s;
    }
    .report-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
</style>
@endsection

@section('content')
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>تقرير المنتجات</h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('reports.sales') }}" class="btn btn-secondary me-2">
                <i class="fas fa-chart-line me-1"></i> تقرير المبيعات
            </a>
            <a href="{{ route('reports.orders') }}" class="btn btn-secondary">
                <i class="fas fa-chart-line me-1"></i> تقرير الطلبات
            </a>
        </div>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="{{ route('reports.products') }}" method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ $startDate }}">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ $endDate }}">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter me-1"></i> تصفية
                    </button>
                    <a href="{{ route('reports.products') }}" class="btn btn-secondary">
                        <i class="fas fa-redo me-1"></i> إعادة تعيين
                    </a>
                </div>
            </form>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white report-card">
                <div class="card-body">
                    <h5 class="card-title">إجمالي المنتجات</h5>
                    <p class="card-text display-6">{{ $totalProducts }}</p>
                    <p class="card-text">عدد التصنيفات: {{ $productsByCategory->count() }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white report-card">
                <div class="card-body">
                    <h5 class="card-title">المنتجات المتاحة</h5>
                    <p class="card-text display-6">{{ $availableProducts }}</p>
                    <p class="card-text">نسبة المنتجات المتاحة: {{ $totalProducts > 0 ? number_format(($availableProducts / $totalProducts) * 100, 1) : 0 }}%</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-danger text-white report-card">
                <div class="card-body">
                    <h5 class="card-title">المنتجات غير المتاحة</h5>
                    <p class="card-text display-6">{{ $unavailableProducts }}</p>
                    <p class="card-text">نسبة المنتجات غير المتاحة: {{ $totalProducts > 0 ? number_format(($unavailableProducts / $totalProducts) * 100, 1) : 0 }}%</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">حالة المنتجات</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="productStatusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">المنتجات حسب التصنيف</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="productsByCategoryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">المنتجات حسب التصنيف</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>التصنيف</th>
                                    <th>عدد المنتجات</th>
                                    <th>المنتجات المتاحة</th>
                                    <th>نسبة المنتجات المتاحة</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($productsByCategory as $categoryData)
                                <tr>
                                    <td>{{ $categoryData['category_name'] }}</td>
                                    <td>{{ $categoryData['count'] }}</td>
                                    <td>{{ $categoryData['available'] }}</td>
                                    <td>{{ $categoryData['count'] > 0 ? number_format(($categoryData['available'] / $categoryData['count']) * 100, 1) : 0 }}%</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="4" class="text-center">لا توجد بيانات</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">المنتجات الأكثر مبيعًا</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>المنتج</th>
                                    <th>الكمية المباعة</th>
                                    <th>إجمالي المبيعات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($topProducts as $index => $product)
                                <tr>
                                    <td>{{ $index + 1 }}</td>
                                    <td>
                                        @if($product->product)
                                            <a href="{{ route('products.show', $product->product->id) }}">{{ $product->product->name }}</a>
                                        @else
                                            منتج محذوف
                                        @endif
                                    </td>
                                    <td>{{ $product->total_quantity }}</td>
                                    <td>{{ number_format($product->total_sales, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="4" class="text-center">لا توجد بيانات</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">المنتجات الأقل مبيعًا</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>المنتج</th>
                                    <th>الكمية المباعة</th>
                                    <th>إجمالي المبيعات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($leastSoldProducts as $index => $product)
                                <tr>
                                    <td>{{ $index + 1 }}</td>
                                    <td>
                                        @if($product->product)
                                            <a href="{{ route('products.show', $product->product->id) }}">{{ $product->product->name }}</a>
                                        @else
                                            منتج محذوف
                                        @endif
                                    </td>
                                    <td>{{ $product->total_quantity }}</td>
                                    <td>{{ number_format($product->total_sales, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="4" class="text-center">لا توجد بيانات</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">المنتجات التي لم تباع</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>المنتج</th>
                                    <th>التصنيف</th>
                                    <th>السعر</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($unsoldProducts as $index => $product)
                                <tr>
                                    <td>{{ $index + 1 }}</td>
                                    <td>
                                        <a href="{{ route('products.show', $product->id) }}">{{ $product->name }}</a>
                                    </td>
                                    <td>{{ $product->category->name ?? 'غير مصنف' }}</td>
                                    <td>{{ number_format($product->price, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                    <td>
                                        @if($product->is_available)
                                            <span class="badge bg-success">متاح</span>
                                        @else
                                            <span class="badge bg-danger">غير متاح</span>
                                        @endif
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center">لا توجد بيانات</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // حالة المنتجات
        const productStatusCtx = document.getElementById('productStatusChart').getContext('2d');
        const productStatusChart = new Chart(productStatusCtx, {
            type: 'pie',
            data: {
                labels: ['متاح', 'غير متاح'],
                datasets: [{
                    data: [{{ $availableProducts }}, {{ $unavailableProducts }}],
                    backgroundColor: ['#198754', '#dc3545'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // المنتجات حسب التصنيف
        const productsByCategoryCtx = document.getElementById('productsByCategoryChart').getContext('2d');
        const productsByCategoryChart = new Chart(productsByCategoryCtx, {
            type: 'bar',
            data: {
                labels: [
                    @foreach($productsByCategory as $categoryData)
                        '{{ $categoryData['category_name'] }}',
                    @endforeach
                ],
                datasets: [{
                    label: 'إجمالي المنتجات',
                    data: [
                        @foreach($productsByCategory as $categoryData)
                            {{ $categoryData['count'] }},
                        @endforeach
                    ],
                    backgroundColor: '#0d6efd',
                    borderWidth: 1
                }, {
                    label: 'المنتجات المتاحة',
                    data: [
                        @foreach($productsByCategory as $categoryData)
                            {{ $categoryData['available'] }},
                        @endforeach
                    ],
                    backgroundColor: '#198754',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    });
</script>
@endsection
