@extends('layouts.app')

@section('title', 'تعديل إعدادات النظام')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-edit me-2"></i>
                        تعديل إعدادات النظام
                    </h3>
                    <a href="{{ route('settings.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        العودة
                    </a>
                </div>

                <div class="card-body">
                    @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-1"></i>
                            <strong>يرجى تصحيح الأخطاء التالية:</strong>
                            <ul class="mb-0 mt-2">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <form action="{{ route('settings.update') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <!-- معلومات المطعم -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-store me-2"></i>
                                    معلومات المطعم
                                </h5>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="restaurant_name" class="form-label">اسم المطعم <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="restaurant_name" name="restaurant_name" 
                                           value="{{ old('restaurant_name', $settings['restaurant']->where('key', 'restaurant_name')->first()->value ?? '') }}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="restaurant_phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="restaurant_phone" name="restaurant_phone" 
                                           value="{{ old('restaurant_phone', $settings['restaurant']->where('key', 'restaurant_phone')->first()->value ?? '') }}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="restaurant_email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="restaurant_email" name="restaurant_email" 
                                           value="{{ old('restaurant_email', $settings['restaurant']->where('key', 'restaurant_email')->first()->value ?? '') }}" required>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="restaurant_address" class="form-label">عنوان المطعم <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="restaurant_address" name="restaurant_address" rows="3" required>{{ old('restaurant_address', $settings['restaurant']->where('key', 'restaurant_address')->first()->value ?? '') }}</textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="restaurant_website" class="form-label">الموقع الإلكتروني</label>
                                    <input type="url" class="form-control" id="restaurant_website" name="restaurant_website" 
                                           value="{{ old('restaurant_website', $settings['restaurant']->where('key', 'restaurant_website')->first()->value ?? '') }}">
                                </div>
                            </div>

                            <!-- الشعار -->
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="restaurant_logo" class="form-label">شعار المطعم</label>
                                    @php
                                        $currentLogo = $settings['restaurant']->where('key', 'restaurant_logo')->first()->value ?? null;
                                    @endphp
                                    
                                    @if($currentLogo)
                                        <div class="current-logo mb-3">
                                            <p class="text-muted mb-2">الشعار الحالي:</p>
                                            <div class="d-flex align-items-center">
                                                <img src="{{ asset('storage/' . $currentLogo) }}" alt="شعار المطعم" class="img-thumbnail me-3" style="max-height: 100px;">
                                                <button type="button" class="btn btn-danger btn-sm" onclick="deleteLogo()">
                                                    <i class="fas fa-trash me-1"></i>
                                                    حذف الشعار
                                                </button>
                                            </div>
                                        </div>
                                    @endif
                                    
                                    <input type="file" class="form-control" id="restaurant_logo" name="restaurant_logo" accept="image/*">
                                    <div class="form-text">يُفضل أن يكون الشعار بصيغة PNG أو JPG وبحجم لا يزيد عن 2 ميجابايت</div>
                                </div>
                            </div>
                        </div>

                        <!-- الإعدادات المالية -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-success border-bottom pb-2">
                                    <i class="fas fa-money-bill-wave me-2"></i>
                                    الإعدادات المالية
                                </h5>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="currency" class="form-label">رمز العملة <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="currency" name="currency" 
                                           value="{{ old('currency', $settings['financial']->where('key', 'currency')->first()->value ?? '') }}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="currency_code" class="form-label">كود العملة <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="currency_code" name="currency_code" maxlength="3"
                                           value="{{ old('currency_code', $settings['financial']->where('key', 'currency_code')->first()->value ?? '') }}" required>
                                    <div class="form-text">مثال: SAR, USD, EUR</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tax_rate" class="form-label">معدل الضريبة (%) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="tax_rate" name="tax_rate" min="0" max="100" step="0.01"
                                           value="{{ old('tax_rate', $settings['financial']->where('key', 'tax_rate')->first()->value ?? '') }}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="tax_number" class="form-label">الرقم الضريبي</label>
                                    <input type="text" class="form-control" id="tax_number" name="tax_number" 
                                           value="{{ old('tax_number', $settings['financial']->where('key', 'tax_number')->first()->value ?? '') }}">
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات الطباعة -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-info border-bottom pb-2">
                                    <i class="fas fa-print me-2"></i>
                                    إعدادات الطباعة
                                </h5>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="default_print_size" class="form-label">حجم الطباعة الافتراضي <span class="text-danger">*</span></label>
                                    <select class="form-select" id="default_print_size" name="default_print_size" required>
                                        @php
                                            $currentPrintSize = $settings['printing']->where('key', 'default_print_size')->first()->value ?? '85mm';
                                        @endphp
                                        <option value="85mm" {{ old('default_print_size', $currentPrintSize) == '85mm' ? 'selected' : '' }}>85mm</option>
                                        <option value="56mm" {{ old('default_print_size', $currentPrintSize) == '56mm' ? 'selected' : '' }}>56mm</option>
                                        <option value="A4" {{ old('default_print_size', $currentPrintSize) == 'A4' ? 'selected' : '' }}>A4</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        @php
                                            $printLogo = $settings['printing']->where('key', 'print_logo')->first()->value ?? '0';
                                        @endphp
                                        <input class="form-check-input" type="checkbox" id="print_logo" name="print_logo" value="1" 
                                               {{ old('print_logo', $printLogo) == '1' ? 'checked' : '' }}>
                                        <label class="form-check-label" for="print_logo">
                                            طباعة الشعار في الفواتير
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        @php
                                            $autoPrintKitchen = $settings['printing']->where('key', 'auto_print_kitchen_order')->first()->value ?? '0';
                                        @endphp
                                        <input class="form-check-input" type="checkbox" id="auto_print_kitchen_order" name="auto_print_kitchen_order" value="1" 
                                               {{ old('auto_print_kitchen_order', $autoPrintKitchen) == '1' ? 'checked' : '' }}>
                                        <label class="form-check-label" for="auto_print_kitchen_order">
                                            طباعة طلب المطبخ تلقائياً
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end">
                                    <a href="{{ route('settings.index') }}" class="btn btn-secondary me-2">
                                        <i class="fas fa-times me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ التغييرات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deleteLogo() {
    if (confirm('هل أنت متأكد من حذف الشعار؟')) {
        fetch('{{ route("settings.delete-logo") }}', {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف الشعار');
        });
    }
}
</script>
@endsection
