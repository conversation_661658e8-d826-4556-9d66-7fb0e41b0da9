<?php $__env->startSection('title', 'تعديل الطلب'); ?>

<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>تعديل الطلب رقم: <?php echo e($order->id); ?></h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?php echo e(route('orders.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
            </a>
            <a href="<?php echo e(route('orders.show', $order->id)); ?>" class="btn btn-info">
                <i class="fas fa-eye me-1"></i> عرض الطلب
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">معلومات الطلب</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 40%">رقم الطلب</th>
                            <td><?php echo e($order->id); ?></td>
                        </tr>
                        <tr>
                            <th>الطاولة</th>
                            <td>
                                <?php if($order->table): ?>
                                    <a href="<?php echo e(route('tables.show', $order->table->id)); ?>"><?php echo e($order->table->name); ?></a>
                                <?php else: ?>
                                    غير محدد
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>النادل</th>
                            <td>
                                <?php if($order->user): ?>
                                    <a href="<?php echo e(route('users.show', $order->user->id)); ?>"><?php echo e($order->user->name); ?></a>
                                <?php else: ?>
                                    غير محدد
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>المبلغ الإجمالي</th>
                            <td class="fw-bold"><?php echo e(number_format($order->total_amount, 2)); ?> ر.س</td>
                        </tr>
                        <tr>
                            <th>تاريخ الإنشاء</th>
                            <td><?php echo e($order->created_at->format('Y-m-d H:i')); ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">تحديث الطلب</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('orders.update', $order->id)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="status" class="form-label">الحالة <span class="text-danger">*</span></label>
                                <select class="form-select <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="status" name="status" required>
                                    <option value="pending" <?php echo e(old('status', $order->status) == 'pending' ? 'selected' : ''); ?>>قيد الانتظار</option>
                                    <option value="preparing" <?php echo e(old('status', $order->status) == 'preparing' ? 'selected' : ''); ?>>قيد التحضير</option>
                                    <option value="ready" <?php echo e(old('status', $order->status) == 'ready' ? 'selected' : ''); ?>>جاهز</option>
                                    <option value="delivered" <?php echo e(old('status', $order->status) == 'delivered' ? 'selected' : ''); ?>>تم التوصيل</option>
                                    <option value="completed" <?php echo e(old('status', $order->status) == 'completed' ? 'selected' : ''); ?>>مكتمل</option>
                                    <option value="cancelled" <?php echo e(old('status', $order->status) == 'cancelled' ? 'selected' : ''); ?>>ملغي</option>
                                </select>
                                <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="notes" name="notes" rows="3"><?php echo e(old('notes', $order->notes)); ?></textarea>
                                <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            <strong>تنبيه:</strong> تغيير حالة الطلب إلى "مكتمل" أو "ملغي" سيؤدي إلى تحرير الطاولة وجعلها متاحة مرة أخرى.
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i> حفظ التغييرات
                                </button>
                                <a href="<?php echo e(route('orders.show', $order->id)); ?>" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i> إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card shadow-sm mt-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">عناصر الطلب</h5>
                </div>
                <div class="card-body">
                    <?php if($order->orderItems->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>#</th>
                                        <th>المنتج</th>
                                        <th>السعر</th>
                                        <th>الكمية</th>
                                        <th>المجموع</th>
                                        <th>ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $order->orderItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($index + 1); ?></td>
                                        <td>
                                            <?php if($item->product): ?>
                                                <a href="<?php echo e(route('products.show', $item->product->id)); ?>"><?php echo e($item->product->name); ?></a>
                                            <?php else: ?>
                                                منتج محذوف
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e(number_format($item->unit_price, 2)); ?> ر.س</td>
                                        <td><?php echo e($item->quantity); ?></td>
                                        <td><?php echo e(number_format($item->subtotal, 2)); ?> ر.س</td>
                                        <td><?php echo e($item->notes ?? '-'); ?></td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th colspan="4" class="text-end">المجموع الكلي:</th>
                                        <th><?php echo e(number_format($order->total_amount, 2)); ?> ر.س</th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            لا توجد عناصر في هذا الطلب
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\restaurant\resources\views/orders/edit.blade.php ENDPATH**/ ?>