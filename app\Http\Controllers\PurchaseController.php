<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Purchase;
use App\Models\PurchaseItem;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class PurchaseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Purchase::with('user')->latest();

        // البحث
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // فلترة حسب حالة الدفع
        if ($request->filled('payment_status')) {
            $query->byPaymentStatus($request->payment_status);
        }

        // فلترة حسب التاريخ
        if ($request->filled('start_date') && $request->filled('end_date')) {
            $query->byDateRange($request->start_date, $request->end_date);
        }

        $purchases = $query->paginate(15);

        // إحصائيات
        $totalPurchases = Purchase::sum('total_amount');
        $monthlyPurchases = Purchase::whereMonth('purchase_date', now()->month)
                                  ->whereYear('purchase_date', now()->year)
                                  ->sum('total_amount');
        $pendingPayments = Purchase::where('payment_status', 'pending')->sum('remaining_amount');
        $todayPurchases = Purchase::whereDate('purchase_date', today())->sum('total_amount');

        $paymentStatuses = Purchase::getPaymentStatuses();

        return view('purchases.index', compact(
            'purchases',
            'totalPurchases',
            'monthlyPurchases',
            'pendingPayments',
            'todayPurchases',
            'paymentStatuses'
        ));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $paymentMethods = Purchase::getPaymentMethods();
        $paymentStatuses = Purchase::getPaymentStatuses();
        $deliveryStatuses = Purchase::getDeliveryStatuses();
        $units = PurchaseItem::getUnits();
        $categories = PurchaseItem::getCategories();

        return view('purchases.create', compact(
            'paymentMethods',
            'paymentStatuses',
            'deliveryStatuses',
            'units',
            'categories'
        ));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'supplier_name' => 'required|string|max:255',
            'supplier_phone' => 'nullable|string|max:20',
            'supplier_address' => 'nullable|string',
            'purchase_date' => 'required|date',
            'payment_method' => 'required|string',
            'payment_status' => 'required|string',
            'delivery_status' => 'required|string',
            'delivery_date' => 'nullable|date',
            'invoice_number' => 'nullable|string|max:255',
            'paid_amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.item_name' => 'required|string|max:255',
            'items.*.item_description' => 'nullable|string',
            'items.*.unit' => 'required|string',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.expiry_date' => 'nullable|date',
            'items.*.category' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();

            // إنشاء المشترى
            $purchase = new Purchase();
            $purchase->purchase_number = Purchase::generatePurchaseNumber();
            $purchase->supplier_name = $request->supplier_name;
            $purchase->supplier_phone = $request->supplier_phone;
            $purchase->supplier_address = $request->supplier_address;
            $purchase->purchase_date = $request->purchase_date;
            $purchase->payment_method = $request->payment_method;
            $purchase->payment_status = $request->payment_status;
            $purchase->delivery_status = $request->delivery_status;
            $purchase->delivery_date = $request->delivery_date;
            $purchase->invoice_number = $request->invoice_number;
            $purchase->paid_amount = $request->paid_amount;
            $purchase->notes = $request->notes;
            $purchase->user_id = Auth::id();

            // حساب المجموع الكلي
            $totalAmount = 0;
            foreach ($request->items as $item) {
                $totalAmount += $item['quantity'] * $item['unit_price'];
            }

            $purchase->total_amount = $totalAmount;
            $purchase->remaining_amount = $totalAmount - $request->paid_amount;
            $purchase->save();

            // إضافة العناصر
            foreach ($request->items as $item) {
                $purchaseItem = new PurchaseItem();
                $purchaseItem->purchase_id = $purchase->id;
                $purchaseItem->item_name = $item['item_name'];
                $purchaseItem->item_description = $item['item_description'];
                $purchaseItem->unit = $item['unit'];
                $purchaseItem->quantity = $item['quantity'];
                $purchaseItem->unit_price = $item['unit_price'];
                $purchaseItem->total_price = $item['quantity'] * $item['unit_price'];
                $purchaseItem->expiry_date = $item['expiry_date'];
                $purchaseItem->category = $item['category'];
                $purchaseItem->save();
            }

            // تحديث حالة الدفع
            $purchase->updateRemainingAmount();

            DB::commit();

            return redirect()->route('purchases.index')
                ->with('success', 'تم إضافة المشترى بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء إضافة المشترى: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Purchase $purchase)
    {
        $purchase->load('items', 'user');
        return view('purchases.show', compact('purchase'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Purchase $purchase)
    {
        $purchase->load('items');
        $paymentMethods = Purchase::getPaymentMethods();
        $paymentStatuses = Purchase::getPaymentStatuses();
        $deliveryStatuses = Purchase::getDeliveryStatuses();
        $units = PurchaseItem::getUnits();
        $categories = PurchaseItem::getCategories();

        return view('purchases.edit', compact(
            'purchase',
            'paymentMethods',
            'paymentStatuses',
            'deliveryStatuses',
            'units',
            'categories'
        ));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Purchase $purchase)
    {
        $validator = Validator::make($request->all(), [
            'supplier_name' => 'required|string|max:255',
            'supplier_phone' => 'nullable|string|max:20',
            'supplier_address' => 'nullable|string',
            'purchase_date' => 'required|date',
            'payment_method' => 'required|string',
            'payment_status' => 'required|string',
            'delivery_status' => 'required|string',
            'delivery_date' => 'nullable|date',
            'invoice_number' => 'nullable|string|max:255',
            'paid_amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.item_name' => 'required|string|max:255',
            'items.*.item_description' => 'nullable|string',
            'items.*.unit' => 'required|string',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.expiry_date' => 'nullable|date',
            'items.*.category' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            DB::beginTransaction();

            // تحديث المشترى
            $purchase->supplier_name = $request->supplier_name;
            $purchase->supplier_phone = $request->supplier_phone;
            $purchase->supplier_address = $request->supplier_address;
            $purchase->purchase_date = $request->purchase_date;
            $purchase->payment_method = $request->payment_method;
            $purchase->payment_status = $request->payment_status;
            $purchase->delivery_status = $request->delivery_status;
            $purchase->delivery_date = $request->delivery_date;
            $purchase->invoice_number = $request->invoice_number;
            $purchase->paid_amount = $request->paid_amount;
            $purchase->notes = $request->notes;

            // حساب المجموع الكلي
            $totalAmount = 0;
            foreach ($request->items as $item) {
                $totalAmount += $item['quantity'] * $item['unit_price'];
            }

            $purchase->total_amount = $totalAmount;
            $purchase->remaining_amount = $totalAmount - $request->paid_amount;
            $purchase->save();

            // حذف العناصر القديمة
            $purchase->items()->delete();

            // إضافة العناصر الجديدة
            foreach ($request->items as $item) {
                $purchaseItem = new PurchaseItem();
                $purchaseItem->purchase_id = $purchase->id;
                $purchaseItem->item_name = $item['item_name'];
                $purchaseItem->item_description = $item['item_description'];
                $purchaseItem->unit = $item['unit'];
                $purchaseItem->quantity = $item['quantity'];
                $purchaseItem->unit_price = $item['unit_price'];
                $purchaseItem->total_price = $item['quantity'] * $item['unit_price'];
                $purchaseItem->expiry_date = $item['expiry_date'];
                $purchaseItem->category = $item['category'];
                $purchaseItem->save();
            }

            // تحديث حالة الدفع
            $purchase->updateRemainingAmount();

            DB::commit();

            return redirect()->route('purchases.index')
                ->with('success', 'تم تحديث المشترى بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء تحديث المشترى: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Purchase $purchase)
    {
        try {
            $purchase->delete();

            return redirect()->route('purchases.index')
                ->with('success', 'تم حذف المشترى بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء حذف المشترى: ' . $e->getMessage());
        }
    }
}
