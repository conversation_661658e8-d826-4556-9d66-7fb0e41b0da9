<template>
  <Link
    :href="href"
    :class="[
      'inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200',
      active
        ? 'text-primary-600 bg-primary-50 dark:text-primary-400 dark:bg-primary-900/50'
        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700'
    ]"
  >
    <slot />
  </Link>
</template>

<script setup>
import { Link } from '@inertiajs/vue3'

defineProps({
  href: String,
  active: {
    type: Boolean,
    default: false
  }
})
</script>
