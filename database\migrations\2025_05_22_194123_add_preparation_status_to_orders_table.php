<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // إضافة عمود preparation_status إذا لم يكن موجود
            if (!Schema::hasColumn('orders', 'preparation_status')) {
                $table->enum('preparation_status', ['pending', 'preparing', 'ready', 'delivered', 'completed'])
                      ->nullable()
                      ->after('status')
                      ->comment('حالة التحضير للطلبات المعلقة');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // حذف عمود preparation_status إذا كان موجود
            if (Schema::hasColumn('orders', 'preparation_status')) {
                $table->dropColumn('preparation_status');
            }
        });
    }
};
