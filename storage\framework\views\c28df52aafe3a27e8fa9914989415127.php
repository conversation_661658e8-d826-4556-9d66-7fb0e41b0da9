<?php $__env->startSection('title', 'عرض المستخدم'); ?>

<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>عرض المستخدم: <?php echo e($user->name); ?></h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?php echo e(route('users.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
            </a>
            <a href="<?php echo e(route('users.edit', $user->id)); ?>" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="card-title">معلومات المستخدم</h5>
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 30%">الاسم</th>
                            <td><?php echo e($user->name); ?></td>
                        </tr>
                        <tr>
                            <th>البريد الإلكتروني</th>
                            <td><?php echo e($user->email); ?></td>
                        </tr>
                        <tr>
                            <th>الدور</th>
                            <td>
                                <span class="badge bg-info"><?php echo e($user->role_name); ?></span>
                                <?php if($user->getUserRole()): ?>
                                <p class="text-muted small mb-0"><?php echo e($user->getUserRole()->description); ?></p>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>تاريخ الإنشاء</th>
                            <td><?php echo e($user->created_at->format('Y-m-d H:i')); ?></td>
                        </tr>
                        <tr>
                            <th>آخر تحديث</th>
                            <td><?php echo e($user->updated_at->format('Y-m-d H:i')); ?></td>
                        </tr>
                    </table>
                </div>

                <div class="col-md-6">
                    <h5 class="card-title">النشاط</h5>
                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <i class="fas fa-clipboard-list me-1"></i> الطلبات
                        </div>
                        <div class="card-body">
                            <p>عدد الطلبات: <?php echo e($user->orders->count()); ?></p>
                            <?php if($user->orders->count() > 0): ?>
                                <a href="#" class="btn btn-sm btn-outline-primary">عرض الطلبات</a>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header bg-light">
                            <i class="fas fa-file-invoice-dollar me-1"></i> الفواتير
                        </div>
                        <div class="card-body">
                            <p>عدد الفواتير: <?php echo e($user->invoices->count()); ?></p>
                            <?php if($user->invoices->count() > 0): ?>
                                <a href="#" class="btn btn-sm btn-outline-primary">عرض الفواتير</a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\restaurant\resources\views/users/show.blade.php ENDPATH**/ ?>