<?php

namespace Database\Factories;

use App\Models\Category;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $arabicFoodNames = [
            // مقبلات
            'حمص', 'متبل', 'تبولة', 'فتوش', 'بابا غنوج', 'سمبوسك', 'كبة', 'فلافل',
            // سلطات
            'سلطة خضراء', 'سلطة فواكه', 'سلطة سيزر', 'سلطة يونانية', 'سلطة الشمندر',
            // أطباق رئيسية
            'كبسة لحم', 'مندي دجاج', 'برياني', 'مقلوبة', 'ملوخية', 'محشي', 'مسخن', 'مجبوس',
            // مشاوي
            'شيش طاووق', 'كباب', 'كفتة', 'شقف لحم', 'ريش', 'فروج مشوي', 'مشاوي مشكلة',
            // حلويات
            'كنافة', 'بقلاوة', 'قطايف', 'أم علي', 'مهلبية', 'بسبوسة', 'هريسة',
            // مشروبات
            'قهوة عربية', 'شاي', 'عصير برتقال', 'عصير مانجو', 'عصير فراولة', 'ليموناضة', 'كوكتيل'
        ];
        
        static $index = 0;
        
        return [
            'name' => $arabicFoodNames[$index++ % count($arabicFoodNames)],
            'description' => $this->faker->paragraph(1),
            'price' => $this->faker->randomFloat(2, 10, 200),
            'image' => null,
            'category_id' => Category::inRandomOrder()->first()->id ?? 1,
            'is_available' => $this->faker->boolean(80), // 80% chance of being available
        ];
    }

    /**
     * Indicate that the product is available.
     */
    public function available(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_available' => true,
        ]);
    }
}
