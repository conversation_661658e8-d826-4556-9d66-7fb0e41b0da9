<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة #<?php echo e($invoice->invoice_number); ?></title>
    <style>
        @page {
            size: 85mm auto;
            margin: 0;
        }
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 5mm;
            font-size: 10px;
            width: 75mm;
        }
        .receipt {
            width: 100%;
        }
        .header {
            text-align: center;
            margin-bottom: 5mm;
            border-bottom: 1px dashed #000;
            padding-bottom: 3mm;
        }
        .header h1 {
            font-size: 14px;
            margin: 0 0 2mm 0;
        }
        .header p {
            margin: 0;
            font-size: 10px;
        }
        .info {
            margin-bottom: 5mm;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1mm;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 5mm;
        }
        table th, table td {
            text-align: right;
            padding: 1mm;
        }
        table th {
            border-bottom: 1px solid #000;
        }
        .totals {
            margin-top: 3mm;
            border-top: 1px dashed #000;
            padding-top: 3mm;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1mm;
        }
        .footer {
            text-align: center;
            margin-top: 5mm;
            border-top: 1px dashed #000;
            padding-top: 3mm;
        }
        .no-print {
            display: none;
        }
        @media print {
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="no-print" style="padding: 10px; background: #f0f0f0; margin-bottom: 10px;">
        <button onclick="window.print()">طباعة</button>
        <a href="<?php echo e(route('invoices.show', $invoice->id)); ?>">العودة</a>
    </div>

    <div class="receipt">
        <div class="header">
            <?php
                $restaurantInfo = \App\Models\Setting::getRestaurantInfo();
                $logo = $restaurantInfo['logo'];
                $printLogo = \App\Models\Setting::get('print_logo', true);
            ?>

            <?php if($printLogo && $logo): ?>
                <div style="text-align: center; margin-bottom: 6px;">
                    <img src="<?php echo e(asset('storage/' . $logo)); ?>" alt="شعار المطعم" style="max-height: 25px; max-width: 120px;">
                </div>
            <?php endif; ?>

            <h1><?php echo e($restaurantInfo['name']); ?></h1>
            <p><?php echo e($restaurantInfo['address']); ?></p>
            <p>هاتف: <?php echo e($restaurantInfo['phone']); ?></p>
            <?php if($restaurantInfo['email']): ?>
            <p><?php echo e($restaurantInfo['email']); ?></p>
            <?php endif; ?>
            <?php if($restaurantInfo['tax_number']): ?>
            <p>الرقم الضريبي: <?php echo e($restaurantInfo['tax_number']); ?></p>
            <?php endif; ?>
            <p>فاتورة ضريبية مبسطة</p>
            <p>رقم الفاتورة: <?php echo e($invoice->invoice_number); ?></p>
            <p>التاريخ: <?php echo e($invoice->created_at->format('Y-m-d H:i')); ?></p>
        </div>

        <div class="info">
            <div class="info-row">
                <span>رقم الطلب:</span>
                <span><?php echo e($invoice->order->id ?? 'غير محدد'); ?></span>
            </div>
            <div class="info-row">
                <span>
                    <?php if($invoice->order && $invoice->order->table): ?>
                        الطاولة:
                    <?php elseif($invoice->order && $invoice->order->order_type == 'takeaway'): ?>
                        نوع الطلب:
                    <?php endif; ?>
                </span>
                <span>
                    <?php if($invoice->order && $invoice->order->table): ?>
                        <?php echo e($invoice->order->table->name); ?>

                    <?php elseif($invoice->order && $invoice->order->order_type == 'takeaway'): ?>
                        طلب خارجي
                    <?php endif; ?>
                </span>
            </div>
            <?php if($invoice->order && $invoice->order->order_type == 'takeaway' && $invoice->order->car_number): ?>
            <div class="info-row">
                <span>رقم السيارة:</span>
                <span><?php echo e($invoice->order->car_number); ?></span>
            </div>
            <?php endif; ?>
            <?php if($invoice->order && $invoice->order->order_type == 'takeaway' && $invoice->order->customer_phone): ?>
            <div class="info-row">
                <span>رقم الهاتف:</span>
                <span><?php echo e($invoice->order->customer_phone); ?></span>
            </div>
            <?php endif; ?>
            <div class="info-row">
                <span>طريقة الدفع:</span>
                <span>
                    <?php if($invoice->payment_method == 'cash'): ?>
                        نقداً
                    <?php elseif($invoice->payment_method == 'card'): ?>
                        بطاقة
                    <?php else: ?>
                        <?php echo e($invoice->payment_method); ?>

                    <?php endif; ?>
                </span>
            </div>
        </div>

        <table>
            <thead>
                <tr>
                    <th style="width: 40%;">المنتج</th>
                    <th style="width: 20%;">السعر</th>
                    <th style="width: 15%;">الكمية</th>
                    <th style="width: 25%;">المجموع</th>
                </tr>
            </thead>
            <tbody>
                <?php if($invoice->order && $invoice->order->orderItems->count() > 0): ?>
                    <?php $__currentLoopData = $invoice->order->orderItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td>
                            <?php if($item->product): ?>
                                <?php echo e($item->product->name); ?>

                                <?php if($item->notes): ?>
                                    <br><small><?php echo e($item->notes); ?></small>
                                <?php endif; ?>
                            <?php else: ?>
                                منتج محذوف
                            <?php endif; ?>
                        </td>
                        <td><?php echo e(number_format($item->unit_price, 2)); ?> <?php echo e($restaurantInfo['currency']); ?></td>
                        <td><?php echo e($item->quantity); ?></td>
                        <td><?php echo e(number_format($item->subtotal, 2)); ?> <?php echo e($restaurantInfo['currency']); ?></td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            </tbody>
        </table>

        <div class="totals">
            <div class="total-row">
                <span>المبلغ الإجمالي:</span>
                <span><?php echo e(number_format($invoice->total_amount ?? 0, 2)); ?> <?php echo e($restaurantInfo['currency']); ?></span>
            </div>
            <?php if($invoice->tax_amount && $invoice->tax_amount > 0): ?>
            <div class="total-row">
                <span>ضريبة القيمة المضافة (<?php echo e($restaurantInfo['tax_rate']); ?>%):</span>
                <span><?php echo e(number_format($invoice->tax_amount, 2)); ?> <?php echo e($restaurantInfo['currency']); ?></span>
            </div>
            <?php endif; ?>
            <?php if($invoice->discount_amount && $invoice->discount_amount > 0): ?>
            <div class="total-row">
                <span>الخصم:</span>
                <span><?php echo e(number_format($invoice->discount_amount, 2)); ?> <?php echo e($restaurantInfo['currency']); ?></span>
            </div>
            <?php endif; ?>
            <div class="total-row">
                <span>المبلغ النهائي:</span>
                <span><?php echo e(number_format($invoice->final_amount ?? $invoice->total_amount ?? 0, 2)); ?> <?php echo e($restaurantInfo['currency']); ?></span>
            </div>
            <div class="total-row">
                <span>المبلغ المدفوع:</span>
                <span><?php echo e(number_format($invoice->paid_amount ?? 0, 2)); ?> <?php echo e($restaurantInfo['currency']); ?></span>
            </div>
            <?php if($invoice->remaining_amount && $invoice->remaining_amount > 0): ?>
            <div class="total-row">
                <span>المبلغ المتبقي:</span>
                <span><?php echo e(number_format($invoice->remaining_amount, 2)); ?> <?php echo e($restaurantInfo['currency']); ?></span>
            </div>
            <?php endif; ?>
        </div>

        <div class="footer">
            <p>شكراً لزيارتكم!</p>
            <p>نتطلع لرؤيتكم مرة أخرى</p>
            <p><?php echo e(now()->format('Y-m-d H:i:s')); ?></p>
        </div>
    </div>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\restaurant\resources\views/invoices/print_85mm.blade.php ENDPATH**/ ?>