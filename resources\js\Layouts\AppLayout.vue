<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Navigation -->
    <nav class="bg-white dark:bg-gray-800 shadow-lg border-b border-gray-200 dark:border-gray-700">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <!-- Logo -->
            <div class="flex-shrink-0 flex items-center">
              <img class="h-8 w-auto" src="/images/logo.png" alt="نظام إدارة المطعم" />
              <span class="ml-2 text-xl font-bold text-gray-900 dark:text-white">نظام إدارة المطعم</span>
            </div>

            <!-- Navigation Links -->
            <div class="hidden md:ml-6 md:flex md:space-x-8 md:space-x-reverse">
              <NavLink :href="route('dashboard')" :active="route().current('dashboard')">
                <HomeIcon class="w-5 h-5 ml-2" />
                لوحة التحكم
              </NavLink>
              
              <NavLink :href="route('orders.index')" :active="route().current('orders.*')">
                <ShoppingCartIcon class="w-5 h-5 ml-2" />
                الطلبات
              </NavLink>
              
              <NavLink :href="route('products.index')" :active="route().current('products.*')">
                <CubeIcon class="w-5 h-5 ml-2" />
                المنتجات
              </NavLink>
              
              <NavLink :href="route('invoices.index')" :active="route().current('invoices.*')">
                <DocumentTextIcon class="w-5 h-5 ml-2" />
                الفواتير
              </NavLink>
            </div>
          </div>

          <!-- Right side -->
          <div class="flex items-center space-x-4 space-x-reverse">
            <!-- Dark mode toggle -->
            <button @click="toggleDarkMode" class="p-2 rounded-md text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
              <SunIcon v-if="isDark" class="w-5 h-5" />
              <MoonIcon v-else class="w-5 h-5" />
            </button>

            <!-- Notifications -->
            <div class="relative">
              <button class="p-2 rounded-md text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                <BellIcon class="w-5 h-5" />
                <span v-if="notificationCount > 0" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {{ notificationCount }}
                </span>
              </button>
            </div>

            <!-- User menu -->
            <div class="relative">
              <Menu as="div" class="relative">
                <MenuButton class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                  <UserCircleIcon class="w-8 h-8 text-gray-500 dark:text-gray-400" />
                  <span class="ml-2 text-gray-700 dark:text-gray-300">{{ $page.props.auth.user.name }}</span>
                  <ChevronDownIcon class="w-4 h-4 ml-1 text-gray-500" />
                </MenuButton>
                
                <transition
                  enter-active-class="transition ease-out duration-100"
                  enter-from-class="transform opacity-0 scale-95"
                  enter-to-class="transform opacity-100 scale-100"
                  leave-active-class="transition ease-in duration-75"
                  leave-from-class="transform opacity-100 scale-100"
                  leave-to-class="transform opacity-0 scale-95"
                >
                  <MenuItems class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                    <div class="py-1">
                      <MenuItem v-slot="{ active }">
                        <Link :href="route('settings.index')" :class="[active ? 'bg-gray-100 dark:bg-gray-700' : '', 'block px-4 py-2 text-sm text-gray-700 dark:text-gray-300']">
                          الإعدادات
                        </Link>
                      </MenuItem>
                      <MenuItem v-slot="{ active }">
                        <Link :href="route('logout')" method="post" :class="[active ? 'bg-gray-100 dark:bg-gray-700' : '', 'block px-4 py-2 text-sm text-gray-700 dark:text-gray-300']">
                          تسجيل الخروج
                        </Link>
                      </MenuItem>
                    </div>
                  </MenuItems>
                </transition>
              </Menu>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- Page Content -->
    <main class="py-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Flash Messages -->
        <div v-if="$page.props.flash.success" class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded animate-fade-in">
          {{ $page.props.flash.success }}
        </div>
        
        <div v-if="$page.props.flash.error" class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded animate-fade-in">
          {{ $page.props.flash.error }}
        </div>

        <!-- Page Header -->
        <div v-if="$slots.header" class="mb-6">
          <slot name="header" />
        </div>

        <!-- Page Content -->
        <slot />
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Link } from '@inertiajs/vue3'
import { Menu, MenuButton, MenuItems, MenuItem } from '@headlessui/vue'
import {
  HomeIcon,
  ShoppingCartIcon,
  CubeIcon,
  DocumentTextIcon,
  BellIcon,
  UserCircleIcon,
  ChevronDownIcon,
  SunIcon,
  MoonIcon
} from '@heroicons/vue/24/outline'
import NavLink from '@/Components/NavLink.vue'

const isDark = ref(false)
const notificationCount = ref(0)

const toggleDarkMode = () => {
  isDark.value = !isDark.value
  if (isDark.value) {
    document.documentElement.classList.add('dark')
    localStorage.setItem('darkMode', 'true')
  } else {
    document.documentElement.classList.remove('dark')
    localStorage.setItem('darkMode', 'false')
  }
}

onMounted(() => {
  // Check for saved dark mode preference
  const savedDarkMode = localStorage.getItem('darkMode')
  if (savedDarkMode === 'true') {
    isDark.value = true
    document.documentElement.classList.add('dark')
  }
})
</script>
