<?php $__env->startSection('title', 'عرض المشترى'); ?>

<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>عرض المشترى: <?php echo e($purchase->purchase_number); ?></h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?php echo e(route('purchases.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
            </a>
            <?php if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('manager')): ?>
            <a href="<?php echo e(route('purchases.edit', $purchase->id)); ?>" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
            <?php endif; ?>
            <button class="btn btn-primary" onclick="window.print()">
                <i class="fas fa-print me-1"></i> طباعة
            </button>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">معلومات المشترى</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 40%">رقم المشترى</th>
                            <td><?php echo e($purchase->purchase_number); ?></td>
                        </tr>
                        <tr>
                            <th>اسم المورد</th>
                            <td><?php echo e($purchase->supplier_name); ?></td>
                        </tr>
                        <?php if($purchase->supplier_phone): ?>
                        <tr>
                            <th>هاتف المورد</th>
                            <td><?php echo e($purchase->supplier_phone); ?></td>
                        </tr>
                        <?php endif; ?>
                        <?php if($purchase->supplier_address): ?>
                        <tr>
                            <th>عنوان المورد</th>
                            <td><?php echo e($purchase->supplier_address); ?></td>
                        </tr>
                        <?php endif; ?>
                        <tr>
                            <th>تاريخ الشراء</th>
                            <td><?php echo e($purchase->purchase_date->format('Y-m-d')); ?></td>
                        </tr>
                        <?php if($purchase->invoice_number): ?>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <td><?php echo e($purchase->invoice_number); ?></td>
                        </tr>
                        <?php endif; ?>
                        <tr>
                            <th>طريقة الدفع</th>
                            <td>
                                <span class="badge bg-info"><?php echo e($purchase->payment_method_name); ?></span>
                            </td>
                        </tr>
                        <tr>
                            <th>حالة الدفع</th>
                            <td>
                                <?php if($purchase->payment_status == 'paid'): ?>
                                    <span class="badge bg-success"><?php echo e($purchase->payment_status_name); ?></span>
                                <?php elseif($purchase->payment_status == 'partial'): ?>
                                    <span class="badge bg-warning"><?php echo e($purchase->payment_status_name); ?></span>
                                <?php elseif($purchase->payment_status == 'overdue'): ?>
                                    <span class="badge bg-danger"><?php echo e($purchase->payment_status_name); ?></span>
                                <?php else: ?>
                                    <span class="badge bg-secondary"><?php echo e($purchase->payment_status_name); ?></span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>حالة التسليم</th>
                            <td>
                                <?php if($purchase->delivery_status == 'delivered'): ?>
                                    <span class="badge bg-success"><?php echo e($purchase->delivery_status_name); ?></span>
                                <?php elseif($purchase->delivery_status == 'partial'): ?>
                                    <span class="badge bg-warning"><?php echo e($purchase->delivery_status_name); ?></span>
                                <?php elseif($purchase->delivery_status == 'cancelled'): ?>
                                    <span class="badge bg-danger"><?php echo e($purchase->delivery_status_name); ?></span>
                                <?php else: ?>
                                    <span class="badge bg-secondary"><?php echo e($purchase->delivery_status_name); ?></span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php if($purchase->delivery_date): ?>
                        <tr>
                            <th>تاريخ التسليم</th>
                            <td><?php echo e($purchase->delivery_date->format('Y-m-d')); ?></td>
                        </tr>
                        <?php endif; ?>
                        <tr>
                            <th>المستخدم</th>
                            <td>
                                <?php if($purchase->user): ?>
                                    <?php echo e($purchase->user->name); ?>

                                <?php else: ?>
                                    غير محدد
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>تاريخ الإنشاء</th>
                            <td><?php echo e($purchase->created_at->format('Y-m-d H:i')); ?></td>
                        </tr>
                    </table>

                    <?php if($purchase->notes): ?>
                    <div class="alert alert-info mt-3">
                        <h6 class="alert-heading">ملاحظات:</h6>
                        <p class="mb-0"><?php echo e($purchase->notes); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">ملخص المبالغ</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <table class="table table-bordered">
                                <tr>
                                    <th>المبلغ الإجمالي</th>
                                    <td class="text-end"><?php echo e(number_format($purchase->total_amount, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></td>
                                </tr>
                                <tr>
                                    <th>المبلغ المدفوع</th>
                                    <td class="text-end"><?php echo e(number_format($purchase->paid_amount, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></td>
                                </tr>
                                <tr class="<?php echo e($purchase->remaining_amount > 0 ? 'table-danger' : 'table-success'); ?>">
                                    <th>المبلغ المتبقي</th>
                                    <td class="text-end fw-bold"><?php echo e(number_format($purchase->remaining_amount, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">عناصر المشترى</h5>
                </div>
                <div class="card-body">
                    <?php if($purchase->items && $purchase->items->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>#</th>
                                        <th>اسم العنصر</th>
                                        <th>الوصف</th>
                                        <th>الوحدة</th>
                                        <th>الكمية</th>
                                        <th>سعر الوحدة</th>
                                        <th>المجموع</th>
                                        <th>الفئة</th>
                                        <th>تاريخ الانتهاء</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $purchase->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($index + 1); ?></td>
                                        <td><?php echo e($item->item_name); ?></td>
                                        <td><?php echo e($item->item_description ?? '-'); ?></td>
                                        <td><?php echo e($item->unit); ?></td>
                                        <td><?php echo e(number_format($item->quantity, 2)); ?></td>
                                        <td><?php echo e(number_format($item->unit_price, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></td>
                                        <td><?php echo e(number_format($item->total_price, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></td>
                                        <td><?php echo e($item->category ?? '-'); ?></td>
                                        <td>
                                            <?php if($item->expiry_date): ?>
                                                <?php echo e($item->expiry_date->format('Y-m-d')); ?>

                                                <?php if($item->expiry_date->isPast()): ?>
                                                    <span class="badge bg-danger ms-1">منتهي الصلاحية</span>
                                                <?php elseif($item->expiry_date->diffInDays(now()) <= 30): ?>
                                                    <span class="badge bg-warning ms-1">قريب الانتهاء</span>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th colspan="6" class="text-end">المجموع الكلي:</th>
                                        <th><?php echo e(number_format($purchase->total_amount, 2)); ?> <?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></th>
                                        <th colspan="2"></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            لا توجد عناصر في هذا المشترى
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .card-header {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .container {
        max-width: 100% !important;
    }
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\restaurant\resources\views/purchases/show.blade.php ENDPATH**/ ?>