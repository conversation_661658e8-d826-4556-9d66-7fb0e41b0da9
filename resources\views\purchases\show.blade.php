@extends('layouts.app')

@section('title', 'عرض المشترى')

@section('content')
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>عرض المشترى: {{ $purchase->purchase_number }}</h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('purchases.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
            </a>
            @if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('manager'))
            <a href="{{ route('purchases.edit', $purchase->id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
            @endif
            <button class="btn btn-primary" onclick="window.print()">
                <i class="fas fa-print me-1"></i> طباعة
            </button>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">معلومات المشترى</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 40%">رقم المشترى</th>
                            <td>{{ $purchase->purchase_number }}</td>
                        </tr>
                        <tr>
                            <th>اسم المورد</th>
                            <td>{{ $purchase->supplier_name }}</td>
                        </tr>
                        @if($purchase->supplier_phone)
                        <tr>
                            <th>هاتف المورد</th>
                            <td>{{ $purchase->supplier_phone }}</td>
                        </tr>
                        @endif
                        @if($purchase->supplier_address)
                        <tr>
                            <th>عنوان المورد</th>
                            <td>{{ $purchase->supplier_address }}</td>
                        </tr>
                        @endif
                        <tr>
                            <th>تاريخ الشراء</th>
                            <td>{{ $purchase->purchase_date->format('Y-m-d') }}</td>
                        </tr>
                        @if($purchase->invoice_number)
                        <tr>
                            <th>رقم الفاتورة</th>
                            <td>{{ $purchase->invoice_number }}</td>
                        </tr>
                        @endif
                        <tr>
                            <th>طريقة الدفع</th>
                            <td>
                                <span class="badge bg-info">{{ $purchase->payment_method_name }}</span>
                            </td>
                        </tr>
                        <tr>
                            <th>حالة الدفع</th>
                            <td>
                                @if($purchase->payment_status == 'paid')
                                    <span class="badge bg-success">{{ $purchase->payment_status_name }}</span>
                                @elseif($purchase->payment_status == 'partial')
                                    <span class="badge bg-warning">{{ $purchase->payment_status_name }}</span>
                                @elseif($purchase->payment_status == 'overdue')
                                    <span class="badge bg-danger">{{ $purchase->payment_status_name }}</span>
                                @else
                                    <span class="badge bg-secondary">{{ $purchase->payment_status_name }}</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>حالة التسليم</th>
                            <td>
                                @if($purchase->delivery_status == 'delivered')
                                    <span class="badge bg-success">{{ $purchase->delivery_status_name }}</span>
                                @elseif($purchase->delivery_status == 'partial')
                                    <span class="badge bg-warning">{{ $purchase->delivery_status_name }}</span>
                                @elseif($purchase->delivery_status == 'cancelled')
                                    <span class="badge bg-danger">{{ $purchase->delivery_status_name }}</span>
                                @else
                                    <span class="badge bg-secondary">{{ $purchase->delivery_status_name }}</span>
                                @endif
                            </td>
                        </tr>
                        @if($purchase->delivery_date)
                        <tr>
                            <th>تاريخ التسليم</th>
                            <td>{{ $purchase->delivery_date->format('Y-m-d') }}</td>
                        </tr>
                        @endif
                        <tr>
                            <th>المستخدم</th>
                            <td>
                                @if($purchase->user)
                                    {{ $purchase->user->name }}
                                @else
                                    غير محدد
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>تاريخ الإنشاء</th>
                            <td>{{ $purchase->created_at->format('Y-m-d H:i') }}</td>
                        </tr>
                    </table>

                    @if($purchase->notes)
                    <div class="alert alert-info mt-3">
                        <h6 class="alert-heading">ملاحظات:</h6>
                        <p class="mb-0">{{ $purchase->notes }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">ملخص المبالغ</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <table class="table table-bordered">
                                <tr>
                                    <th>المبلغ الإجمالي</th>
                                    <td class="text-end">{{ number_format($purchase->total_amount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                </tr>
                                <tr>
                                    <th>المبلغ المدفوع</th>
                                    <td class="text-end">{{ number_format($purchase->paid_amount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                </tr>
                                <tr class="{{ $purchase->remaining_amount > 0 ? 'table-danger' : 'table-success' }}">
                                    <th>المبلغ المتبقي</th>
                                    <td class="text-end fw-bold">{{ number_format($purchase->remaining_amount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">عناصر المشترى</h5>
                </div>
                <div class="card-body">
                    @if($purchase->items && $purchase->items->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>#</th>
                                        <th>اسم العنصر</th>
                                        <th>الوصف</th>
                                        <th>الوحدة</th>
                                        <th>الكمية</th>
                                        <th>سعر الوحدة</th>
                                        <th>المجموع</th>
                                        <th>الفئة</th>
                                        <th>تاريخ الانتهاء</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($purchase->items as $index => $item)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>{{ $item->item_name }}</td>
                                        <td>{{ $item->item_description ?? '-' }}</td>
                                        <td>{{ $item->unit }}</td>
                                        <td>{{ number_format($item->quantity, 2) }}</td>
                                        <td>{{ number_format($item->unit_price, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                        <td>{{ number_format($item->total_price, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                        <td>{{ $item->category ?? '-' }}</td>
                                        <td>
                                            @if($item->expiry_date)
                                                {{ $item->expiry_date->format('Y-m-d') }}
                                                @if($item->expiry_date->isPast())
                                                    <span class="badge bg-danger ms-1">منتهي الصلاحية</span>
                                                @elseif($item->expiry_date->diffInDays(now()) <= 30)
                                                    <span class="badge bg-warning ms-1">قريب الانتهاء</span>
                                                @endif
                                            @else
                                                -
                                            @endif
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th colspan="6" class="text-end">المجموع الكلي:</th>
                                        <th>{{ number_format($purchase->total_amount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</th>
                                        <th colspan="2"></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            لا توجد عناصر في هذا المشترى
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .card-header {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .container {
        max-width: 100% !important;
    }
}
</style>
@endsection
