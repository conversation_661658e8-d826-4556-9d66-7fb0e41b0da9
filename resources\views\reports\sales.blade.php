@extends('layouts.app')

@section('title', 'تقرير المبيعات')

@section('styles')
<style>
    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 20px;
    }
    .report-card {
        transition: all 0.3s;
    }
    .report-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
</style>
@endsection

@section('content')
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>تقرير المبيعات</h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('reports.orders') }}" class="btn btn-secondary">
                <i class="fas fa-chart-line me-1"></i> تقرير الطلبات
            </a>
        </div>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="{{ route('reports.sales') }}" method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ $startDate }}">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ $endDate }}">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter me-1"></i> تصفية
                    </button>
                    <a href="{{ route('reports.sales') }}" class="btn btn-secondary">
                        <i class="fas fa-redo me-1"></i> إعادة تعيين
                    </a>
                </div>
            </form>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white report-card">
                <div class="card-body">
                    <h5 class="card-title">إجمالي المبيعات</h5>
                    <p class="card-text display-6">{{ number_format($totalSales, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</p>
                    <p class="card-text">عدد الفواتير: {{ $invoices->count() }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white report-card">
                <div class="card-body">
                    <h5 class="card-title">إجمالي المدفوعات</h5>
                    <p class="card-text display-6">{{ number_format($totalPaid, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</p>
                    <p class="card-text">نسبة التحصيل: {{ $totalSales > 0 ? number_format(($totalPaid / $totalSales) * 100, 1) : 0 }}%</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-danger text-white report-card">
                <div class="card-body">
                    <h5 class="card-title">إجمالي المتبقي</h5>
                    <p class="card-text display-6">{{ number_format($totalRemaining, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</p>
                    <p class="card-text">نسبة المتبقي: {{ $totalSales > 0 ? number_format(($totalRemaining / $totalSales) * 100, 1) : 0 }}%</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">حالة الفواتير</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="invoiceStatusChart"></canvas>
                    </div>
                    <div class="row text-center mt-3">
                        <div class="col-md-4">
                            <div class="card bg-success text-white">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-0">مدفوعة</h6>
                                    <p class="card-text mb-0">{{ $paidInvoices }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-warning text-dark">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-0">مدفوعة جزئياً</h6>
                                    <p class="card-text mb-0">{{ $partialInvoices }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-danger text-white">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-0">غير مدفوعة</h6>
                                    <p class="card-text mb-0">{{ $unpaidInvoices }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">طرق الدفع</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="paymentMethodChart"></canvas>
                    </div>
                    <div class="row text-center mt-3">
                        <div class="col-md-4">
                            <div class="card bg-success text-white">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-0">نقداً</h6>
                                    <p class="card-text mb-0">{{ number_format($salesByPaymentMethod['cash'], 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-info text-white">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-0">بطاقة ائتمان</h6>
                                    <p class="card-text mb-0">{{ number_format($salesByPaymentMethod['credit_card'], 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body py-2">
                                    <h6 class="card-title mb-0">بطاقة خصم</h6>
                                    <p class="card-text mb-0">{{ number_format($salesByPaymentMethod['debit_card'], 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">المبيعات اليومية</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="dailySalesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">المنتجات الأكثر مبيعًا</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>المنتج</th>
                                    <th>الكمية المباعة</th>
                                    <th>إجمالي المبيعات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($topProducts as $index => $product)
                                <tr>
                                    <td>{{ $index + 1 }}</td>
                                    <td>
                                        @if($product->product)
                                            <a href="{{ route('products.show', $product->product->id) }}">{{ $product->product->name }}</a>
                                        @else
                                            منتج محذوف
                                        @endif
                                    </td>
                                    <td>{{ $product->total_quantity }}</td>
                                    <td>{{ number_format($product->total_sales, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="4" class="text-center">لا توجد بيانات</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-invoice-dollar me-2"></i>
                        قائمة الفواتير
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>التاريخ</th>
                                    <th>الطاولة</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>المبلغ المدفوع</th>
                                    <th>المبلغ المتبقي</th>
                                    <th>حالة الدفع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($invoices as $invoice)
                                <tr>
                                    <td>{{ $invoice->invoice_number }}</td>
                                    <td>{{ $invoice->created_at->format('Y-m-d') }}</td>
                                    <td>
                                        @if($invoice->order && $invoice->order->table)
                                            {{ $invoice->order->table->name }}
                                        @else
                                            -
                                        @endif
                                    </td>
                                    <td>{{ number_format($invoice->total_amount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                    <td>{{ number_format($invoice->paid_amount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                    <td>{{ number_format($invoice->remaining_amount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                    <td>
                                        @if($invoice->payment_status == 'paid')
                                            <span class="badge bg-success">مدفوعة</span>
                                        @elseif($invoice->payment_status == 'partial')
                                            <span class="badge bg-warning">مدفوعة جزئياً</span>
                                        @elseif($invoice->payment_status == 'unpaid')
                                            <span class="badge bg-danger">غير مدفوعة</span>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="{{ route('invoices.show', $invoice->id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('invoices.print', $invoice->id) }}" class="btn btn-sm btn-primary" target="_blank">
                                            <i class="fas fa-print"></i>
                                        </a>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="8" class="text-center">لا توجد بيانات</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- معلومات النتائج -->
                    <div class="d-flex justify-content-between align-items-center mt-3 mb-3">
                        <div class="text-muted">
                            <small>
                                <i class="fas fa-info-circle me-1"></i>
                                عرض {{ $invoices->firstItem() ?? 0 }} - {{ $invoices->lastItem() ?? 0 }} من {{ $invoices->total() }} فاتورة
                            </small>
                        </div>
                        <div class="text-muted">
                            <small>
                                <i class="fas fa-file-invoice me-1"></i>
                                {{ $invoices->perPage() }} فاتورة في الصفحة
                            </small>
                        </div>
                    </div>

                    <!-- Pagination -->
                    @if($invoices->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $invoices->appends(request()->query())->links('custom-pagination') }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // حالة الفواتير
        const invoiceStatusCtx = document.getElementById('invoiceStatusChart').getContext('2d');
        const invoiceStatusChart = new Chart(invoiceStatusCtx, {
            type: 'pie',
            data: {
                labels: ['مدفوعة', 'مدفوعة جزئياً', 'غير مدفوعة'],
                datasets: [{
                    data: [{{ $paidInvoices }}, {{ $partialInvoices }}, {{ $unpaidInvoices }}],
                    backgroundColor: ['#198754', '#ffc107', '#dc3545'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // طرق الدفع
        const paymentMethodCtx = document.getElementById('paymentMethodChart').getContext('2d');
        const paymentMethodChart = new Chart(paymentMethodCtx, {
            type: 'pie',
            data: {
                labels: ['نقداً', 'بطاقة ائتمان', 'بطاقة خصم'],
                datasets: [{
                    data: [
                        {{ $salesByPaymentMethod['cash'] }},
                        {{ $salesByPaymentMethod['credit_card'] }},
                        {{ $salesByPaymentMethod['debit_card'] }}
                    ],
                    backgroundColor: ['#198754', '#0dcaf0', '#0d6efd'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // المبيعات اليومية
        const dailySalesCtx = document.getElementById('dailySalesChart').getContext('2d');
        const dailySalesChart = new Chart(dailySalesCtx, {
            type: 'bar',
            data: {
                labels: [
                    @foreach($dailySales as $date => $data)
                        '{{ $date }}',
                    @endforeach
                ],
                datasets: [{
                    label: 'إجمالي المبيعات',
                    data: [
                        @foreach($dailySales as $data)
                            {{ $data['total'] }},
                        @endforeach
                    ],
                    backgroundColor: '#0d6efd',
                    borderWidth: 1
                }, {
                    label: 'المبلغ المدفوع',
                    data: [
                        @foreach($dailySales as $data)
                            {{ $data['paid'] }},
                        @endforeach
                    ],
                    backgroundColor: '#198754',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    });
</script>
@endsection
