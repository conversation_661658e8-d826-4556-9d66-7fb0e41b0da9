<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلب المطبخ - {{ $order->id }}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
            color: #333;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .kitchen-order {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border: 2px solid #333;
            padding: 15px;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        
        .header h2 {
            margin: 5px 0 0 0;
            font-size: 18px;
            font-weight: normal;
        }
        
        .order-info {
            margin-bottom: 15px;
            border-bottom: 1px solid #333;
            padding-bottom: 10px;
        }
        
        .order-info div {
            margin-bottom: 5px;
            display: flex;
            justify-content: space-between;
        }
        
        .order-info strong {
            font-weight: bold;
        }
        
        .items-section {
            margin-bottom: 15px;
        }
        
        .items-header {
            background: #333;
            color: white;
            padding: 8px;
            text-align: center;
            font-weight: bold;
            font-size: 16px;
        }
        
        .item {
            border-bottom: 1px solid #ddd;
            padding: 10px 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .item:last-child {
            border-bottom: none;
        }
        
        .item-name {
            font-weight: bold;
            font-size: 16px;
            flex: 1;
        }
        
        .item-quantity {
            background: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 50%;
            font-weight: bold;
            min-width: 30px;
            text-align: center;
            margin-left: 10px;
        }
        
        .item-notes {
            font-style: italic;
            color: #666;
            margin-top: 5px;
            font-size: 12px;
        }
        
        .footer {
            text-align: center;
            border-top: 2px solid #333;
            padding-top: 10px;
            margin-top: 15px;
        }
        
        .time {
            font-size: 12px;
            color: #666;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 10px;
            }
            
            .kitchen-order {
                max-width: none;
                border: none;
                padding: 0;
            }
            
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="kitchen-order">
        <div class="header">
            <h1>طلب المطبخ</h1>
            <h2>رقم الطلب: {{ $order->id }}</h2>
        </div>
        
        <div class="order-info">
            <div>
                <strong>الطاولة:</strong>
                <span>{{ $order->table ? $order->table->name : 'طلب خارجي' }}</span>
            </div>
            <div>
                <strong>النادل:</strong>
                <span>{{ $order->user ? $order->user->name : '-' }}</span>
            </div>
            @if($order->customer_name)
            <div>
                <strong>العميل:</strong>
                <span>{{ $order->customer_name }}</span>
            </div>
            @endif
            @if($order->car_number)
            <div>
                <strong>رقم السيارة:</strong>
                <span>{{ $order->car_number }}</span>
            </div>
            @endif
            <div>
                <strong>الوقت:</strong>
                <span>{{ $order->created_at->format('H:i') }}</span>
            </div>
        </div>
        
        <div class="items-section">
            <div class="items-header">
                الأصناف المطلوبة
            </div>
            
            @foreach($order->orderItems as $item)
            <div class="item">
                <div style="flex: 1;">
                    <div class="item-name">{{ $item->product ? $item->product->name : 'منتج محذوف' }}</div>
                    @if($item->notes)
                    <div class="item-notes">ملاحظات: {{ $item->notes }}</div>
                    @endif
                </div>
                <div class="item-quantity">{{ $item->quantity }}</div>
            </div>
            @endforeach
        </div>
        
        @if($order->notes)
        <div class="order-info">
            <div>
                <strong>ملاحظات الطلب:</strong>
            </div>
            <div style="margin-top: 5px; font-style: italic;">
                {{ $order->notes }}
            </div>
        </div>
        @endif
        
        <div class="footer">
            <div class="time">
                طُبع في: {{ now()->format('Y-m-d H:i:s') }}
            </div>
        </div>
        
        <div class="text-center mt-4 no-print">
            <button onclick="window.print()" style="background: #333; color: white; border: none; padding: 10px 20px; margin: 5px; cursor: pointer;">طباعة</button>
            <button onclick="window.close()" style="background: #666; color: white; border: none; padding: 10px 20px; margin: 5px; cursor: pointer;">إغلاق</button>
        </div>
    </div>
</body>
</html>
