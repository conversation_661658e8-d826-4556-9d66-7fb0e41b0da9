<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use App\Models\Backup;
use App\Models\User;

class TestController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * اختبار الصلاحيات
     */
    public function testPermissions()
    {
        try {
            $user = auth()->user();
            
            return response()->json([
                'success' => true,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role_id' => $user->role_id,
                    'role' => $user->role,
                ],
                'permissions' => [
                    'hasRole_admin' => $user->hasRole('admin'),
                    'hasRole_manager' => $user->hasRole('manager'),
                    'hasRole_cashier' => $user->hasRole('cashier'),
                ],
                'role_details' => $user->getUserRole(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * اختبار صلاحيات النسخ الاحتياطي
     */
    public function testBackupPermissions()
    {
        try {
            $user = auth()->user();
            
            if (!$user->hasRole('admin') && !$user->hasRole('manager')) {
                return response()->json([
                    'success' => false,
                    'error' => 'ليس لديك صلاحية للنسخ الاحتياطي',
                    'user_role' => $user->role,
                    'required_roles' => ['admin', 'manager']
                ], 403);
            }

            return response()->json([
                'success' => true,
                'message' => 'لديك صلاحية للنسخ الاحتياطي',
                'user' => $user->name,
                'role' => $user->role
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * إنشاء نسخة احتياطية تجريبية
     */
    public function createTestBackup(Request $request)
    {
        try {
            $user = auth()->user();
            
            // التحقق من الصلاحيات
            if (!$user->hasRole('admin') && !$user->hasRole('manager')) {
                return response()->json([
                    'success' => false,
                    'error' => 'ليس لديك صلاحية للنسخ الاحتياطي'
                ], 403);
            }

            // التحقق من البيانات
            $backupType = $request->input('backup_type', 'database');
            if (!in_array($backupType, ['database', 'files', 'full'])) {
                return response()->json([
                    'success' => false,
                    'error' => 'نوع النسخة الاحتياطية غير صحيح'
                ], 400);
            }

            // إنشاء مجلد النسخ الاحتياطية
            $backupDir = storage_path('app/backups');
            if (!File::exists($backupDir)) {
                File::makeDirectory($backupDir, 0755, true);
            }

            // إنشاء ملف نسخة احتياطية تجريبية
            $timestamp = now()->format('Y-m-d_H-i-s');
            $filename = "test_backup_{$backupType}_{$timestamp}.sql";
            $filepath = storage_path("app/backups/{$filename}");

            $content = "-- نسخة احتياطية تجريبية\n";
            $content .= "-- النوع: {$backupType}\n";
            $content .= "-- التاريخ: " . now()->format('Y-m-d H:i:s') . "\n";
            $content .= "-- المستخدم: {$user->name}\n";
            $content .= "-- عدد المستخدمين: " . User::count() . "\n";
            $content .= "\n-- تم إنشاء هذه النسخة للاختبار فقط\n";

            File::put($filepath, $content);

            // إنشاء سجل في قاعدة البيانات
            $backup = Backup::create([
                'filename' => $filename,
                'type' => $backupType,
                'status' => 'completed',
                'description' => 'نسخة احتياطية تجريبية',
                'created_by' => $user->id,
                'is_scheduled' => false,
                'started_at' => now(),
                'completed_at' => now(),
                'file_size' => File::size($filepath),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء النسخة الاحتياطية التجريبية بنجاح',
                'backup' => [
                    'id' => $backup->id,
                    'filename' => $backup->filename,
                    'type' => $backup->type,
                    'size' => $backup->formatted_size,
                    'created_at' => $backup->created_at->format('Y-m-d H:i:s')
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('خطأ في النسخة الاحتياطية التجريبية: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => 'فشل في إنشاء النسخة الاحتياطية: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * اختبار middleware
     */
    public function testMiddleware()
    {
        try {
            $user = auth()->user();
            
            return response()->json([
                'success' => true,
                'message' => 'تم تجاوز middleware بنجاح',
                'user' => $user->name,
                'middlewares_passed' => ['auth']
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
