@extends('layouts.app')

@section('title', 'إدارة الفواتير')

@section('content')
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>إدارة الفواتير</h2>
            <p class="text-muted">إجمالي الفواتير: {{ $invoices->total() }}</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('invoices.create') }}" class="btn btn-primary">
                <i class="fas fa-plus-circle me-1"></i> إنشاء فاتورة جديدة
            </a>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="row">
                <div class="col-md-3">
                    <div class="card border-success text-center">
                        <div class="card-body">
                            <h6 class="card-title">مدفوعة</h6>
                            <h4 class="text-success">{{ $statistics['paid_count'] }}</h4>
                            <small class="text-muted">
                                {{ number_format($statistics['paid_amount'], 2) }}
                                {{ \App\Models\Setting::get('currency', 'ر.س') }}
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-warning text-center">
                        <div class="card-body">
                            <h6 class="card-title">مدفوعة جزئياً</h6>
                            <h4 class="text-warning">{{ $statistics['partial_count'] }}</h4>
                            <small class="text-muted">
                                {{ number_format($statistics['partial_remaining'], 2) }}
                                {{ \App\Models\Setting::get('currency', 'ر.س') }} متبقي
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-danger text-center">
                        <div class="card-body">
                            <h6 class="card-title">غير مدفوعة</h6>
                            <h4 class="text-danger">{{ $statistics['unpaid_count'] }}</h4>
                            <small class="text-muted">
                                {{ number_format($statistics['unpaid_amount'], 2) }}
                                {{ \App\Models\Setting::get('currency', 'ر.س') }}
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-info text-center">
                        <div class="card-body">
                            <h6 class="card-title">إجمالي المبيعات</h6>
                            <h4 class="text-info">
                                {{ number_format($statistics['total_amount'], 2) }}
                                {{ \App\Models\Setting::get('currency', 'ر.س') }}
                            </h4>
                            <small class="text-muted">جميع الفواتير</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث والتصفية -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('invoices.index') }}" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="{{ request('search') }}" placeholder="البحث في الفواتير...">
                </div>
                <div class="col-md-2">
                    <label for="payment_status" class="form-label">حالة الدفع</label>
                    <select class="form-select" id="payment_status" name="payment_status">
                        <option value="">جميع الحالات</option>
                        @foreach($paymentStatuses as $key => $name)
                            <option value="{{ $key }}" {{ request('payment_status') == $key ? 'selected' : '' }}>
                                {{ $name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="payment_method" class="form-label">طريقة الدفع</label>
                    <select class="form-select" id="payment_method" name="payment_method">
                        <option value="">جميع الطرق</option>
                        @foreach($paymentMethods as $key => $name)
                            <option value="{{ $key }}" {{ request('payment_method') == $key ? 'selected' : '' }}>
                                {{ $name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="user_id" class="form-label">الكاشير</label>
                    <select class="form-select" id="user_id" name="user_id">
                        <option value="">جميع الكاشيرين</option>
                        @foreach($users as $user)
                            <option value="{{ $user->id }}" {{ request('user_id') == $user->id ? 'selected' : '' }}>
                                {{ $user->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-1">
                    <label for="per_page" class="form-label">عدد النتائج</label>
                    <select class="form-select" id="per_page" name="per_page">
                        <option value="15" {{ request('per_page') == 15 ? 'selected' : '' }}>15</option>
                        <option value="25" {{ request('per_page', 25) == 25 ? 'selected' : '' }}>25</option>
                        <option value="30" {{ request('per_page') == 30 ? 'selected' : '' }}>30</option>
                        <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50</option>
                        <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="sort_by" class="form-label">ترتيب حسب</label>
                    <select class="form-select" id="sort_by" name="sort_by">
                        <option value="created_at" {{ request('sort_by', 'created_at') == 'created_at' ? 'selected' : '' }}>التاريخ</option>
                        <option value="invoice_number" {{ request('sort_by') == 'invoice_number' ? 'selected' : '' }}>رقم الفاتورة</option>
                        <option value="total_amount" {{ request('sort_by') == 'total_amount' ? 'selected' : '' }}>المبلغ</option>
                        <option value="payment_status" {{ request('sort_by') == 'payment_status' ? 'selected' : '' }}>حالة الدفع</option>
                    </select>
                </div>

                <!-- صف ثاني للفلاتر الإضافية -->
                <div class="col-md-2">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from"
                           value="{{ request('date_from') }}">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to"
                           value="{{ request('date_to') }}">
                </div>
                <div class="col-md-2">
                    <label for="amount_from" class="form-label">من مبلغ</label>
                    <input type="number" class="form-control" id="amount_from" name="amount_from"
                           value="{{ request('amount_from') }}" step="0.01" min="0">
                </div>
                <div class="col-md-2">
                    <label for="amount_to" class="form-label">إلى مبلغ</label>
                    <input type="number" class="form-control" id="amount_to" name="amount_to"
                           value="{{ request('amount_to') }}" step="0.01" min="0">
                </div>
                <div class="col-md-1">
                    <label for="sort_order" class="form-label">الاتجاه</label>
                    <select class="form-select" id="sort_order" name="sort_order">
                        <option value="desc" {{ request('sort_order', 'desc') == 'desc' ? 'selected' : '' }}>تنازلي</option>
                        <option value="asc" {{ request('sort_order') == 'asc' ? 'selected' : '' }}>تصاعدي</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label>&nbsp;</label>
                    <div class="d-flex">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search me-1"></i> بحث
                        </button>
                        <a href="{{ route('invoices.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i> مسح الفلاتر
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- عرض النتائج -->
    <div class="card shadow-sm">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">قائمة الفواتير</h5>
            <div class="text-muted">
                عرض {{ $invoices->firstItem() ?? 0 }} - {{ $invoices->lastItem() ?? 0 }} من {{ $invoices->total() }} فاتورة
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>رقم الطلب</th>
                            <th>الطاولة/النوع</th>
                            <th>المبلغ الإجمالي</th>
                            <th>المبلغ المدفوع</th>
                            <th>المبلغ المتبقي</th>
                            <th>طريقة الدفع</th>
                            <th>حالة الدفع</th>
                            <th>التاريخ والوقت</th>
                            <th>الكاشير</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($invoices as $invoice)
                        <tr>
                            <td>
                                <strong>{{ $invoice->invoice_number }}</strong>
                            </td>
                            <td>
                                @if($invoice->order)
                                    <a href="{{ route('orders.show', $invoice->order->id) }}" class="text-decoration-none">
                                        <strong>#{{ $invoice->order->id }}</strong>
                                    </a>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                @if($invoice->order && $invoice->order->table)
                                    <i class="fas fa-utensils text-primary me-1"></i>
                                    {{ $invoice->order->table->name }}
                                @elseif($invoice->order && $invoice->order->order_type == 'takeaway')
                                    <i class="fas fa-shopping-bag text-warning me-1"></i>
                                    <span class="badge bg-warning">طلب خارجي</span>
                                    @if($invoice->order->car_number)
                                        <br><small class="text-muted">رقم السيارة: {{ $invoice->order->car_number }}</small>
                                    @endif
                                @elseif($invoice->order && $invoice->order->order_type == 'delivery')
                                    <i class="fas fa-motorcycle text-info me-1"></i>
                                    <span class="badge bg-info">توصيل</span>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                <strong>{{ number_format($invoice->total_amount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</strong>
                            </td>
                            <td>
                                <span class="text-success">{{ number_format($invoice->paid_amount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</span>
                            </td>
                            <td>
                                @if($invoice->remaining_amount > 0)
                                    <span class="text-danger">{{ number_format($invoice->remaining_amount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</span>
                                @else
                                    <span class="text-muted">0.00 {{ \App\Models\Setting::get('currency', 'ر.س') }}</span>
                                @endif
                            </td>
                            <td>
                                @if($invoice->payment_method == 'cash')
                                    <span class="badge bg-success"><i class="fas fa-money-bill me-1"></i>نقداً</span>
                                @elseif($invoice->payment_method == 'credit_card')
                                    <span class="badge bg-info"><i class="fas fa-credit-card me-1"></i>بطاقة ائتمان</span>
                                @elseif($invoice->payment_method == 'debit_card')
                                    <span class="badge bg-primary"><i class="fas fa-credit-card me-1"></i>بطاقة خصم</span>
                                @endif
                            </td>
                            <td>
                                @if($invoice->payment_status == 'paid')
                                    <span class="badge bg-success">مدفوعة</span>
                                @elseif($invoice->payment_status == 'partial')
                                    <span class="badge bg-warning">مدفوعة جزئياً</span>
                                @elseif($invoice->payment_status == 'unpaid')
                                    <span class="badge bg-danger">غير مدفوعة</span>
                                @endif
                            </td>
                            <td>
                                <div>{{ $invoice->created_at->format('Y-m-d') }}</div>
                                <small class="text-muted">{{ $invoice->created_at->format('H:i') }}</small>
                            </td>
                            <td>
                                <small>{{ $invoice->user->name ?? 'غير محدد' }}</small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('invoices.show', $invoice->id) }}" class="btn btn-sm btn-info" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('manager'))
                                    <a href="{{ route('invoices.edit', $invoice->id) }}" class="btn btn-sm btn-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @endif
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-secondary dropdown-toggle"
                                                data-bs-toggle="dropdown" aria-expanded="false" title="طباعة">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="{{ route('invoices.print', $invoice->id) }}" target="_blank">
                                                <i class="fas fa-file-pdf me-1"></i>A4
                                            </a></li>
                                            <li><a class="dropdown-item" href="{{ route('invoices.print.85mm', $invoice->id) }}" target="_blank">
                                                <i class="fas fa-receipt me-1"></i>85مم
                                            </a></li>
                                            <li><a class="dropdown-item" href="{{ route('invoices.print.56mm', $invoice->id) }}" target="_blank">
                                                <i class="fas fa-receipt me-1"></i>56مم
                                            </a></li>
                                        </ul>
                                    </div>
                                    @if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('manager'))
                                    <button type="button" class="btn btn-sm btn-danger" title="حذف"
                                            data-bs-toggle="modal" data-bs-target="#deleteModal{{ $invoice->id }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    @endif
                                </div>

                                <!-- Modal for Delete Confirmation -->
                                @if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('manager'))
                                <div class="modal fade" id="deleteModal{{ $invoice->id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ $invoice->id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ $invoice->id }}">تأكيد الحذف</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                هل أنت متأكد من رغبتك في حذف الفاتورة رقم <strong>{{ $invoice->invoice_number }}</strong>؟
                                                <br><small class="text-danger">تحذير: لا يمكن التراجع عن هذا الإجراء!</small>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                <form action="{{ route('invoices.destroy', $invoice->id) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-danger">حذف</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endif
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="11" class="text-center py-4">
                                <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                                <br>
                                <span class="text-muted">لا توجد فواتير تطابق معايير البحث</span>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        @if($invoices->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">
                    عرض {{ $invoices->firstItem() }} - {{ $invoices->lastItem() }} من {{ $invoices->total() }} فاتورة
                </div>
                <div>
                    {{ $invoices->appends(request()->query())->links('custom-pagination') }}
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

<script>
// تحديث الصفحة عند تغيير عدد النتائج
document.getElementById('per_page').addEventListener('change', function() {
    this.form.submit();
});

// تحديث الصفحة عند تغيير الترتيب
document.getElementById('sort_by').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('sort_order').addEventListener('change', function() {
    this.form.submit();
});

// تحديد التاريخ الحالي كافتراضي للبحث
document.addEventListener('DOMContentLoaded', function() {
    // يمكن إضافة المزيد من JavaScript هنا حسب الحاجة
});
</script>
@endsection
