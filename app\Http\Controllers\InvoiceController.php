<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\Order;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class InvoiceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // إذا كان الطلب يحتوي على latest=true، إرجاع آخر فاتورة كـ JSON
        if ($request->has('latest') && $request->get('latest') === 'true') {
            $latestInvoice = Invoice::with(['order', 'user'])->latest()->first();

            if ($latestInvoice) {
                return response()->json([
                    'success' => true,
                    'invoice' => $latestInvoice
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'لم يتم العثور على فواتير'
                ]);
            }
        }

        // إذا كان الطلب يحتوي على search للـ API، البحث عن الفواتير
        if ($request->has('search') && $request->ajax()) {
            $searchTerm = $request->get('search');
            $invoices = Invoice::with(['order.user', 'user'])
                ->where('invoice_number', 'like', "%{$searchTerm}%")
                ->orWhereHas('order', function($query) use ($searchTerm) {
                    $query->where('customer_name', 'like', "%{$searchTerm}%")
                          ->orWhere('customer_phone', 'like', "%{$searchTerm}%");
                })
                ->latest()
                ->get();

            return response()->json([
                'success' => true,
                'invoices' => $invoices
            ]);
        }

        // للعرض العادي مع pagination وفلاتر
        $query = Invoice::with(['order', 'user']);

        // البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhere('notes', 'like', "%{$search}%")
                  ->orWhereHas('order', function($orderQuery) use ($search) {
                      $orderQuery->where('customer_name', 'like', "%{$search}%")
                                 ->orWhere('customer_phone', 'like', "%{$search}%");
                  })
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // فلترة حسب حالة الدفع
        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        // فلترة حسب طريقة الدفع
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        // فلترة حسب الكاشير
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // فلترة حسب التاريخ
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // فلترة حسب المبلغ
        if ($request->filled('amount_from')) {
            $query->where('total_amount', '>=', $request->amount_from);
        }

        if ($request->filled('amount_to')) {
            $query->where('total_amount', '<=', $request->amount_to);
        }

        // ترتيب النتائج
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        if (in_array($sortBy, ['id', 'created_at', 'total_amount', 'payment_status', 'invoice_number'])) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->latest();
        }

        // عدد العناصر في الصفحة
        $perPage = $request->get('per_page', 25);
        if (!in_array($perPage, [15, 25, 30, 50, 100])) {
            $perPage = 25;
        }

        $invoices = $query->paginate($perPage);

        // حساب الإحصائيات من جميع الفواتير (ليس فقط الصفحة الحالية)
        $allInvoicesQuery = Invoice::query();

        // تطبيق نفس الفلاتر على الإحصائيات
        if ($request->filled('search')) {
            $search = $request->search;
            $allInvoicesQuery->where(function($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhere('notes', 'like', "%{$search}%")
                  ->orWhereHas('order', function($orderQuery) use ($search) {
                      $orderQuery->where('customer_name', 'like', "%{$search}%")
                                 ->orWhere('customer_phone', 'like', "%{$search}%");
                  })
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('payment_status')) {
            $allInvoicesQuery->where('payment_status', $request->payment_status);
        }

        if ($request->filled('payment_method')) {
            $allInvoicesQuery->where('payment_method', $request->payment_method);
        }

        if ($request->filled('user_id')) {
            $allInvoicesQuery->where('user_id', $request->user_id);
        }

        if ($request->filled('date_from')) {
            $allInvoicesQuery->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $allInvoicesQuery->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('amount_from')) {
            $allInvoicesQuery->where('total_amount', '>=', $request->amount_from);
        }

        if ($request->filled('amount_to')) {
            $allInvoicesQuery->where('total_amount', '<=', $request->amount_to);
        }

        // حساب الإحصائيات
        $statistics = [
            'paid_count' => $allInvoicesQuery->clone()->where('payment_status', 'paid')->count(),
            'paid_amount' => $allInvoicesQuery->clone()->where('payment_status', 'paid')->sum('total_amount'),
            'unpaid_count' => $allInvoicesQuery->clone()->where('payment_status', 'unpaid')->count(),
            'unpaid_amount' => $allInvoicesQuery->clone()->where('payment_status', 'unpaid')->sum('total_amount'),
            'partial_count' => $allInvoicesQuery->clone()->where('payment_status', 'partial')->count(),
            'partial_remaining' => $allInvoicesQuery->clone()->where('payment_status', 'partial')->sum('remaining_amount'),
            'total_amount' => $allInvoicesQuery->clone()->sum('total_amount'),
        ];

        // بيانات إضافية للفلاتر
        $users = User::whereHas('role', function ($query) {
            $query->whereIn('name', ['cashier', 'admin']);
        })->get();

        $paymentStatuses = [
            'paid' => 'مدفوع',
            'unpaid' => 'غير مدفوع',
            'partial' => 'دفع جزئي'
        ];

        $paymentMethods = [
            'cash' => 'نقدي',
            'credit_card' => 'بطاقة ائتمان',
            'debit_card' => 'بطاقة خصم'
        ];

        return view('invoices.index', compact('invoices', 'users', 'paymentStatuses', 'paymentMethods', 'statistics'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $orders = Order::whereIn('status', ['completed', 'delivered'])
            ->whereDoesntHave('invoice')
            ->with(['user', 'table'])
            ->get();

        $cashiers = User::whereHas('role', function ($query) {
            $query->where('name', 'cashier');
        })->get();

        return view('invoices.create', compact('orders', 'cashiers'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'order_id' => 'required|exists:orders,id',
            'user_id' => 'required|exists:users,id',
            'payment_method' => 'required|in:cash,credit_card,debit_card',
            'payment_status' => 'required|in:paid,unpaid,partial',
            'paid_amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string',
        ]);

        // التحقق من عدم وجود فاتورة للطلب بالفعل
        $existingInvoice = Invoice::where('order_id', $validated['order_id'])->first();
        if ($existingInvoice) {
            return redirect()->route('invoices.index')
                ->with('error', 'يوجد فاتورة بالفعل لهذا الطلب');
        }

        // الحصول على الطلب
        $order = Order::findOrFail($validated['order_id']);

        DB::beginTransaction();

        try {
            // حساب المبالغ (الطلب يحتوي بالفعل على الضريبة)
            $subtotalAmount = $order->orderItems->sum(function($item) {
                return $item->quantity * $item->unit_price;
            });
            $taxRate = \App\Models\Setting::get('tax_rate', 15) / 100; // قراءة معدل الضريبة من الإعدادات
            $taxAmount = $subtotalAmount * $taxRate;
            $finalAmount = $subtotalAmount + $taxAmount;

            // إنشاء الفاتورة
            $invoice = Invoice::create([
                'order_id' => $validated['order_id'],
                'user_id' => $validated['user_id'],
                'invoice_number' => 'INV-' . date('Ymd') . '-' . rand(1000, 9999),
                'total_amount' => $subtotalAmount,
                'tax_amount' => $taxAmount,
                'discount_amount' => 0,
                'final_amount' => $finalAmount,
                'paid_amount' => $validated['paid_amount'],
                'remaining_amount' => $finalAmount - $validated['paid_amount'],
                'payment_method' => $validated['payment_method'],
                'payment_status' => $validated['payment_status'],
                'notes' => $validated['notes'] ?? null,
            ]);

            // تحديث حالة الطلب إلى مكتمل إذا لم يكن كذلك بالفعل
            if ($order->status !== 'completed') {
                $order->status = 'completed';
                $order->save();

                // تحرير الطاولة
                if ($order->table) {
                    $order->table->status = 'available';
                    $order->table->save();
                }
            }

            DB::commit();

            return redirect()->route('invoices.show', $invoice->id)
                ->with('success', 'تم إنشاء الفاتورة بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء إنشاء الفاتورة: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $invoice = Invoice::with(['order.orderItems.product', 'order.table', 'order.user', 'user'])->findOrFail($id);
        return view('invoices.show', compact('invoice'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $invoice = Invoice::with(['order'])->findOrFail($id);
        $cashiers = User::whereHas('role', function ($query) {
            $query->where('name', 'cashier');
        })->get();

        return view('invoices.edit', compact('invoice', 'cashiers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $invoice = Invoice::findOrFail($id);

        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'payment_method' => 'required|in:cash,credit_card,debit_card',
            'payment_status' => 'required|in:paid,unpaid,partial',
            'paid_amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string',
        ]);

        DB::beginTransaction();

        try {
            // تحديث الفاتورة
            $invoice->user_id = $validated['user_id'];
            $invoice->payment_method = $validated['payment_method'];
            $invoice->payment_status = $validated['payment_status'];
            $invoice->paid_amount = $validated['paid_amount'];
            $invoice->remaining_amount = $invoice->total_amount - $validated['paid_amount'];
            $invoice->notes = $validated['notes'] ?? null;
            $invoice->save();

            DB::commit();

            return redirect()->route('invoices.show', $invoice->id)
                ->with('success', 'تم تحديث الفاتورة بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء تحديث الفاتورة: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $invoice = Invoice::findOrFail($id);

        DB::beginTransaction();

        try {
            // حذف الفاتورة
            $invoice->delete();

            DB::commit();

            return redirect()->route('invoices.index')
                ->with('success', 'تم حذف الفاتورة بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء حذف الفاتورة: ' . $e->getMessage());
        }
    }

    /**
     * طباعة الفاتورة
     */
    public function print(string $id)
    {
        $invoice = Invoice::with(['order.orderItems.product', 'order.table', 'order.user', 'user'])->findOrFail($id);
        return view('invoices.print', compact('invoice'));
    }

    /**
     * طباعة الفاتورة بمقاس 85مم
     */
    public function print85mm(string $id)
    {
        $invoice = Invoice::with(['order.orderItems.product', 'order.table', 'order.user', 'user'])->findOrFail($id);
        return view('invoices.print_85mm', compact('invoice'));
    }

    /**
     * طباعة الفاتورة بمقاس 56مم
     */
    public function print56mm(string $id)
    {
        $invoice = Invoice::with(['order.orderItems.product', 'order.table', 'order.user', 'user'])->findOrFail($id);
        return view('invoices.print_56mm', compact('invoice'));
    }
}
