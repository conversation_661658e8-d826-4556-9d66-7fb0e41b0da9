<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * إنشاء مثيل جديد من المتحكم
     */
    public function __construct()
    {
        // التحقق من المصادقة فقط
        $this->middleware('auth');
    }

    /**
     * الحصول على إشعارات المستخدم الحالي
     */
    public function getNotifications()
    {
        try {
            // تسجيل معلومات التشخيص
            \Log::info('طلب الحصول على الإشعارات', [
                'user_id' => Auth::id(),
                'user_name' => Auth::user()->name ?? 'غير محدد',
                'is_authenticated' => Auth::check()
            ]);

            if (!Auth::check()) {
                \Log::warning('محاولة الوصول للإشعارات بدون تسجيل دخول');
                return response()->json([
                    'success' => false,
                    'message' => 'يجب تسجيل الدخول أولاً'
                ], 401);
            }

            $notifications = Notification::where('user_id', Auth::id())
                ->orderBy('created_at', 'desc')
                ->limit(15)
                ->get();

            \Log::info('تم جلب آخر 15 إشعار بنجاح', [
                'count' => $notifications->count(),
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => true,
                'notifications' => $notifications,
                'count' => $notifications->count(),
                'user_id' => Auth::id()
            ]);
        } catch (\Exception $e) {
            \Log::error('خطأ في جلب الإشعارات', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الإشعارات: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * تعيين إشعار كمقروء
     */
    public function markAsRead($id)
    {
        $notification = Notification::where('id', $id)
            ->where('user_id', Auth::id())
            ->first();

        if (!$notification) {
            return response()->json([
                'success' => false,
                'message' => 'الإشعار غير موجود'
            ], 404);
        }

        $notification->read = true;
        $notification->save();

        return response()->json([
            'success' => true,
            'message' => 'تم تعيين الإشعار كمقروء'
        ]);
    }

    /**
     * تعيين جميع الإشعارات كمقروءة
     */
    public function markAllAsRead()
    {
        Notification::where('user_id', Auth::id())
            ->where('read', false)
            ->update(['read' => true]);

        return response()->json([
            'success' => true,
            'message' => 'تم تعيين جميع الإشعارات كمقروءة'
        ]);
    }
}
