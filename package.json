{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "watch": "vite build --watch"}, "devDependencies": {"@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "@inertiajs/vue3": "^1.0.14", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tailwindcss/vite": "^4.0.0", "@vitejs/plugin-vue": "^5.0.4", "autoprefixer": "^10.4.16", "axios": "^1.8.2", "chart.js": "^4.4.1", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.32", "tailwindcss": "^4.0.0", "vite": "^6.2.4", "vue": "^3.4.15", "vue-chartjs": "^5.3.0"}, "dependencies": {"@inertiajs/inertia": "^0.11.1"}}