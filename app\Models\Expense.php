<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Expense extends Model
{
    protected $fillable = [
        'title',
        'description',
        'amount',
        'category',
        'expense_date',
        'payment_method',
        'receipt_number',
        'vendor',
        'status',
        'user_id',
        'notes'
    ];

    protected $casts = [
        'expense_date' => 'date',
        'amount' => 'decimal:2'
    ];

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * فئات المصروفات المتاحة
     */
    public static function getCategories(): array
    {
        return [
            'raw_materials' => 'مواد خام',
            'salaries' => 'رواتب',
            'rent' => 'إيجار',
            'utilities' => 'فواتير (كهرباء، ماء، غاز)',
            'maintenance' => 'صيانة',
            'marketing' => 'تسويق',
            'equipment' => 'معدات',
            'transportation' => 'مواصلات',
            'other' => 'أخرى'
        ];
    }

    /**
     * طرق الدفع المتاحة
     */
    public static function getPaymentMethods(): array
    {
        return [
            'cash' => 'نقدي',
            'card' => 'بطاقة',
            'bank_transfer' => 'تحويل بنكي',
            'check' => 'شيك'
        ];
    }

    /**
     * حالات المصروف
     */
    public static function getStatuses(): array
    {
        return [
            'paid' => 'مدفوع',
            'pending' => 'معلق',
            'cancelled' => 'ملغي'
        ];
    }

    /**
     * الحصول على اسم الفئة
     */
    public function getCategoryNameAttribute(): string
    {
        $categories = self::getCategories();
        return $categories[$this->category] ?? $this->category;
    }

    /**
     * الحصول على اسم طريقة الدفع
     */
    public function getPaymentMethodNameAttribute(): string
    {
        $methods = self::getPaymentMethods();
        return $methods[$this->payment_method] ?? $this->payment_method;
    }

    /**
     * الحصول على اسم الحالة
     */
    public function getStatusNameAttribute(): string
    {
        $statuses = self::getStatuses();
        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * scope للبحث
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('vendor', 'like', "%{$search}%")
              ->orWhere('receipt_number', 'like', "%{$search}%");
        });
    }

    /**
     * scope للفلترة حسب الفئة
     */
    public function scopeByCategory($query, $category)
    {
        if ($category && $category !== 'all') {
            return $query->where('category', $category);
        }
        return $query;
    }

    /**
     * scope للفلترة حسب التاريخ
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        if ($startDate && $endDate) {
            return $query->whereBetween('expense_date', [$startDate, $endDate]);
        }
        return $query;
    }
}
