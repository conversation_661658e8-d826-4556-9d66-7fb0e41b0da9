<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .info-box {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-right: 4px solid #007bff;
        }
        .success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار بسيط لنظام النسخ الاحتياطي</h1>
        
        <div class="info-box">
            <h3>معلومات المستخدم الحالي:</h3>
            @auth
                <p><strong>الاسم:</strong> {{ auth()->user()->name }}</p>
                <p><strong>البريد الإلكتروني:</strong> {{ auth()->user()->email }}</p>
                <p><strong>الدور:</strong> {{ auth()->user()->role }}</p>
                <p><strong>معرف الدور:</strong> {{ auth()->user()->role_id }}</p>
                <p><strong>هل هو مدير؟</strong> {{ auth()->user()->hasRole('admin') ? 'نعم ✅' : 'لا ❌' }}</p>
                <p><strong>هل هو مدير مطعم؟</strong> {{ auth()->user()->hasRole('manager') ? 'نعم ✅' : 'لا ❌' }}</p>
            @else
                <p style="color: red;">❌ غير مسجل دخول</p>
            @endauth
        </div>

        <div class="info-box">
            <h3>اختبار الصلاحيات:</h3>
            @auth
                @php
                    $user = auth()->user();
                    $canBackup = $user->hasRole('admin') || $user->hasRole('manager');
                @endphp
                
                @if($canBackup)
                    <div class="success">
                        <h4>✅ لديك صلاحية للنسخ الاحتياطي</h4>
                        <p>يمكنك إنشاء النسخ الاحتياطية</p>
                    </div>
                @else
                    <div class="error">
                        <h4>❌ ليس لديك صلاحية للنسخ الاحتياطي</h4>
                        <p>تحتاج إلى دور admin أو manager</p>
                    </div>
                @endif
            @else
                <div class="error">
                    <h4>❌ غير مسجل دخول</h4>
                    <p>يجب تسجيل الدخول أولاً</p>
                </div>
            @endauth
        </div>

        <div class="info-box">
            <h3>اختبار إنشاء نسخة احتياطية بسيطة:</h3>
            @auth
                @if($canBackup)
                    @php
                        try {
                            // إنشاء مجلد النسخ الاحتياطية
                            $backupDir = storage_path('app/backups');
                            if (!\Illuminate\Support\Facades\File::exists($backupDir)) {
                                \Illuminate\Support\Facades\File::makeDirectory($backupDir, 0755, true);
                            }
                            
                            // إنشاء ملف نسخة احتياطية بسيط
                            $timestamp = now()->format('Y-m-d_H-i-s');
                            $filename = "simple_test_backup_{$timestamp}.sql";
                            $filepath = storage_path("app/backups/{$filename}");
                            
                            $content = "-- نسخة احتياطية تجريبية بسيطة\n";
                            $content .= "-- التاريخ: " . now()->format('Y-m-d H:i:s') . "\n";
                            $content .= "-- المستخدم: " . auth()->user()->name . "\n";
                            $content .= "-- عدد المستخدمين في النظام: " . \App\Models\User::count() . "\n";
                            $content .= "\n-- تم إنشاء هذه النسخة للاختبار فقط\n";
                            
                            \Illuminate\Support\Facades\File::put($filepath, $content);
                            
                            // إنشاء سجل في قاعدة البيانات
                            $backup = \App\Models\Backup::create([
                                'filename' => $filename,
                                'type' => 'database',
                                'status' => 'completed',
                                'description' => 'نسخة احتياطية تجريبية بسيطة',
                                'created_by' => auth()->id(),
                                'is_scheduled' => false,
                                'started_at' => now(),
                                'completed_at' => now(),
                                'file_size' => \Illuminate\Support\Facades\File::size($filepath),
                            ]);
                            
                            $testResult = 'success';
                            $testMessage = 'تم إنشاء النسخة الاحتياطية بنجاح!';
                            $testDetails = [
                                'معرف النسخة' => $backup->id,
                                'اسم الملف' => $backup->filename,
                                'حجم الملف' => $backup->formatted_size,
                                'مسار الملف' => $filepath,
                                'تاريخ الإنشاء' => $backup->created_at->format('Y-m-d H:i:s')
                            ];
                            
                        } catch (\Exception $e) {
                            $testResult = 'error';
                            $testMessage = 'فشل في إنشاء النسخة الاحتياطية: ' . $e->getMessage();
                            $testDetails = [
                                'نوع الخطأ' => get_class($e),
                                'رسالة الخطأ' => $e->getMessage(),
                                'الملف' => $e->getFile(),
                                'السطر' => $e->getLine()
                            ];
                        }
                    @endphp
                    
                    @if($testResult === 'success')
                        <div class="success">
                            <h4>✅ {{ $testMessage }}</h4>
                            @foreach($testDetails as $key => $value)
                                <p><strong>{{ $key }}:</strong> {{ $value }}</p>
                            @endforeach
                        </div>
                    @else
                        <div class="error">
                            <h4>❌ {{ $testMessage }}</h4>
                            @foreach($testDetails as $key => $value)
                                <p><strong>{{ $key }}:</strong> {{ $value }}</p>
                            @endforeach
                        </div>
                    @endif
                @else
                    <div class="error">
                        <h4>❌ لا يمكن إجراء الاختبار</h4>
                        <p>ليس لديك صلاحية للنسخ الاحتياطي</p>
                    </div>
                @endif
            @else
                <div class="error">
                    <h4>❌ لا يمكن إجراء الاختبار</h4>
                    <p>غير مسجل دخول</p>
                </div>
            @endauth
        </div>

        <div class="info-box">
            <h3>معلومات النظام:</h3>
            <p><strong>إصدار PHP:</strong> {{ PHP_VERSION }}</p>
            <p><strong>إصدار Laravel:</strong> {{ app()->version() }}</p>
            <p><strong>البيئة:</strong> {{ app()->environment() }}</p>
            <p><strong>مجلد النسخ الاحتياطية:</strong> {{ storage_path('app/backups') }}</p>
            <p><strong>هل المجلد موجود؟</strong> {{ \Illuminate\Support\Facades\File::exists(storage_path('app/backups')) ? 'نعم ✅' : 'لا ❌' }}</p>
            <p><strong>هل المجلد قابل للكتابة؟</strong> {{ is_writable(storage_path('app/backups')) ? 'نعم ✅' : 'لا ❌' }}</p>
        </div>

        <div>
            <a href="{{ route('dashboard') }}" class="btn">العودة للوحة التحكم</a>
            <a href="{{ route('backup.index') }}" class="btn">صفحة النسخ الاحتياطي</a>
        </div>
    </div>
</body>
</html>
