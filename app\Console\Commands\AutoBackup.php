<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\BackupController;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class AutoBackup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:auto 
                            {--type=full : نوع النسخة الاحتياطية (full, database, files)}
                            {--email : إرسال النسخة بالإيميل}
                            {--drive : رفع النسخة إلى Google Drive}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'إنشاء نسخة احتياطية تلقائية للموقع وقاعدة البيانات';

    /*
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 بدء عملية النسخ الاحتياطي التلقائي...');
        
        try {
            $type = $this->option('type');
            $sendEmail = $this->option('email');
            $uploadToDrive = $this->option('drive');
            
            $this->info("📋 نوع النسخة: {$type}");
            
            // إنشاء instance من BackupController
            $backupController = new BackupController();
            
            // استدعاء method performBackup باستخدام reflection
            $reflection = new \ReflectionClass($backupController);
            $method = $reflection->getMethod('performBackup');
            $method->setAccessible(true);
            
            $this->info('⏳ جاري إنشاء النسخة الاحتياطية...');
            $backupPath = $method->invoke($backupController, $type);
            
            $this->info("✅ تم إنشاء النسخة الاحتياطية: " . basename($backupPath));
            
            // إرسال بالإيميل إذا كان مطلوب
            if ($sendEmail) {
                $this->info('📧 جاري إرسال النسخة بالإيميل...');
                $this->sendBackupEmail($backupPath);
                $this->info('✅ تم إرسال النسخة بالإيميل بنجاح');
            }
            
            // رفع إلى Google Drive إذا كان مطلوب
            if ($uploadToDrive) {
                $this->info('☁️ جاري رفع النسخة إلى Google Drive...');
                $this->uploadToDrive($backupPath);
                $this->info('✅ تم رفع النسخة إلى Google Drive بنجاح');
            }
            
            // تسجيل في اللوج
            Log::info('تم إنشاء نسخة احتياطية تلقائية', [
                'type' => $type,
                'path' => $backupPath,
                'email_sent' => $sendEmail,
                'drive_uploaded' => $uploadToDrive
            ]);
            
            $this->info('🎉 تمت عملية النسخ الاحتياطي بنجاح!');
            
        } catch (\Exception $e) {
            $this->error('❌ فشل في إنشاء النسخة الاحتياطية: ' . $e->getMessage());
            
            // تسجيل الخطأ في اللوج
            Log::error('فشل في النسخ الاحتياطي التلقائي', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return 1;
        }
        
        return 0;
    }
    
    /**
     * إرسال النسخة الاحتياطية بالإيميل
     */
    private function sendBackupEmail($backupPath)
    {
        $adminEmail = config('mail.admin_email', '<EMAIL>');
        
        Mail::raw('تم إنشاء نسخة احتياطية تلقائية جديدة للموقع. الملف مرفق مع هذا الإيميل.', function ($message) use ($backupPath, $adminEmail) {
            $message->to($adminEmail)
                    ->subject('نسخة احتياطية تلقائية - ' . config('app.name'))
                    ->attach($backupPath);
        });
    }
    
    /**
     * رفع النسخة الاحتياطية إلى Google Drive
     */
    private function uploadToDrive($backupPath)
    {
        // هذه الوظيفة تحتاج إلى إعداد Google Drive API
        // يمكن تنفيذها لاحقاً حسب الحاجة
        
        // Storage::disk('google')->put(basename($backupPath), file_get_contents($backupPath));
        $this->warn('⚠️ رفع Google Drive غير مفعل حالياً - يحتاج إعداد API');
    }
}
