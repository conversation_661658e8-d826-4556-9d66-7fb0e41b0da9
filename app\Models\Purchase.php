<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Purchase extends Model
{
    protected $fillable = [
        'purchase_number',
        'supplier_name',
        'supplier_phone',
        'supplier_address',
        'purchase_date',
        'total_amount',
        'paid_amount',
        'remaining_amount',
        'payment_method',
        'payment_status',
        'delivery_status',
        'delivery_date',
        'invoice_number',
        'user_id',
        'notes'
    ];

    protected $casts = [
        'purchase_date' => 'date',
        'delivery_date' => 'date',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'remaining_amount' => 'decimal:2'
    ];

    /**
     * العلاقة مع المستخدم
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع عناصر المشترى
     */
    public function items(): HasMany
    {
        return $this->hasMany(PurchaseItem::class);
    }

    /**
     * طرق الدفع المتاحة
     */
    public static function getPaymentMethods(): array
    {
        return [
            'cash' => 'نقدي',
            'card' => 'بطاقة',
            'bank_transfer' => 'تحويل بنكي',
            'check' => 'شيك',
            'credit' => 'آجل'
        ];
    }

    /**
     * حالات الدفع
     */
    public static function getPaymentStatuses(): array
    {
        return [
            'paid' => 'مدفوع',
            'pending' => 'معلق',
            'partial' => 'دفع جزئي',
            'overdue' => 'متأخر'
        ];
    }

    /**
     * حالات التسليم
     */
    public static function getDeliveryStatuses(): array
    {
        return [
            'delivered' => 'تم التسليم',
            'pending' => 'معلق',
            'partial' => 'تسليم جزئي',
            'cancelled' => 'ملغي'
        ];
    }

    /**
     * إنشاء رقم مشترى جديد
     */
    public static function generatePurchaseNumber(): string
    {
        $lastPurchase = self::latest()->first();
        $lastNumber = $lastPurchase ? intval(substr($lastPurchase->purchase_number, 2)) : 0;
        $newNumber = $lastNumber + 1;
        return 'PU' . str_pad($newNumber, 6, '0', STR_PAD_LEFT);
    }

    /**
     * الحصول على اسم طريقة الدفع
     */
    public function getPaymentMethodNameAttribute(): string
    {
        $methods = self::getPaymentMethods();
        return $methods[$this->payment_method] ?? $this->payment_method;
    }

    /**
     * الحصول على اسم حالة الدفع
     */
    public function getPaymentStatusNameAttribute(): string
    {
        $statuses = self::getPaymentStatuses();
        return $statuses[$this->payment_status] ?? $this->payment_status;
    }

    /**
     * الحصول على اسم حالة التسليم
     */
    public function getDeliveryStatusNameAttribute(): string
    {
        $statuses = self::getDeliveryStatuses();
        return $statuses[$this->delivery_status] ?? $this->delivery_status;
    }

    /**
     * scope للبحث
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('purchase_number', 'like', "%{$search}%")
              ->orWhere('supplier_name', 'like', "%{$search}%")
              ->orWhere('invoice_number', 'like', "%{$search}%");
        });
    }

    /**
     * scope للفلترة حسب حالة الدفع
     */
    public function scopeByPaymentStatus($query, $status)
    {
        if ($status && $status !== 'all') {
            return $query->where('payment_status', $status);
        }
        return $query;
    }

    /**
     * scope للفلترة حسب التاريخ
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        if ($startDate && $endDate) {
            return $query->whereBetween('purchase_date', [$startDate, $endDate]);
        }
        return $query;
    }

    /**
     * تحديث المبلغ المتبقي
     */
    public function updateRemainingAmount()
    {
        $this->remaining_amount = $this->total_amount - $this->paid_amount;

        // تحديث حالة الدفع
        if ($this->remaining_amount <= 0) {
            $this->payment_status = 'paid';
        } elseif ($this->paid_amount > 0) {
            $this->payment_status = 'partial';
        } else {
            $this->payment_status = 'pending';
        }

        $this->save();
    }
}
