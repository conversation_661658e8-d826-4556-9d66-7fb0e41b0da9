<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Backup;
use Carbon\Carbon;

class CleanupPendingBackups extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:cleanup-pending {--force : Force cleanup without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'تنظيف النسخ الاحتياطية المعلقة أو التي فشلت';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 البحث عن النسخ الاحتياطية المعلقة...');

        // البحث عن النسخ المعلقة لأكثر من 30 دقيقة
        $pendingBackups = Backup::where('status', 'pending')
            ->orWhere('status', 'running')
            ->where('created_at', '<', Carbon::now()->subMinutes(30))
            ->get();

        if ($pendingBackups->isEmpty()) {
            $this->info('✅ لا توجد نسخ احتياطية معلقة للتنظيف');
            return 0;
        }

        $this->warn("🚨 تم العثور على {$pendingBackups->count()} نسخة احتياطية معلقة:");

        foreach ($pendingBackups as $backup) {
            $this->line("- {$backup->filename} (منذ {$backup->created_at->diffForHumans()})");
        }

        if (!$this->option('force') && !$this->confirm('هل تريد تحديث حالة هذه النسخ إلى "فاشلة"؟')) {
            $this->info('تم إلغاء العملية');
            return 0;
        }

        $updated = 0;
        foreach ($pendingBackups as $backup) {
            $backup->status = 'failed';
            $backup->completed_at = now();
            $backup->save();
            $updated++;
            $this->info("✅ تم تحديث: {$backup->filename}");
        }

        $this->info("🎉 تم تحديث {$updated} نسخة احتياطية بنجاح");
        return 0;
    }
}
