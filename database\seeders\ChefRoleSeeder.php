<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class ChefRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إضافة دور الشيف إذا لم يكن موجوداً
        $chefRole = DB::table('roles')->where('name', 'chef')->first();
        if (!$chefRole) {
            $roleId = DB::table('roles')->insertGetId([
                'name' => 'chef',
                'description' => 'شيف - لديه صلاحيات إدارة الطلبات في المطبخ',
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // إنشاء مستخدم بدور الشيف فقط إذا لم يكن موجوداً
            $existingChef = DB::table('users')->where('email', '<EMAIL>')->first();
            if (!$existingChef) {
                DB::table('users')->insert([
                    'name' => 'شيف المطعم',
                    'email' => '<EMAIL>',
                    'password' => Hash::make('password'),
                    'role_id' => $roleId,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }
}
