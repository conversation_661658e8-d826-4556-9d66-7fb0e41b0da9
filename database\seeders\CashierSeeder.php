<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Role;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class CashierSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // الحصول على دور الكاشير
        $cashierRole = Role::where('name', 'cashier')->first();
        
        if (!$cashierRole) {
            $this->command->error('دور الكاشير غير موجود. يرجى تشغيل RoleSeeder أولاً.');
            return;
        }

        // إنشاء كاشيرين
        $cashiers = [
            [
                'name' => 'كاشير أول',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role_id' => $cashierRole->id,
            ],
            [
                'name' => 'كاشير ثاني',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role_id' => $cashierRole->id,
            ],
        ];

        foreach ($cashiers as $cashierData) {
            User::firstOrCreate(
                ['email' => $cashierData['email']],
                $cashierData
            );
        }

        $this->command->info('تم إنشاء الكاشيرين بنجاح.');
    }
}
