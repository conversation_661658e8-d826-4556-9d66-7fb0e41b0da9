<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin users
        User::factory()->admin()->create([
            'name' => 'مدير النظام',
            'email' => '<EMAIL>',
        ]);

        // Create manager
        User::factory()->state(function (array $attributes) {
            $managerRole = \App\Models\Role::where('name', 'manager')->first();
            return [
                'role_id' => $managerRole ? $managerRole->id : 2,
            ];
        })->create([
            'name' => 'مدير المطعم',
            'email' => '<EMAIL>',
        ]);

        // Create waiters
        $waiterRole = \App\Models\Role::where('name', 'waiter')->first();
        if ($waiterRole) {
            // إنشاء نادل محدد
            User::firstOrCreate(
                ['email' => '<EMAIL>'],
                [
                    'name' => 'نادل أول',
                    'password' => \Illuminate\Support\Facades\Hash::make('password'),
                    'role_id' => $waiterRole->id,
                ]
            );

            User::firstOrCreate(
                ['email' => '<EMAIL>'],
                [
                    'name' => 'نادل ثاني',
                    'password' => \Illuminate\Support\Facades\Hash::make('password'),
                    'role_id' => $waiterRole->id,
                ]
            );
        }

        // Create additional waiters
        User::factory()->waiter()->count(1)->create();

        // Create cashiers
        User::factory()->cashier()->count(2)->create();

        // Create kitchen staff
        User::factory()->state(function (array $attributes) {
            $kitchenRole = \App\Models\Role::where('name', 'kitchen')->first();
            return [
                'role_id' => $kitchenRole ? $kitchenRole->id : 5,
            ];
        })->count(2)->create();
    }
}
