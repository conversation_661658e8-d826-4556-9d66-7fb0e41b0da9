@extends('layouts.app')

@section('title', 'عرض الدور')

@section('content')
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>عرض الدور: {{ $role->name }}</h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('roles.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
            </a>
            <a href="{{ route('roles.edit', $role->id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
        </div>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <h5 class="card-title">معلومات الدور</h5>
            <table class="table table-bordered">
                <tr>
                    <th style="width: 30%">الاسم</th>
                    <td>{{ $role->name }}</td>
                </tr>
                <tr>
                    <th>الوصف</th>
                    <td>{{ $role->description }}</td>
                </tr>
                <tr>
                    <th>عدد المستخدمين</th>
                    <td>{{ $role->users->count() }}</td>
                </tr>
                <tr>
                    <th>تاريخ الإنشاء</th>
                    <td>{{ $role->created_at->format('Y-m-d H:i') }}</td>
                </tr>
                <tr>
                    <th>آخر تحديث</th>
                    <td>{{ $role->updated_at->format('Y-m-d H:i') }}</td>
                </tr>
            </table>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-header">
            <h5 class="card-title mb-0">المستخدمون بهذا الدور</h5>
        </div>
        <div class="card-body">
            @if($role->users->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>#</th>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($role->users as $user)
                            <tr>
                                <td>{{ $user->id }}</td>
                                <td>{{ $user->name }}</td>
                                <td>{{ $user->email }}</td>
                                <td>{{ $user->created_at->format('Y-m-d') }}</td>
                                <td>
                                    <a href="{{ route('users.show', $user->id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="alert alert-info">
                    لا يوجد مستخدمين بهذا الدور
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
