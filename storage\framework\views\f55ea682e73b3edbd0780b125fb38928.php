<?php $__env->startSection('title', 'إضافة مصروف جديد'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مصروف جديد
                    </h3>
                    <a href="<?php echo e(route('expenses.index')); ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        العودة للقائمة
                    </a>
                </div>

                <div class="card-body">
                    <?php if($errors->any()): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-1"></i>
                            <strong>يرجى تصحيح الأخطاء التالية:</strong>
                            <ul class="mb-0 mt-2">
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <form action="<?php echo e(route('expenses.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>

                        <div class="row">
                            <!-- المعلومات الأساسية -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="title" class="form-label">عنوان المصروف <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="<?php echo e(old('title')); ?>" required>
                                </div>

                                <div class="mb-3">
                                    <label for="category" class="form-label">الفئة <span class="text-danger">*</span></label>
                                    <select class="form-select" id="category" name="category" required>
                                        <option value="">اختر الفئة</option>
                                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($key); ?>" <?php echo e(old('category') == $key ? 'selected' : ''); ?>>
                                                <?php echo e($name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="amount" class="form-label">المبلغ <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="amount" name="amount" 
                                               value="<?php echo e(old('amount')); ?>" step="0.01" min="0" required>
                                        <span class="input-group-text"><?php echo e(\App\Models\Setting::get('currency', 'ر.س')); ?></span>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="expense_date" class="form-label">تاريخ المصروف <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="expense_date" name="expense_date" 
                                           value="<?php echo e(old('expense_date', date('Y-m-d'))); ?>" required>
                                </div>
                            </div>

                            <!-- تفاصيل الدفع -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="payment_method" class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                                    <select class="form-select" id="payment_method" name="payment_method" required>
                                        <option value="">اختر طريقة الدفع</option>
                                        <?php $__currentLoopData = $paymentMethods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($key); ?>" <?php echo e(old('payment_method') == $key ? 'selected' : ''); ?>>
                                                <?php echo e($name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="status" class="form-label">الحالة <span class="text-danger">*</span></label>
                                    <select class="form-select" id="status" name="status" required>
                                        <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($key); ?>" <?php echo e(old('status', 'paid') == $key ? 'selected' : ''); ?>>
                                                <?php echo e($name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="vendor" class="form-label">المورد/الجهة</label>
                                    <input type="text" class="form-control" id="vendor" name="vendor" 
                                           value="<?php echo e(old('vendor')); ?>" placeholder="اسم المورد أو الجهة">
                                </div>

                                <div class="mb-3">
                                    <label for="receipt_number" class="form-label">رقم الإيصال</label>
                                    <input type="text" class="form-control" id="receipt_number" name="receipt_number" 
                                           value="<?php echo e(old('receipt_number')); ?>" placeholder="رقم الإيصال أو الفاتورة">
                                </div>
                            </div>
                        </div>

                        <!-- الوصف والملاحظات -->
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="description" class="form-label">وصف المصروف</label>
                                    <textarea class="form-control" id="description" name="description" rows="3" 
                                              placeholder="وصف تفصيلي للمصروف..."><?php echo e(old('description')); ?></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="notes" class="form-label">ملاحظات إضافية</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="2" 
                                              placeholder="ملاحظات إضافية..."><?php echo e(old('notes')); ?></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end">
                                    <a href="<?php echo e(route('expenses.index')); ?>" class="btn btn-secondary me-2">
                                        <i class="fas fa-times me-1"></i>
                                        إلغاء
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ المصروف
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحديد التاريخ الحالي كافتراضي
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.getElementById('expense_date');
    if (!dateInput.value) {
        dateInput.value = new Date().toISOString().split('T')[0];
    }
});

// التحقق من صحة النموذج
document.querySelector('form').addEventListener('submit', function(e) {
    const amount = document.getElementById('amount').value;
    if (parseFloat(amount) <= 0) {
        e.preventDefault();
        alert('يجب أن يكون المبلغ أكبر من صفر');
        return false;
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\restaurant\resources\views/expenses/create.blade.php ENDPATH**/ ?>