<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // حذف عمود status الحالي
            $table->dropColumn('status');
        });

        // إضافة عمود status جديد مع حالة draft
        Schema::table('orders', function (Blueprint $table) {
            $table->enum('status', ['pending', 'preparing', 'ready', 'delivered', 'completed', 'cancelled', 'suspended', 'in_progress', 'draft'])->default('pending')->after('car_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // حذف عمود status الحالي
            $table->dropColumn('status');
        });

        // إعادة إضافة عمود status بدون draft
        Schema::table('orders', function (Blueprint $table) {
            $table->enum('status', ['pending', 'preparing', 'ready', 'delivered', 'completed', 'cancelled', 'suspended', 'in_progress'])->default('pending')->after('car_number');
        });
    }
};
