@extends('layouts.app')

@section('title', 'إضافة طلب جديد')

@section('styles')
<style>
    .product-card {
        cursor: pointer;
        transition: all 0.3s;
    }
    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .product-card.selected {
        border: 2px solid #0d6efd;
        background-color: rgba(13, 110, 253, 0.05);
    }
    .product-image {
        height: 120px;
        object-fit: cover;
    }
</style>
@endsection

@section('content')
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>إضافة طلب جديد</h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('orders.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
            </a>
        </div>
    </div>



    <form action="{{ route('orders.store') }}" method="POST" id="orderForm">
        @csrf

        <div class="row">
            <div class="col-md-8">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">معلومات الطلب</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="table_id" class="form-label">الطاولة <span class="text-danger">*</span></label>
                                <select class="form-select @error('table_id') is-invalid @enderror" id="table_id" name="table_id" required>
                                    <option value="">اختر الطاولة</option>
                                    @foreach($tables as $table)
                                        <option value="{{ $table->id }}" {{ old('table_id') == $table->id ? 'selected' : '' }}>
                                            {{ $table->name }} ({{ $table->capacity }} أشخاص) - {{ $table->status == 'available' ? 'متاحة' : 'محجوزة' }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('table_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="user_id" class="form-label">الكاشير المسؤول <span class="text-danger">*</span></label>
                                <select class="form-select @error('user_id') is-invalid @enderror" id="user_id" name="user_id" required>
                                    <option value="">اختر الكاشير</option>
                                    @forelse($cashiers as $cashier)
                                        <option value="{{ $cashier->id }}" {{ old('user_id') == $cashier->id ? 'selected' : '' }}>
                                            {{ $cashier->name }}
                                        </option>
                                    @empty
                                        <option value="" disabled>لا يوجد كاشيرين متاحين</option>
                                    @endforelse
                                </select>
                                @error('user_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                @if($cashiers->isEmpty())
                                    <div class="text-danger small mt-1">
                                        لا يوجد كاشيرين في النظام. يرجى إضافة كاشيرين أولاً.
                                    </div>
                                @endif
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control @error('notes') is-invalid @enderror" id="notes" name="notes" rows="2">{{ old('notes') }}</textarea>
                                @error('notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">المنتجات</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="searchProducts" placeholder="ابحث عن منتج...">
                                    <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="row" id="productsContainer">
                            @foreach($products as $product)
                            <div class="col-md-4 mb-3 product-item" data-category="{{ $product->category->name ?? 'غير مصنف' }}">
                                <div class="card h-100 product-card" data-id="{{ $product->id }}" data-name="{{ $product->name }}" data-price="{{ $product->price }}">
                                    @if($product->image)
                                        <img src="{{ asset('storage/' . $product->image) }}" class="card-img-top product-image" alt="{{ $product->name }}">
                                    @else
                                        <div class="bg-light text-center p-3 product-image">
                                            <i class="fas fa-image fa-3x text-muted mt-3"></i>
                                        </div>
                                    @endif
                                    <div class="card-body">
                                        <h6 class="card-title">{{ $product->name }}</h6>
                                        <p class="card-text text-primary fw-bold">{{ number_format($product->price, 2) }} ر.س</p>
                                        <p class="card-text text-muted small">{{ $product->category->name ?? 'غير مصنف' }}</p>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card shadow-sm sticky-top" style="top: 20px;">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">ملخص الطلب</h5>
                    </div>
                    <div class="card-body">
                        <div id="selectedProducts">
                            <div class="alert alert-info">
                                لم يتم اختيار أي منتجات بعد
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>الإجمالي:</span>
                            <span id="subtotalAmount">0.00 ر.س</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>الضريبة ({{ \App\Models\Setting::get('tax_rate', 15) }}%):</span>
                            <span id="taxAmount">0.00 ر.س</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span class="fw-bold">المجموع النهائي:</span>
                            <span class="fw-bold text-primary" id="totalAmount">0.00 ر.س</span>
                        </div>
                        <button type="submit" class="btn btn-primary w-100" id="submitOrder">
                            <i class="fas fa-save me-1"></i> حفظ الطلب
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        let selectedProducts = [];
        let total = 0;

        // البحث عن المنتجات
        document.getElementById('searchProducts').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const products = document.querySelectorAll('.product-item');

            products.forEach(product => {
                const productName = product.querySelector('.card-title').textContent.toLowerCase();
                const categoryName = product.dataset.category.toLowerCase();

                if (productName.includes(searchTerm) || categoryName.includes(searchTerm)) {
                    product.style.display = '';
                } else {
                    product.style.display = 'none';
                }
            });
        });

        // مسح البحث
        document.getElementById('clearSearch').addEventListener('click', function() {
            document.getElementById('searchProducts').value = '';
            document.querySelectorAll('.product-item').forEach(product => {
                product.style.display = '';
            });
        });

        // اختيار المنتج
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('click', function() {
                const productId = this.dataset.id;
                const productName = this.dataset.name;
                const productPrice = parseFloat(this.dataset.price);

                // التحقق مما إذا كان المنتج موجودًا بالفعل
                const existingProduct = selectedProducts.find(p => p.id === productId);

                if (existingProduct) {
                    // زيادة الكمية
                    existingProduct.quantity += 1;
                    existingProduct.subtotal = existingProduct.quantity * productPrice;
                } else {
                    // إضافة منتج جديد
                    selectedProducts.push({
                        id: productId,
                        name: productName,
                        price: productPrice,
                        quantity: 1,
                        subtotal: productPrice,
                        notes: ''
                    });

                    // تحديد المنتج المختار
                    this.classList.add('selected');
                }

                updateOrderSummary();
            });
        });

        // تحديث ملخص الطلب
        function updateOrderSummary() {
            const container = document.getElementById('selectedProducts');
            total = 0;

            if (selectedProducts.length === 0) {
                container.innerHTML = '<div class="alert alert-info">لم يتم اختيار أي منتجات بعد</div>';
                document.getElementById('subtotalAmount').textContent = '0.00 ر.س';
                document.getElementById('taxAmount').textContent = '0.00 ر.س';
                document.getElementById('totalAmount').textContent = '0.00 ر.س';
                return;
            }

            let html = '';

            selectedProducts.forEach((product, index) => {
                total += product.subtotal;

                html += `
                <div class="card mb-2">
                    <div class="card-body p-2">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">${product.name}</h6>
                            <button type="button" class="btn btn-sm btn-outline-danger remove-product" data-index="${index}">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>${product.price.toFixed(2)} ر.س</span>
                            <div class="input-group input-group-sm" style="width: 120px;">
                                <button class="btn btn-outline-secondary decrease-quantity" type="button" data-index="${index}">-</button>
                                <input type="number" class="form-control text-center product-quantity" value="${product.quantity}" min="1" data-index="${index}">
                                <button class="btn btn-outline-secondary increase-quantity" type="button" data-index="${index}">+</button>
                            </div>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span>المجموع:</span>
                            <span class="fw-bold">${product.subtotal.toFixed(2)} ر.س</span>
                        </div>
                        <div class="mt-2">
                            <input type="text" class="form-control form-control-sm product-notes" placeholder="ملاحظات" data-index="${index}" value="${product.notes}">
                        </div>
                        <input type="hidden" name="products[${index}][id]" value="${product.id}">
                        <input type="hidden" name="products[${index}][quantity]" value="${product.quantity}" class="product-quantity-input">
                        <input type="hidden" name="products[${index}][notes]" value="${product.notes}" class="product-notes-input">
                    </div>
                </div>
                `;
            });

            container.innerHTML = html;

            // حساب الضريبة والمجموع النهائي
            const subtotal = total;
            const taxRate = {{ \App\Models\Setting::get('tax_rate', 15) }} / 100; // قراءة معدل الضريبة من الإعدادات
            const tax = subtotal * taxRate;
            const finalTotal = subtotal + tax;

            // تحديث العرض
            document.getElementById('subtotalAmount').textContent = subtotal.toFixed(2) + ' ر.س';
            document.getElementById('taxAmount').textContent = tax.toFixed(2) + ' ر.س';
            document.getElementById('totalAmount').textContent = finalTotal.toFixed(2) + ' ر.س';

            // إضافة مستمعي الأحداث للأزرار
            document.querySelectorAll('.remove-product').forEach(button => {
                button.addEventListener('click', function() {
                    const index = parseInt(this.dataset.index);
                    const productId = selectedProducts[index].id;

                    // إزالة التحديد من المنتج
                    document.querySelector(`.product-card[data-id="${productId}"]`).classList.remove('selected');

                    // إزالة المنتج من المصفوفة
                    selectedProducts.splice(index, 1);

                    updateOrderSummary();
                });
            });

            document.querySelectorAll('.increase-quantity').forEach(button => {
                button.addEventListener('click', function() {
                    const index = parseInt(this.dataset.index);
                    selectedProducts[index].quantity += 1;
                    selectedProducts[index].subtotal = selectedProducts[index].quantity * selectedProducts[index].price;
                    updateOrderSummary();
                });
            });

            document.querySelectorAll('.decrease-quantity').forEach(button => {
                button.addEventListener('click', function() {
                    const index = parseInt(this.dataset.index);
                    if (selectedProducts[index].quantity > 1) {
                        selectedProducts[index].quantity -= 1;
                        selectedProducts[index].subtotal = selectedProducts[index].quantity * selectedProducts[index].price;
                        updateOrderSummary();
                    }
                });
            });

            document.querySelectorAll('.product-quantity').forEach(input => {
                input.addEventListener('change', function() {
                    const index = parseInt(this.dataset.index);
                    const quantity = parseInt(this.value);

                    if (quantity >= 1) {
                        selectedProducts[index].quantity = quantity;
                        selectedProducts[index].subtotal = selectedProducts[index].quantity * selectedProducts[index].price;
                        updateOrderSummary();
                    } else {
                        this.value = 1;
                    }
                });
            });

            document.querySelectorAll('.product-notes').forEach(input => {
                input.addEventListener('input', function() {
                    const index = parseInt(this.dataset.index);
                    selectedProducts[index].notes = this.value;
                    document.querySelectorAll('.product-notes-input')[index].value = this.value;
                });
            });

            // تحديث قيم الإدخال المخفية
            document.querySelectorAll('.product-quantity-input').forEach((input, index) => {
                input.value = selectedProducts[index].quantity;
            });

            document.querySelectorAll('.product-notes-input').forEach((input, index) => {
                input.value = selectedProducts[index].notes;
            });
        }

        // التحقق قبل إرسال النموذج
        document.getElementById('orderForm').addEventListener('submit', function(e) {
            if (selectedProducts.length === 0) {
                e.preventDefault();
                alert('يرجى اختيار منتج واحد على الأقل');
                return;
            }

            // التحقق من اختيار الكاشير
            const userId = document.getElementById('user_id').value;
            if (!userId) {
                e.preventDefault();
                alert('يرجى اختيار الكاشير المسؤول');
                return;
            }

            // التحقق من اختيار الطاولة
            const tableId = document.getElementById('table_id').value;
            if (!tableId) {
                e.preventDefault();
                alert('يرجى اختيار الطاولة');
                return;
            }
        });

        // تحقق من وجود الكاشيرين عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const cashierSelect = document.getElementById('user_id');
            const cashierOptions = cashierSelect.querySelectorAll('option[value!=""]');

            if (cashierOptions.length === 0) {
                console.warn('لا يوجد كاشيرين متاحين في القائمة المنسدلة');
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-warning mt-2';
                alertDiv.innerHTML = '<strong>تحذير:</strong> لا يوجد كاشيرين متاحين. يرجى إضافة كاشيرين من إدارة المستخدمين.';
                cashierSelect.parentNode.appendChild(alertDiv);
            } else {
                console.log('تم العثور على ' + cashierOptions.length + ' كاشير/كاشيرين');
            }
        });
    });
</script>
@endsection
