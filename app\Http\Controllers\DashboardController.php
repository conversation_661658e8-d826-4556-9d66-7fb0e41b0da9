<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Invoice;
use App\Models\Order;
use App\Models\Product;
use App\Models\Table;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DashboardController extends Controller
{
    /**
     * عرض لوحة التحكم
     */
    public function index()
    {
        // إحصائيات اليوم
        $todaySales = Invoice::where('payment_status', 'paid')
            ->whereDate('created_at', today())
            ->sum('total_amount');

        $todayOrders = Order::whereDate('created_at', today())->count();

        $activeTables = Table::whereHas('orders', function($query) {
            $query->whereIn('status', ['pending', 'processing', 'preparing']);
        })->count();

        $pendingOrders = Order::whereIn('status', ['pending', 'processing', 'preparing'])->count();

        // إحصائيات للمقارنة مع الأمس
        $yesterdaySales = Invoice::where('payment_status', 'paid')
            ->whereDate('created_at', today()->subDay())
            ->sum('total_amount');

        $yesterdayOrders = Order::whereDate('created_at', today()->subDay())->count();

        $stats = [
            'todaySales' => $todaySales,
            'todayOrders' => $todayOrders,
            'activeTables' => $activeTables,
            'pendingOrders' => $pendingOrders,
            'salesTrend' => [
                'direction' => $todaySales >= $yesterdaySales ? 'up' : 'down',
                'percentage' => $yesterdaySales > 0 ? round((($todaySales - $yesterdaySales) / $yesterdaySales) * 100, 1) : 0
            ],
            'ordersTrend' => [
                'direction' => $todayOrders >= $yesterdayOrders ? 'up' : 'down',
                'percentage' => $yesterdayOrders > 0 ? round((($todayOrders - $yesterdayOrders) / $yesterdayOrders) * 100, 1) : 0
            ]
        ];

        // الطلبات الأخيرة
        $recentOrders = Order::with(['user', 'table'])
            ->latest()
            ->take(10)
            ->get()
            ->map(function ($order) {
                return [
                    'id' => $order->id,
                    'table_number' => $order->table ? $order->table->name : null,
                    'customer_name' => $order->customer_name,
                    'total_amount' => $order->total_amount,
                    'status' => $order->status,
                    'created_at' => $order->created_at
                ];
            });

        // بيانات المبيعات للأسبوع الماضي
        $salesData = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = today()->subDays($i);
            $sales = Invoice::where('payment_status', 'paid')
                ->whereDate('created_at', $date)
                ->sum('total_amount');
            $salesData[] = $sales;
        }

        // بيانات توزيع الطلبات
        $ordersData = [
            Order::where('status', 'completed')->count(),
            Order::whereIn('status', ['processing', 'preparing'])->count(),
            Order::where('status', 'pending')->count(),
            Order::where('status', 'cancelled')->count()
        ];

        return Inertia::render('Dashboard', [
            'stats' => $stats,
            'recentOrders' => $recentOrders,
            'salesData' => $salesData,
            'ordersData' => $ordersData
        ]);
    }
}
