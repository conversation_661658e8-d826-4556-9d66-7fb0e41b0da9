<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Invoice;
use App\Models\Order;
use App\Models\Product;
use App\Models\Table;
use App\Models\User;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * عرض لوحة التحكم
     */
    public function index()
    {
        // إحصائيات لعرضها في لوحة التحكم
        $stats = [
            'users' => User::count(),
            'tables' => Table::count(),
            'categories' => Category::count(),
            'products' => Product::count(),
            'orders' => [
                'total' => Order::count(),
                'pending' => Order::where('status', 'pending')->count(),
                'processing' => Order::whereIn('status', ['processing', 'preparing', 'in_progress'])->count(),
                'completed' => Order::whereIn('status', ['completed', 'delivered'])->count(),
                'suspended' => Order::where('status', 'suspended')->count(),
                'cancelled' => Order::where('status', 'cancelled')->count(),
                'ready' => Order::where('status', 'ready')->count(),
            ],
            'invoices' => [
                'total' => Invoice::count(),
                'paid' => Invoice::where('payment_status', 'paid')->count(),
                'pending' => Invoice::where('payment_status', 'pending')->count(),
            ],
            'revenue' => [
                'total' => Invoice::where('payment_status', 'paid')->sum('final_amount'),
                'today' => Invoice::where('payment_status', 'paid')
                    ->whereDate('created_at', today())
                    ->sum('final_amount'),
            ],
        ];

        // الطلبات الأخيرة
        $latestOrders = Order::with(['user', 'table'])
            ->latest()
            ->take(5)
            ->get();

        // الفواتير الأخيرة
        $latestInvoices = Invoice::with(['user', 'order'])
            ->latest()
            ->take(5)
            ->get();

        return view('dashboard.index', compact('stats', 'latestOrders', 'latestInvoices'));
    }
}
