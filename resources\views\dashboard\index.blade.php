@extends('layouts.app')

@section('title', 'لوحة التحكم')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">لوحة التحكم</h1>
        <div>
            <span class="text-muted">{{ now()->format('Y-m-d') }}</span>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-4">
            <div class="card card-dashboard bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">إجمالي الطلبات</h6>
                            <h2 class="mt-2 mb-0">{{ $stats['orders']['total'] }}</h2>
                        </div>
                        <div>
                            <i class="fas fa-clipboard-list fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card card-dashboard bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            @if(auth()->user()->hasRole('cashier'))
                                <h6 class="card-title mb-0">إيرادات اليوم</h6>
                                <h2 class="mt-2 mb-0">{{ number_format($stats['revenue']['today'], 2) }} ر.ع</h2>
                            @else
                                <h6 class="card-title mb-0">إجمالي الإيرادات</h6>
                                <h2 class="mt-2 mb-0">{{ number_format($stats['revenue']['total'], 2) }} ر.ع</h2>
                            @endif
                        </div>
                        <div>
                            @if(auth()->user()->hasRole('cashier'))
                                <i class="fas fa-calendar-day fa-3x opacity-50"></i>
                            @else
                                <i class="fas fa-money-bill-wave fa-3x opacity-50"></i>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card card-dashboard bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">المنتجات</h6>
                            <h2 class="mt-2 mb-0">{{ $stats['products'] }}</h2>
                        </div>
                        <div>
                            <i class="fas fa-utensils fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card card-dashboard bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-0">الطاولات</h6>
                            <h2 class="mt-2 mb-0">{{ $stats['tables'] }}</h2>
                        </div>
                        <div>
                            <i class="fas fa-chair fa-3x opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Orders Status -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card card-dashboard h-100">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">حالة الطلبات</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-3">
                            <div class="p-3 border rounded bg-light">
                                <h3 class="text-warning">{{ $stats['orders']['pending'] }}</h3>
                                <p class="mb-0">قيد الانتظار</p>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="p-3 border rounded bg-light">
                                <h3 class="text-primary">{{ $stats['orders']['processing'] }}</h3>
                                <p class="mb-0">قيد التحضير</p>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="p-3 border rounded bg-light">
                                <h3 class="text-info">{{ $stats['orders']['ready'] }}</h3>
                                <p class="mb-0">جاهزة</p>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="p-3 border rounded bg-light">
                                <h3 class="text-success">{{ $stats['orders']['completed'] }}</h3>
                                <p class="mb-0">مكتملة</p>
                            </div>
                        </div>
                    </div>

                    <div class="row text-center mt-3">
                        <div class="col-6">
                            <div class="p-3 border rounded bg-light">
                                <h3 class="text-secondary">{{ $stats['orders']['suspended'] }}</h3>
                                <p class="mb-0">معلقة</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 border rounded bg-light">
                                <h3 class="text-danger">{{ $stats['orders']['cancelled'] }}</h3>
                                <p class="mb-0">ملغاة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card card-dashboard h-100">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">حالة الفواتير</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="p-3 border rounded bg-light">
                                <h3 class="text-warning">{{ $stats['invoices']['pending'] }}</h3>
                                <p class="mb-0">قيد الانتظار</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 border rounded bg-light">
                                <h3 class="text-success">{{ $stats['invoices']['paid'] }}</h3>
                                <p class="mb-0">مدفوعة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Latest Orders and Invoices -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card card-dashboard">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">أحدث الطلبات</h5>
                    <a href="{{ route('orders.index') }}" class="btn btn-sm btn-primary">عرض الكل</a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>الطاولة</th>
                                    <th>الحالة</th>
                                    <th>المبلغ</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($latestOrders as $order)
                                <tr>
                                    <td>{{ $order->id }}</td>
                                    <td>{{ $order->table ? $order->table->name : 'غير محدد' }}</td>
                                    <td>
                                        @php
                                            $statusBadge = '';
                                            switch($order->status) {
                                                case 'pending':
                                                    $statusBadge = '<span class="badge bg-warning">قيد الانتظار</span>';
                                                    break;
                                                case 'in_progress':
                                                    $statusBadge = '<span class="badge bg-info">قيد التنفيذ</span>';
                                                    break;
                                                case 'processing':
                                                case 'preparing':
                                                    $statusBadge = '<span class="badge bg-primary">قيد التحضير</span>';
                                                    break;
                                                case 'ready':
                                                    $statusBadge = '<span class="badge bg-success">جاهز</span>';
                                                    break;
                                                case 'delivered':
                                                    $statusBadge = '<span class="badge bg-success">تم التسليم</span>';
                                                    break;
                                                case 'completed':
                                                    $statusBadge = '<span class="badge bg-success">مكتمل</span>';
                                                    break;
                                                case 'suspended':
                                                    $statusBadge = '<span class="badge bg-secondary">معلق</span>';
                                                    break;
                                                case 'cancelled':
                                                    $statusBadge = '<span class="badge bg-danger">ملغي</span>';
                                                    break;
                                                default:
                                                    $statusBadge = '<span class="badge bg-dark">' . $order->status . '</span>';
                                            }
                                        @endphp
                                        {!! $statusBadge !!}
                                    </td>
                                    <td>{{ number_format($order->total_amount, 2) }}</td>
                                    <td>{{ $order->created_at->format('Y-m-d H:i') }}</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center">لا توجد طلبات حتى الآن</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card card-dashboard">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">أحدث الفواتير</h5>
                    <a href="{{ route('invoices.index') }}" class="btn btn-sm btn-primary">عرض الكل</a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>رقم الطلب</th>
                                    <th>الحالة</th>
                                    <th>المبلغ</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($latestInvoices as $invoice)
                                <tr>
                                    <td>{{ $invoice->invoice_number }}</td>
                                    <td>{{ $invoice->order_id }}</td>
                                    <td>
                                        @if($invoice->payment_status == 'pending')
                                            <span class="badge bg-warning">قيد الانتظار</span>
                                        @elseif($invoice->payment_status == 'paid')
                                            <span class="badge bg-success">مدفوعة</span>
                                        @else
                                            <span class="badge bg-danger">ملغاة</span>
                                        @endif
                                    </td>
                                    <td>{{ number_format($invoice->final_amount, 2) }}</td>
                                    <td>{{ $invoice->created_at->format('Y-m-d H:i') }}</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center">لا توجد فواتير حتى الآن</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
