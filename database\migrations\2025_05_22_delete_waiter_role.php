<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // حذف دور النادل من قاعدة البيانات
        DB::table('roles')->where('name', 'waiter')->delete();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // إعادة إنشاء دور النادل
        DB::table('roles')->insert([
            'name' => 'waiter',
            'description' => 'نادل - لديه صلاحيات إدارة الطلبات والطاولات',
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
};
