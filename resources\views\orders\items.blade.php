@extends('layouts.app')

@section('title', 'عناصر الطلب')

@section('content')
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>عناصر الطلب رقم: {{ $order->id }}</h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('orders.show', $order->id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للطلب
            </a>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <h5 class="card-title mb-0">معلومات الطلب</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 40%">رقم الطلب</th>
                            <td>{{ $order->id }}</td>
                        </tr>
                        <tr>
                            <th>الطاولة</th>
                            <td>
                                @if($order->table)
                                    <a href="{{ route('tables.show', $order->table->id) }}">{{ $order->table->name }}</a>
                                @else
                                    غير محدد
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>النادل</th>
                            <td>
                                @if($order->user)
                                    <a href="{{ route('users.show', $order->user->id) }}">{{ $order->user->name }}</a>
                                @else
                                    غير محدد
                                @endif
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 40%">الحالة</th>
                            <td>
                                @if($order->status == 'pending')
                                    <span class="badge bg-secondary">قيد الانتظار</span>
                                @elseif($order->status == 'preparing')
                                    <span class="badge bg-primary">قيد التحضير</span>
                                @elseif($order->status == 'ready')
                                    <span class="badge bg-info">جاهز</span>
                                @elseif($order->status == 'delivered')
                                    <span class="badge bg-success">تم التوصيل</span>
                                @elseif($order->status == 'completed')
                                    <span class="badge bg-success">مكتمل</span>
                                @elseif($order->status == 'cancelled')
                                    <span class="badge bg-danger">ملغي</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>المبلغ الإجمالي</th>
                            <td class="fw-bold">{{ number_format($order->total_amount, 2) }} ر.س</td>
                        </tr>
                        <tr>
                            <th>تاريخ الإنشاء</th>
                            <td>{{ $order->created_at->format('Y-m-d H:i') }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow-sm mt-4">
        <div class="card-header bg-light">
            <h5 class="card-title mb-0">عناصر الطلب</h5>
        </div>
        <div class="card-body">
            @if($order->orderItems->count() > 0)
                <div class="row">
                    @foreach($order->orderItems as $item)
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                @if($item->product && $item->product->image)
                                    <img src="{{ asset('storage/' . $item->product->image) }}" class="card-img-top" alt="{{ $item->product->name }}" style="height: 180px; object-fit: cover;">
                                @else
                                    <div class="bg-light text-center p-4" style="height: 180px;">
                                        <i class="fas fa-utensils fa-4x text-muted mt-4"></i>
                                    </div>
                                @endif
                                <div class="card-body">
                                    <h5 class="card-title">
                                        @if($item->product)
                                            {{ $item->product->name }}
                                        @else
                                            منتج محذوف
                                        @endif
                                    </h5>
                                    <p class="card-text">
                                        <strong>السعر:</strong> {{ number_format($item->unit_price, 2) }} ر.س<br>
                                        <strong>الكمية:</strong> {{ $item->quantity }}<br>
                                        <strong>المجموع:</strong> {{ number_format($item->subtotal, 2) }} ر.س
                                    </p>
                                    @if($item->notes)
                                        <div class="alert alert-info p-2 mb-0">
                                            <small><strong>ملاحظات:</strong> {{ $item->notes }}</small>
                                        </div>
                                    @endif
                                </div>
                                @if($item->product)
                                    <div class="card-footer bg-white">
                                        <a href="{{ route('products.show', $item->product->id) }}" class="btn btn-sm btn-outline-primary w-100">
                                            <i class="fas fa-eye me-1"></i> عرض المنتج
                                        </a>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="alert alert-success">
                            <h5 class="alert-heading">المجموع الكلي</h5>
                            <p class="mb-0 fs-4">{{ number_format($order->total_amount, 2) }} ر.س</p>
                        </div>
                    </div>
                </div>
            @else
                <div class="alert alert-info">
                    لا توجد عناصر في هذا الطلب
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
