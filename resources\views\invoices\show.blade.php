@extends('layouts.app')

@section('title', 'عرض الفاتورة')

@section('content')
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>عرض الفاتورة: {{ $invoice->invoice_number }}</h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('invoices.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
            </a>
            @if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('manager'))
            <a href="{{ route('invoices.edit', $invoice->id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
            @endif
            <div class="dropdown d-inline-block">
                <button class="btn btn-primary dropdown-toggle" type="button" id="printDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-print me-1"></i> طباعة
                </button>
                <ul class="dropdown-menu" aria-labelledby="printDropdown">
                    <li><a class="dropdown-item" href="{{ route('invoices.print', $invoice->id) }}" target="_blank">طباعة A4</a></li>
                    <li><a class="dropdown-item" href="{{ route('invoices.print.85mm', $invoice->id) }}" target="_blank">طباعة 85مم</a></li>
                    <li><a class="dropdown-item" href="{{ route('invoices.print.56mm', $invoice->id) }}" target="_blank">طباعة 56مم</a></li>
                </ul>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">معلومات الفاتورة</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 40%">رقم الفاتورة</th>
                            <td>{{ $invoice->invoice_number }}</td>
                        </tr>
                        <tr>
                            <th>الطلب</th>
                            <td>
                                @if($invoice->order)
                                    <a href="{{ route('orders.show', $invoice->order->id) }}">طلب #{{ $invoice->order->id }}</a>
                                @else
                                    غير محدد
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>الطاولة</th>
                            <td>
                                @if($invoice->order && $invoice->order->table)
                                    {{ $invoice->order->table->name }}
                                @elseif($invoice->order && $invoice->order->order_type == 'takeaway')
                                    <span class="badge bg-info">طلب خارجي</span>
                                @else
                                    غير محدد
                                @endif
                            </td>
                        </tr>
                        @if($invoice->order && $invoice->order->order_type == 'takeaway' && $invoice->order->car_number)
                        <tr>
                            <th>رقم السيارة</th>
                            <td>{{ $invoice->order->car_number }}</td>
                        </tr>
                        @endif
                        @if($invoice->order && $invoice->order->order_type == 'takeaway' && $invoice->order->customer_phone)
                        <tr>
                            <th>رقم الهاتف</th>
                            <td>{{ $invoice->order->customer_phone }}</td>
                        </tr>
                        @endif
                        <tr>
                            <th>الكاشير</th>
                            <td>
                                @if($invoice->user)
                                    {{ $invoice->user->name }}
                                @else
                                    غير محدد
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>طريقة الدفع</th>
                            <td>
                                @if($invoice->payment_method == 'cash')
                                    <span class="badge bg-success">نقداً</span>
                                @elseif($invoice->payment_method == 'credit_card')
                                    <span class="badge bg-info">بطاقة ائتمان</span>
                                @elseif($invoice->payment_method == 'debit_card')
                                    <span class="badge bg-primary">بطاقة خصم</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>حالة الدفع</th>
                            <td>
                                @if($invoice->payment_status == 'paid')
                                    <span class="badge bg-success">مدفوعة</span>
                                @elseif($invoice->payment_status == 'partial')
                                    <span class="badge bg-warning">مدفوعة جزئياً</span>
                                @elseif($invoice->payment_status == 'unpaid')
                                    <span class="badge bg-danger">غير مدفوعة</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>تاريخ الإنشاء</th>
                            <td>{{ $invoice->created_at->format('Y-m-d H:i') }}</td>
                        </tr>
                    </table>

                    @if($invoice->notes)
                    <div class="alert alert-info mt-3">
                        <h6 class="alert-heading">ملاحظات:</h6>
                        <p class="mb-0">{{ $invoice->notes }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">ملخص المبالغ</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <table class="table table-bordered">
                                <tr>
                                    <th>المبلغ الإجمالي</th>
                                    <td class="text-end">{{ number_format($invoice->total_amount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                </tr>
                                <tr>
                                    <th>المبلغ المدفوع</th>
                                    <td class="text-end">{{ number_format($invoice->paid_amount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                </tr>
                                <tr class="{{ $invoice->remaining_amount > 0 ? 'table-danger' : 'table-success' }}">
                                    <th>المبلغ المتبقي</th>
                                    <td class="text-end fw-bold">{{ number_format($invoice->remaining_amount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">عناصر الطلب</h5>
                </div>
                <div class="card-body">
                    @if($invoice->order && $invoice->order->orderItems->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>#</th>
                                        <th>المنتج</th>
                                        <th>السعر</th>
                                        <th>الكمية</th>
                                        <th>المجموع</th>
                                        <th>ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($invoice->order->orderItems as $index => $item)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>
                                            @if($item->product)
                                                {{ $item->product->name }}
                                            @else
                                                منتج محذوف
                                            @endif
                                        </td>
                                        <td>{{ number_format($item->unit_price, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                        <td>{{ $item->quantity }}</td>
                                        <td>{{ number_format($item->subtotal, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                        <td>{{ $item->notes ?? '-' }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th colspan="4" class="text-end">المجموع الكلي:</th>
                                        <th>{{ number_format($invoice->total_amount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            لا توجد عناصر في هذا الطلب
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
