<?php

namespace Database\Factories;

use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Category>
 */
class CategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = [
            'مقبلات',
            'سلطات',
            'أطباق رئيسية',
            'مشاوي',
            'حلويات',
            'مشروبات ساخنة',
            'مشروبات باردة',
            'عصائر طازجة',
            'وجبات سريعة',
            'أطباق جانبية'
        ];
        
        static $index = 0;
        
        return [
            'name' => $categories[$index++ % count($categories)],
            'description' => $this->faker->sentence(),
            'image' => null,
        ];
    }
}
