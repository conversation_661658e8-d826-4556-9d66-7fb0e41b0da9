<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ChefController extends Controller
{
    /**
     * إنشاء مثيل جديد من المتحكم
     */
    public function __construct()
    {
        // التحقق من المصادقة فقط
        $this->middleware('auth');
    }

    /**
     * عرض واجهة الشيف
     */
    public function index()
    {
        // الحصول على جميع الطلبات النشطة (قيد الانتظار أو قيد التنفيذ أو قيد التحضير أو جاهزة أو معلقة)
        // للطلبات المعلقة، نعتمد على حالة التحضير لتحديد القائمة التي ستظهر فيها

        // 1. الطلبات قيد الانتظار:
        // - الطلبات الداخلية (dine_in) ذات الحالة pending أو in_progress
        // - الطلبات الداخلية (dine_in) ذات الحالة suspended وحالة التحضير pending أو null
        // - الطلبات الخارجية (takeaway) ذات الحالة pending
        $pendingOrders = Order::where(function($query) {
                // الطلبات الداخلية العادية (غير معلقة)
                $query->where('order_type', 'dine_in')
                      ->whereIn('status', ['pending', 'in_progress']);
            })
            ->orWhere(function($query) {
                // الطلبات الداخلية المعلقة مع حالة تحضير pending أو null
                $query->where('order_type', 'dine_in')
                      ->where('status', 'suspended')
                      ->where(function($q) {
                          $q->where('preparation_status', 'pending')
                            ->orWhereNull('preparation_status');
                      });
            })
            ->orWhere(function($query) {
                // الطلبات الخارجية العادية
                $query->where('order_type', 'takeaway')
                      ->where('status', 'pending');
            })
            ->orWhere(function($query) {
                // الطلبات الخارجية المعلقة مع حالة تحضير pending أو null
                $query->where('order_type', 'takeaway')
                      ->where('status', 'suspended')
                      ->where(function($q) {
                          $q->where('preparation_status', 'pending')
                            ->orWhereNull('preparation_status');
                      });
            })
            ->with(['table', 'orderItems.product', 'user'])
            ->orderBy('created_at', 'asc')
            ->get();

        // 2. الطلبات قيد التحضير:
        // - الطلبات ذات الحالة preparing
        // - الطلبات الداخلية (dine_in) ذات الحالة suspended وحالة التحضير preparing
        // - الطلبات الخارجية (takeaway) ذات الحالة suspended وحالة التحضير preparing
        $preparingOrders = Order::where(function($query) {
                // الطلبات العادية قيد التحضير
                $query->where('status', 'preparing');
            })
            ->orWhere(function($query) {
                // الطلبات الداخلية المعلقة قيد التحضير
                $query->where('order_type', 'dine_in')
                      ->where('status', 'suspended')
                      ->where('preparation_status', 'preparing');
            })
            ->orWhere(function($query) {
                // الطلبات الخارجية المعلقة قيد التحضير
                $query->where('order_type', 'takeaway')
                      ->where('status', 'suspended')
                      ->where('preparation_status', 'preparing');
            })
            ->with(['table', 'orderItems.product', 'user'])
            ->orderBy('created_at', 'asc')
            ->get();

        // 3. الطلبات الجاهزة:
        // - الطلبات ذات الحالة ready
        // - الطلبات الداخلية (dine_in) ذات الحالة suspended وحالة التحضير ready
        // - الطلبات الخارجية (takeaway) ذات الحالة suspended وحالة التحضير ready
        $readyOrders = Order::where(function($query) {
                // الطلبات العادية الجاهزة
                $query->where('status', 'ready');
            })
            ->orWhere(function($query) {
                // الطلبات الداخلية المعلقة الجاهزة
                $query->where('order_type', 'dine_in')
                      ->where('status', 'suspended')
                      ->where('preparation_status', 'ready');
            })
            ->orWhere(function($query) {
                // الطلبات الخارجية المعلقة الجاهزة
                $query->where('order_type', 'takeaway')
                      ->where('status', 'suspended')
                      ->where('preparation_status', 'ready');
            })
            ->with(['table', 'orderItems.product', 'user'])
            ->orderBy('created_at', 'asc')
            ->get();

        return view('chef.index', compact('pendingOrders', 'preparingOrders', 'readyOrders'));
    }

    /**
     * الحصول على جميع الطلبات النشطة
     */
    public function getActiveOrders()
    {
        // الحصول على الطلبات النشطة (قيد الانتظار أو قيد التنفيذ أو قيد التحضير أو جاهزة أو معلقة)
        // للطلبات المعلقة، نعتمد على حالة التحضير لتحديد القائمة التي ستظهر فيها

        // 1. الطلبات قيد الانتظار:
        // - الطلبات الداخلية (dine_in) ذات الحالة pending أو in_progress
        // - الطلبات الداخلية (dine_in) ذات الحالة suspended وحالة التحضير pending أو null
        // - الطلبات الخارجية (takeaway) ذات الحالة pending
        $pendingOrders = Order::where(function($query) {
                // الطلبات الداخلية العادية (غير معلقة)
                $query->where('order_type', 'dine_in')
                      ->whereIn('status', ['pending', 'in_progress']);
            })
            ->orWhere(function($query) {
                // الطلبات الداخلية المعلقة مع حالة تحضير pending أو null
                $query->where('order_type', 'dine_in')
                      ->where('status', 'suspended')
                      ->where(function($q) {
                          $q->where('preparation_status', 'pending')
                            ->orWhereNull('preparation_status');
                      });
            })
            ->orWhere(function($query) {
                // الطلبات الخارجية العادية
                $query->where('order_type', 'takeaway')
                      ->where('status', 'pending');
            })
            ->orWhere(function($query) {
                // الطلبات الخارجية المعلقة مع حالة تحضير pending أو null
                $query->where('order_type', 'takeaway')
                      ->where('status', 'suspended')
                      ->where(function($q) {
                          $q->where('preparation_status', 'pending')
                            ->orWhereNull('preparation_status');
                      });
            })
            ->with(['table', 'user', 'orderItems.product'])
            ->orderBy('created_at', 'asc')
            ->get();

        // 2. الطلبات قيد التحضير:
        // - الطلبات ذات الحالة preparing
        // - الطلبات الداخلية (dine_in) ذات الحالة suspended وحالة التحضير preparing
        // - الطلبات الخارجية (takeaway) ذات الحالة suspended وحالة التحضير preparing
        $preparingOrders = Order::where(function($query) {
                // الطلبات العادية قيد التحضير
                $query->where('status', 'preparing');
            })
            ->orWhere(function($query) {
                // الطلبات الداخلية المعلقة قيد التحضير
                $query->where('order_type', 'dine_in')
                      ->where('status', 'suspended')
                      ->where('preparation_status', 'preparing');
            })
            ->orWhere(function($query) {
                // الطلبات الخارجية المعلقة قيد التحضير
                $query->where('order_type', 'takeaway')
                      ->where('status', 'suspended')
                      ->where('preparation_status', 'preparing');
            })
            ->with(['table', 'user', 'orderItems.product'])
            ->orderBy('created_at', 'asc')
            ->get();

        // 3. الطلبات الجاهزة:
        // - الطلبات ذات الحالة ready
        // - الطلبات الداخلية (dine_in) ذات الحالة suspended وحالة التحضير ready
        // - الطلبات الخارجية (takeaway) ذات الحالة suspended وحالة التحضير ready
        $readyOrders = Order::where(function($query) {
                // الطلبات العادية الجاهزة
                $query->where('status', 'ready');
            })
            ->orWhere(function($query) {
                // الطلبات الداخلية المعلقة الجاهزة
                $query->where('order_type', 'dine_in')
                      ->where('status', 'suspended')
                      ->where('preparation_status', 'ready');
            })
            ->orWhere(function($query) {
                // الطلبات الخارجية المعلقة الجاهزة
                $query->where('order_type', 'takeaway')
                      ->where('status', 'suspended')
                      ->where('preparation_status', 'ready');
            })
            ->with(['table', 'user', 'orderItems.product'])
            ->orderBy('created_at', 'asc')
            ->get();

        // التأكد من أن total_amount هو رقم
        $pendingOrders->transform(function ($order) {
            $order->total_amount = (float) $order->total_amount;
            return $order;
        });

        $preparingOrders->transform(function ($order) {
            $order->total_amount = (float) $order->total_amount;
            return $order;
        });

        $readyOrders->transform(function ($order) {
            $order->total_amount = (float) $order->total_amount;
            return $order;
        });

        return response()->json([
            'success' => true,
            'pendingOrders' => $pendingOrders,
            'preparingOrders' => $preparingOrders,
            'readyOrders' => $readyOrders
        ]);
    }

    /**
     * تحديث حالة الطلب
     */
    public function updateOrderStatus(Request $request, $id)
    {
        $order = Order::findOrFail($id);

        $validated = $request->validate([
            'status' => 'required|in:pending,in_progress,preparing,ready,delivered,completed,suspended',
            'notes' => 'nullable|string',
        ]);

        DB::beginTransaction();

        try {
            // تحديث حالة الطلب
            $oldStatus = $order->status;

            // إذا كان الطلب معلق (suspended)، نحتفظ بحالة التعليق
            // ولكن نضيف علامة لتتبع حالة التحضير الفعلية
            if ($oldStatus === 'suspended') {
                // إضافة حالة التحضير الفعلية في عمود preparation_status
                $preparationStatus = $validated['status'];
                $order->preparation_status = $preparationStatus;

                // نحتفظ بحالة التعليق إلا إذا كانت الحالة الجديدة هي "مكتمل"
                if ($validated['status'] === 'completed') {
                    $order->status = 'completed';
                } else {
                    $order->status = 'suspended';
                }
            } else {
                // في حالة الطلبات غير المعلقة، نقوم بتحديث الحالة كالمعتاد
                $order->status = $validated['status'];
                $order->preparation_status = $validated['status'];
            }

            // إذا كان الطلب داخلي وتم تغيير حالته إلى "تم التسليم"، نحتفظ بحالة التعليق
            if ($order->order_type === 'dine_in' && $validated['status'] === 'delivered' && $oldStatus === 'suspended') {
                $order->preparation_status = 'delivered';
                $order->status = 'suspended';
            }

            // إضافة ملاحظات إذا وجدت
            if (isset($validated['notes']) && !empty($validated['notes'])) {
                $order->notes = $validated['notes'];
            }

            $order->save();

            // تحديد ما إذا كان يجب إرسال إشعار "جاهز للتسليم"
            $shouldSendReadyNotification = false;
            $shouldSendDeliveredNotification = false;

            // إذا كان الطلب معلق، نتحقق من حالة التحضير
            if ($oldStatus === 'suspended') {
                // إذا تم تعيين حالة التحضير إلى "جاهز للتسليم"
                if ($validated['status'] === 'ready') {
                    $shouldSendReadyNotification = true;
                }
                // إذا تم تعيين حالة التحضير إلى "تم التسليم"
                else if ($validated['status'] === 'delivered') {
                    $shouldSendDeliveredNotification = true;
                }
            }
            // إذا كان الطلب خارجي أو داخلي غير معلق، نتحقق من الحالة الجديدة
            else {
                if ($validated['status'] === 'ready') {
                    $shouldSendReadyNotification = true;
                }
                else if ($validated['status'] === 'delivered') {
                    $shouldSendDeliveredNotification = true;
                }
            }

            // إرسال إشعار "جاهز للتسليم" إذا لزم الأمر
            if ($shouldSendReadyNotification && $order->user_id) {
                // إعداد بيانات الإشعار
                $notificationData = ['order_id' => $order->id];

                // إضافة رقم السيارة إذا كان الطلب خارجي وله رقم سيارة
                if ($order->order_type === 'takeaway' && !empty($order->car_number)) {
                    $notificationData['car_number'] = $order->car_number;
                }

                // إنشاء رسالة الإشعار
                $message = "الطلب رقم {$order->id} جاهز للتسليم";
                if ($order->order_type === 'takeaway' && !empty($order->car_number)) {
                    $message .= " (رقم السيارة: {$order->car_number})";
                }

                // إنشاء إشعار في قاعدة البيانات
                $notification = new \App\Models\Notification();
                $notification->user_id = $order->user_id; // معرف النادل الذي قام بإنشاء الطلب
                $notification->title = 'طلب جاهز للتسليم';
                $notification->message = $message;
                $notification->type = 'order_ready';
                $notification->data = json_encode($notificationData);
                $notification->read = false;
                $notification->save();
            }

            // إرسال إشعار "تم تسليم الطلب" إذا لزم الأمر
            if ($shouldSendDeliveredNotification && $order->user_id) {
                // إعداد بيانات الإشعار
                $notificationData = ['order_id' => $order->id];

                // إضافة رقم السيارة إذا كان الطلب خارجي وله رقم سيارة
                if ($order->order_type === 'takeaway' && !empty($order->car_number)) {
                    $notificationData['car_number'] = $order->car_number;
                }

                // إنشاء رسالة الإشعار
                $message = "تم تسليم الطلب رقم {$order->id}";
                if ($order->order_type === 'takeaway' && !empty($order->car_number)) {
                    $message .= " (رقم السيارة: {$order->car_number})";
                }

                // إنشاء إشعار في قاعدة البيانات
                $notification = new \App\Models\Notification();
                $notification->user_id = $order->user_id; // معرف النادل الذي قام بإنشاء الطلب
                $notification->title = 'تم تسليم الطلب';
                $notification->message = $message;
                $notification->type = 'order_delivered';
                $notification->data = json_encode($notificationData);
                $notification->read = false;
                $notification->save();
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث حالة الطلب بنجاح',
                'order' => $order->load(['table', 'orderItems.product', 'user']),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث حالة الطلب: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * الحصول على تفاصيل طلب محدد
     */
    public function getOrder($id)
    {
        $order = Order::with(['table', 'user', 'orderItems.product'])
            ->findOrFail($id);

        // التأكد من أن total_amount هو رقم
        $order->total_amount = (float) $order->total_amount;

        // التأكد من أن unit_price و subtotal في عناصر الطلب هي أرقام
        if ($order->orderItems) {
            foreach ($order->orderItems as $item) {
                $item->unit_price = (float) $item->unit_price;
                $item->subtotal = (float) $item->subtotal;
                $item->quantity = (int) $item->quantity;
            }
        }

        return response()->json([
            'success' => true,
            'order' => $order
        ]);
    }
}
