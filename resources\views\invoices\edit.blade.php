@extends('layouts.app')

@section('title', 'تعديل الفاتورة')

@section('content')
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>تعديل الفاتورة: {{ $invoice->invoice_number }}</h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('invoices.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
            </a>
            <a href="{{ route('invoices.show', $invoice->id) }}" class="btn btn-info">
                <i class="fas fa-eye me-1"></i> عرض الفاتورة
            </a>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            <form action="{{ route('invoices.update', $invoice->id) }}" method="POST">
                @csrf
                @method('PUT')

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">رقم الفاتورة</label>
                        <input type="text" class="form-control" value="{{ $invoice->invoice_number }}" readonly>
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">الطلب</label>
                        <input type="text" class="form-control" value="طلب #{{ $invoice->order->id ?? 'غير محدد' }}" readonly>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="user_id" class="form-label">الكاشير <span class="text-danger">*</span></label>
                        <select class="form-select @error('user_id') is-invalid @enderror" id="user_id" name="user_id" required>
                            <option value="">اختر الكاشير</option>
                            @foreach($cashiers as $cashier)
                                <option value="{{ $cashier->id }}" {{ old('user_id', $invoice->user_id) == $cashier->id ? 'selected' : '' }}>
                                    {{ $cashier->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('user_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">المبلغ الإجمالي</label>
                        <div class="input-group">
                            <input type="text" class="form-control" value="{{ number_format($invoice->total_amount, 2) }}" readonly>
                            <span class="input-group-text">{{ \App\Models\Setting::get('currency', 'ر.س') }}</span>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="payment_method" class="form-label">طريقة الدفع <span class="text-danger">*</span></label>
                        <select class="form-select @error('payment_method') is-invalid @enderror" id="payment_method" name="payment_method" required>
                            <option value="cash" {{ old('payment_method', $invoice->payment_method) == 'cash' ? 'selected' : '' }}>نقداً</option>
                            <option value="credit_card" {{ old('payment_method', $invoice->payment_method) == 'credit_card' ? 'selected' : '' }}>بطاقة ائتمان</option>
                            <option value="debit_card" {{ old('payment_method', $invoice->payment_method) == 'debit_card' ? 'selected' : '' }}>بطاقة خصم</option>
                        </select>
                        @error('payment_method')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-4">
                        <label for="payment_status" class="form-label">حالة الدفع <span class="text-danger">*</span></label>
                        <select class="form-select @error('payment_status') is-invalid @enderror" id="payment_status" name="payment_status" required>
                            <option value="paid" {{ old('payment_status', $invoice->payment_status) == 'paid' ? 'selected' : '' }}>مدفوعة</option>
                            <option value="partial" {{ old('payment_status', $invoice->payment_status) == 'partial' ? 'selected' : '' }}>مدفوعة جزئياً</option>
                            <option value="unpaid" {{ old('payment_status', $invoice->payment_status) == 'unpaid' ? 'selected' : '' }}>غير مدفوعة</option>
                        </select>
                        @error('payment_status')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="col-md-4">
                        <label for="paid_amount" class="form-label">المبلغ المدفوع <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="number" step="0.01" min="0" class="form-control @error('paid_amount') is-invalid @enderror" id="paid_amount" name="paid_amount" value="{{ old('paid_amount', $invoice->paid_amount) }}" required>
                            <span class="input-group-text">{{ \App\Models\Setting::get('currency', 'ر.س') }}</span>
                            @error('paid_amount')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control @error('notes') is-invalid @enderror" id="notes" name="notes" rows="3">{{ old('notes', $invoice->notes) }}</textarea>
                        @error('notes')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>المبلغ الإجمالي:</strong>
                                    <span id="total_amount">{{ number_format($invoice->total_amount, 2) }}</span> {{ \App\Models\Setting::get('currency', 'ر.س') }}
                                </div>
                                <div>
                                    <strong>المبلغ المدفوع:</strong>
                                    <span id="display_paid_amount">{{ number_format($invoice->paid_amount, 2) }}</span> {{ \App\Models\Setting::get('currency', 'ر.س') }}
                                </div>
                                <div>
                                    <strong>المبلغ المتبقي:</strong>
                                    <span id="remaining_amount">{{ number_format($invoice->remaining_amount, 2) }}</span> {{ \App\Models\Setting::get('currency', 'ر.س') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> حفظ التغييرات
                        </button>
                        <a href="{{ route('invoices.show', $invoice->id) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i> إلغاء
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const paidAmountInput = document.getElementById('paid_amount');
        const paymentStatusSelect = document.getElementById('payment_status');
        const totalAmountSpan = document.getElementById('total_amount');
        const displayPaidAmountSpan = document.getElementById('display_paid_amount');
        const remainingAmountSpan = document.getElementById('remaining_amount');

        const totalAmount = parseFloat({{ $invoice->total_amount }});

        // تحديث المبالغ عند تغيير المبلغ المدفوع
        paidAmountInput.addEventListener('input', updateAmounts);

        // تحديث حالة الدفع تلقائيًا بناءً على المبلغ المدفوع
        paidAmountInput.addEventListener('change', updatePaymentStatus);

        function updateAmounts() {
            const paidAmount = parseFloat(paidAmountInput.value) || 0;
            const remainingAmount = totalAmount - paidAmount;

            displayPaidAmountSpan.textContent = paidAmount.toFixed(2);
            remainingAmountSpan.textContent = remainingAmount.toFixed(2);
        }

        function updatePaymentStatus() {
            const paidAmount = parseFloat(paidAmountInput.value) || 0;

            if (paidAmount <= 0) {
                paymentStatusSelect.value = 'unpaid';
            } else if (paidAmount < totalAmount) {
                paymentStatusSelect.value = 'partial';
            } else {
                paymentStatusSelect.value = 'paid';
            }
        }
    });
</script>
@endsection
