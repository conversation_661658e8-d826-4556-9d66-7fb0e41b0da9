<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Table;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class OrderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Order::with(['user', 'table']);

        // البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('id', 'like', "%{$search}%")
                  ->orWhere('notes', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('table', function($tableQuery) use ($search) {
                      $tableQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // فلترة حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // فلترة حسب نوع الطلب
        if ($request->filled('order_type')) {
            $query->where('order_type', $request->order_type);
        }

        // فلترة حسب النادل
        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // فلترة حسب التاريخ
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // فلترة حسب المبلغ
        if ($request->filled('amount_from')) {
            $query->where('total_amount', '>=', $request->amount_from);
        }

        if ($request->filled('amount_to')) {
            $query->where('total_amount', '<=', $request->amount_to);
        }

        // ترتيب النتائج
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        if (in_array($sortBy, ['id', 'created_at', 'total_amount', 'status'])) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->latest();
        }

        // عدد العناصر في الصفحة
        $perPage = $request->get('per_page', 25);
        if (!in_array($perPage, [15, 25, 30, 50, 100])) {
            $perPage = 25;
        }

        $orders = $query->paginate($perPage);

        // بيانات إضافية للفلاتر
        $users = User::whereHas('role', function ($query) {
            $query->whereIn('name', ['waiter', 'admin']);
        })->get();

        $statuses = [
            'pending' => 'في الانتظار',
            'preparing' => 'قيد التحضير',
            'ready' => 'جاهز',
            'delivered' => 'تم التسليم',
            'completed' => 'مكتمل',
            'cancelled' => 'ملغي'
        ];

        $orderTypes = [
            'dine_in' => 'طعام داخلي',
            'takeaway' => 'طلب خارجي',
            'delivery' => 'توصيل'
        ];

        return view('orders.index', compact('orders', 'users', 'statuses', 'orderTypes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $tables = Table::where('status', 'available')->orWhere('status', 'reserved')->get();
        $products = Product::where('is_available', true)->with('category')->get();
        $cashiers = User::whereHas('role', function ($query) {
            $query->where('name', 'cashier');
        })->with('role')->get();

        // تحقق من وجود الكاشيرين
        if ($cashiers->isEmpty()) {
            return redirect()->route('orders.index')
                ->with('error', 'لا يوجد كاشيرين في النظام. يرجى إضافة كاشيرين أولاً من إدارة المستخدمين.');
        }

        return view('orders.create', compact('tables', 'products', 'cashiers'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'table_id' => 'required|exists:tables,id',
            'user_id' => 'required|exists:users,id',
            'products' => 'required|array',
            'products.*.id' => 'required|exists:products,id',
            'products.*.quantity' => 'required|integer|min:1',
            'products.*.notes' => 'nullable|string',
            'notes' => 'nullable|string',
        ]);

        DB::beginTransaction();

        try {
            // إنشاء الطلب
            $order = Order::create([
                'user_id' => $validated['user_id'],
                'table_id' => $validated['table_id'],
                'status' => 'pending',
                'notes' => $validated['notes'] ?? null,
                'total_amount' => 0, // سيتم حسابه لاحقًا
            ]);

            // تحديث حالة الطاولة
            $table = Table::find($validated['table_id']);
            $table->status = 'occupied';
            $table->save();

            // إضافة عناصر الطلب
            $totalAmount = 0;

            foreach ($validated['products'] as $productData) {
                $product = Product::find($productData['id']);
                $quantity = $productData['quantity'];
                $unitPrice = $product->price;
                $subtotal = $quantity * $unitPrice;
                $totalAmount += $subtotal;

                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'quantity' => $quantity,
                    'unit_price' => $unitPrice,
                    'subtotal' => $subtotal,
                    'notes' => $productData['notes'] ?? null,
                ]);
            }

            // حساب الضريبة والمجموع النهائي
            $taxRate = \App\Models\Setting::get('tax_rate', 15) / 100; // قراءة معدل الضريبة من الإعدادات
            $taxAmount = $totalAmount * $taxRate;
            $finalAmount = $totalAmount + $taxAmount;

            // تحديث إجمالي الطلب (يشمل الضريبة)
            $order->total_amount = $finalAmount;
            $order->save();

            DB::commit();

            return redirect()->route('orders.show', $order->id)
                ->with('success', 'تم إنشاء الطلب بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء إنشاء الطلب: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $order = Order::with(['user', 'table', 'orderItems.product'])->findOrFail($id);
        return view('orders.show', compact('order'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $order = Order::with(['orderItems.product'])->findOrFail($id);
        $tables = Table::all();
        $products = Product::where('is_available', true)->with('category')->get();
        $cashiers = User::whereHas('role', function ($query) {
            $query->where('name', 'cashier');
        })->get();

        return view('orders.edit', compact('order', 'tables', 'products', 'cashiers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $order = Order::findOrFail($id);

        // لا يمكن تعديل الطلبات المكتملة أو الملغاة
        if (in_array($order->status, ['completed', 'cancelled'])) {
            return redirect()->route('orders.show', $order->id)
                ->with('error', 'لا يمكن تعديل الطلبات المكتملة أو الملغاة');
        }

        $validated = $request->validate([
            'status' => 'required|in:pending,preparing,ready,delivered,completed,cancelled',
            'notes' => 'nullable|string',
        ]);

        DB::beginTransaction();

        try {
            // تحديث حالة الطلب
            $oldStatus = $order->status;
            $order->status = $validated['status'];
            $order->notes = $validated['notes'];
            $order->save();

            // إذا تم إلغاء الطلب، قم بتحرير الطاولة (فقط للطلبات الداخلية)
            if ($validated['status'] === 'cancelled' && $oldStatus !== 'cancelled' && $order->order_type === 'dine_in' && $order->table) {
                $table = $order->table;
                $table->status = 'available';
                $table->save();
            }

            // إذا تم إكمال الطلب، قم بتحرير الطاولة (فقط للطلبات الداخلية)
            if ($validated['status'] === 'completed' && $oldStatus !== 'completed' && $order->order_type === 'dine_in' && $order->table) {
                $table = $order->table;
                $table->status = 'available';
                $table->save();
            }

            DB::commit();

            return redirect()->route('orders.show', $order->id)
                ->with('success', 'تم تحديث الطلب بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء تحديث الطلب: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $order = Order::findOrFail($id);

        // لا يمكن حذف الطلبات إلا إذا كانت ملغاة
        if ($order->status !== 'cancelled') {
            return redirect()->route('orders.index')
                ->with('error', 'لا يمكن حذف الطلبات إلا إذا كانت ملغاة. قم بإلغاء الطلب أولاً.');
        }

        DB::beginTransaction();

        try {
            // حذف عناصر الطلب
            $order->orderItems()->delete();

            // حذف الطلب
            $order->delete();

            DB::commit();

            return redirect()->route('orders.index')
                ->with('success', 'تم حذف الطلب بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء حذف الطلب: ' . $e->getMessage());
        }
    }

    /**
     * عرض عناصر الطلب
     */
    public function items(string $id)
    {
        $order = Order::with(['orderItems.product'])->findOrFail($id);
        return view('orders.items', compact('order'));
    }
}
