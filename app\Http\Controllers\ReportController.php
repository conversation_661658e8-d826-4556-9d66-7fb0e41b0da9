<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Invoice;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Table;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Pagination\LengthAwarePaginator;

class ReportController extends Controller
{
    /**
     * عرض تقرير المبيعات
     */
    public function sales(Request $request)
    {
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->format('Y-m-d'));

        // تحويل التواريخ إلى كائنات Carbon
        $startDateCarbon = Carbon::parse($startDate)->startOfDay();
        $endDateCarbon = Carbon::parse($endDate)->endOfDay();

        // الحصول على الفواتير في الفترة المحددة مع pagination
        $invoices = Invoice::whereBetween('created_at', [$startDateCarbon, $endDateCarbon])
            ->with(['order.orderItems.product', 'user'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // الحصول على جميع الفواتير للإحصائيات (بدون pagination)
        $allInvoices = Invoice::whereBetween('created_at', [$startDateCarbon, $endDateCarbon])
            ->with(['order.orderItems.product', 'user'])
            ->get();

        // إجمالي المبيعات (استخدام جميع الفواتير للإحصائيات)
        $totalSales = $allInvoices->sum('total_amount');
        $totalPaid = $allInvoices->sum('paid_amount');
        $totalRemaining = $allInvoices->sum('remaining_amount');

        // عدد الفواتير حسب حالة الدفع
        $paidInvoices = $allInvoices->where('payment_status', 'paid')->count();
        $partialInvoices = $allInvoices->where('payment_status', 'partial')->count();
        $unpaidInvoices = $allInvoices->where('payment_status', 'unpaid')->count();

        // المبيعات حسب طريقة الدفع
        $salesByPaymentMethod = [
            'cash' => $allInvoices->where('payment_method', 'cash')->sum('paid_amount'),
            'credit_card' => $allInvoices->where('payment_method', 'credit_card')->sum('paid_amount'),
            'debit_card' => $allInvoices->where('payment_method', 'debit_card')->sum('paid_amount'),
        ];

        // المبيعات اليومية
        $dailySales = $allInvoices->groupBy(function ($invoice) {
            return $invoice->created_at->format('Y-m-d');
        })->map(function ($dayInvoices) {
            return [
                'count' => $dayInvoices->count(),
                'total' => $dayInvoices->sum('total_amount'),
                'paid' => $dayInvoices->sum('paid_amount'),
            ];
        });

        // المنتجات الأكثر مبيعًا
        $topProducts = OrderItem::whereHas('order', function ($query) use ($startDateCarbon, $endDateCarbon) {
                $query->whereBetween('created_at', [$startDateCarbon, $endDateCarbon]);
            })
            ->select('product_id', DB::raw('SUM(quantity) as total_quantity'), DB::raw('SUM(subtotal) as total_sales'))
            ->with('product')
            ->groupBy('product_id')
            ->orderByDesc('total_quantity')
            ->limit(10)
            ->get();

        return view('reports.sales', compact(
            'startDate',
            'endDate',
            'invoices',
            'totalSales',
            'totalPaid',
            'totalRemaining',
            'paidInvoices',
            'partialInvoices',
            'unpaidInvoices',
            'salesByPaymentMethod',
            'dailySales',
            'topProducts'
        ));
    }

    /**
     * عرض تقرير الطلبات
     */
    public function orders(Request $request)
    {
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->format('Y-m-d'));

        // تحويل التواريخ إلى كائنات Carbon
        $startDateCarbon = Carbon::parse($startDate)->startOfDay();
        $endDateCarbon = Carbon::parse($endDate)->endOfDay();

        // الحصول على الطلبات في الفترة المحددة مع pagination
        $orders = Order::whereBetween('created_at', [$startDateCarbon, $endDateCarbon])
            ->with(['user', 'table', 'orderItems.product'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // الحصول على جميع الطلبات للإحصائيات (بدون pagination)
        $allOrders = Order::whereBetween('created_at', [$startDateCarbon, $endDateCarbon])
            ->with(['user', 'table', 'orderItems.product'])
            ->get();

        // إجمالي الطلبات (استخدام جميع الطلبات للإحصائيات)
        $totalOrders = $allOrders->count();
        $totalOrdersAmount = $allOrders->sum('total_amount');

        // عدد الطلبات حسب الحالة
        $pendingOrders = $allOrders->where('status', 'pending')->count();
        $preparingOrders = $allOrders->where('status', 'preparing')->count();
        $readyOrders = $allOrders->where('status', 'ready')->count();
        $deliveredOrders = $allOrders->where('status', 'delivered')->count();
        $completedOrders = $allOrders->where('status', 'completed')->count();
        $cancelledOrders = $allOrders->where('status', 'cancelled')->count();

        // الطلبات اليومية
        $dailyOrders = $allOrders->groupBy(function ($order) {
            return $order->created_at->format('Y-m-d');
        })->map(function ($dayOrders) {
            return [
                'count' => $dayOrders->count(),
                'total' => $dayOrders->sum('total_amount'),
            ];
        });

        // الطلبات حسب الطاولة (للإحصائيات)
        $allOrdersByTable = $allOrders->groupBy('table_id')->map(function ($tableOrders, $tableId) {
            $tableName = $tableOrders->first()->table ? $tableOrders->first()->table->name : 'غير محدد';
            return [
                'table_id' => $tableId,
                'table_name' => $tableName,
                'count' => $tableOrders->count(),
                'total' => $tableOrders->sum('total_amount'),
            ];
        });

        // الطلبات حسب الطاولة مع pagination
        $ordersByTableCollection = collect($allOrdersByTable->values());
        $currentPage = request()->get('table_page', 1);
        $perPage = 10;
        $ordersByTable = new LengthAwarePaginator(
            $ordersByTableCollection->forPage($currentPage, $perPage),
            $ordersByTableCollection->count(),
            $perPage,
            $currentPage,
            [
                'path' => request()->url(),
                'pageName' => 'table_page',
            ]
        );
        $ordersByTable->appends(request()->except('table_page'));

        // الطلبات حسب النادل
        $ordersByWaiter = $allOrders->groupBy('user_id')->map(function ($waiterOrders, $userId) {
            $waiterName = $waiterOrders->first()->user ? $waiterOrders->first()->user->name : 'غير محدد';
            return [
                'waiter_name' => $waiterName,
                'count' => $waiterOrders->count(),
                'total' => $waiterOrders->sum('total_amount'),
            ];
        });

        return view('reports.orders', compact(
            'startDate',
            'endDate',
            'orders',
            'totalOrders',
            'totalOrdersAmount',
            'pendingOrders',
            'preparingOrders',
            'readyOrders',
            'deliveredOrders',
            'completedOrders',
            'cancelledOrders',
            'dailyOrders',
            'ordersByTable',
            'ordersByWaiter'
        ));
    }

    /**
     * عرض تقرير المنتجات
     */
    public function products(Request $request)
    {
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->format('Y-m-d'));

        // تحويل التواريخ إلى كائنات Carbon
        $startDateCarbon = Carbon::parse($startDate)->startOfDay();
        $endDateCarbon = Carbon::parse($endDate)->endOfDay();

        // الحصول على المنتجات
        $products = Product::with('category')->get();

        // إجمالي المنتجات
        $totalProducts = $products->count();
        $availableProducts = $products->where('is_available', true)->count();
        $unavailableProducts = $products->where('is_available', false)->count();

        // المنتجات حسب التصنيف
        $productsByCategory = $products->groupBy('category_id')->map(function ($categoryProducts, $categoryId) {
            $categoryName = $categoryProducts->first()->category ? $categoryProducts->first()->category->name : 'غير مصنف';
            return [
                'category_name' => $categoryName,
                'count' => $categoryProducts->count(),
                'available' => $categoryProducts->where('is_available', true)->count(),
            ];
        });

        // المنتجات الأكثر مبيعًا
        $topProducts = OrderItem::whereHas('order', function ($query) use ($startDateCarbon, $endDateCarbon) {
                $query->whereBetween('created_at', [$startDateCarbon, $endDateCarbon]);
            })
            ->select('product_id', DB::raw('SUM(quantity) as total_quantity'), DB::raw('SUM(subtotal) as total_sales'))
            ->with('product')
            ->groupBy('product_id')
            ->orderByDesc('total_quantity')
            ->limit(10)
            ->get();

        // المنتجات الأقل مبيعًا
        $leastSoldProducts = OrderItem::whereHas('order', function ($query) use ($startDateCarbon, $endDateCarbon) {
                $query->whereBetween('created_at', [$startDateCarbon, $endDateCarbon]);
            })
            ->select('product_id', DB::raw('SUM(quantity) as total_quantity'), DB::raw('SUM(subtotal) as total_sales'))
            ->with('product')
            ->groupBy('product_id')
            ->orderBy('total_quantity')
            ->limit(10)
            ->get();

        // المنتجات التي لم تباع
        $productIds = OrderItem::whereHas('order', function ($query) use ($startDateCarbon, $endDateCarbon) {
                $query->whereBetween('created_at', [$startDateCarbon, $endDateCarbon]);
            })
            ->pluck('product_id')
            ->unique();

        $unsoldProducts = Product::whereNotIn('id', $productIds)->get();

        return view('reports.products', compact(
            'startDate',
            'endDate',
            'products',
            'totalProducts',
            'availableProducts',
            'unavailableProducts',
            'productsByCategory',
            'topProducts',
            'leastSoldProducts',
            'unsoldProducts'
        ));
    }
}