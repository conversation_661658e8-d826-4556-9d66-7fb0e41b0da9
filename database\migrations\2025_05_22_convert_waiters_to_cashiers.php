<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // الحصول على معرف دور الكاشير
        $cashierRole = DB::table('roles')->where('name', 'cashier')->first();
        
        if ($cashierRole) {
            // تحويل جميع النادلين إلى كاشيرين
            DB::table('users')
                ->whereHas('role', function($query) {
                    $query->where('name', 'waiter');
                })
                ->update(['role_id' => $cashierRole->id]);
                
            // أو استخدام استعلام مباشر
            $waiterRole = DB::table('roles')->where('name', 'waiter')->first();
            if ($waiterRole) {
                DB::table('users')
                    ->where('role_id', $waiterRole->id)
                    ->update(['role_id' => $cashierRole->id]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // إنشاء دور النادل مرة أخرى إذا لم يكن موجوداً
        $waiterRole = DB::table('roles')->where('name', 'waiter')->first();
        if (!$waiterRole) {
            $waiterRoleId = DB::table('roles')->insertGetId([
                'name' => 'waiter',
                'description' => 'نادل - لديه صلاحيات إدارة الطلبات والطاولات',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } else {
            $waiterRoleId = $waiterRole->id;
        }

        // يمكن إرجاع بعض المستخدمين إلى دور النادل إذا لزم الأمر
        // هذا اختياري حسب الحاجة
    }
};
