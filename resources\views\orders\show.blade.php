@extends('layouts.app')

@section('title', 'عرض الطلب')

@section('content')
<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2>عرض الطلب رقم: {{ $order->id }}</h2>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('orders.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
            </a>
            @if(!in_array($order->status, ['completed', 'cancelled']))
            <a href="{{ route('orders.edit', $order->id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
            @endif
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">معلومات الطلب</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 40%">رقم الطلب</th>
                            <td>{{ $order->id }}</td>
                        </tr>
                        <tr>
                            <th>الطاولة</th>
                            <td>
                                @if($order->table)
                                    <a href="{{ route('tables.show', $order->table->id) }}">{{ $order->table->name }}</a>
                                @else
                                    غير محدد
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>النادل</th>
                            <td>
                                @if($order->user)
                                    <a href="{{ route('users.show', $order->user->id) }}">{{ $order->user->name }}</a>
                                @else
                                    غير محدد
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>الحالة</th>
                            <td>
                                @if($order->status == 'pending')
                                    <span class="badge bg-secondary">قيد الانتظار</span>
                                @elseif($order->status == 'preparing')
                                    <span class="badge bg-primary">قيد التحضير</span>
                                @elseif($order->status == 'ready')
                                    <span class="badge bg-info">جاهز</span>
                                @elseif($order->status == 'delivered')
                                    <span class="badge bg-success">تم التوصيل</span>
                                @elseif($order->status == 'completed')
                                    <span class="badge bg-success">مكتمل</span>
                                @elseif($order->status == 'cancelled')
                                    <span class="badge bg-danger">ملغي</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <th>المبلغ الإجمالي</th>
                            <td class="fw-bold">{{ number_format($order->total_amount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                        </tr>
                        <tr>
                            <th>تاريخ الإنشاء</th>
                            <td>{{ $order->created_at->format('Y-m-d H:i') }}</td>
                        </tr>
                        <tr>
                            <th>آخر تحديث</th>
                            <td>{{ $order->updated_at->format('Y-m-d H:i') }}</td>
                        </tr>
                    </table>

                    @if($order->notes)
                    <div class="alert alert-info mt-3">
                        <h6 class="alert-heading">ملاحظات:</h6>
                        <p class="mb-0">{{ $order->notes }}</p>
                    </div>
                    @endif
                </div>
            </div>

            @if(!in_array($order->status, ['completed', 'cancelled']))
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">تحديث الحالة</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('orders.update', $order->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <input type="hidden" name="notes" value="{{ $order->notes }}">

                        <div class="mb-3">
                            <label for="status" class="form-label">الحالة الجديدة</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="pending" {{ $order->status == 'pending' ? 'selected' : '' }}>قيد الانتظار</option>
                                <option value="preparing" {{ $order->status == 'preparing' ? 'selected' : '' }}>قيد التحضير</option>
                                <option value="ready" {{ $order->status == 'ready' ? 'selected' : '' }}>جاهز</option>
                                <option value="delivered" {{ $order->status == 'delivered' ? 'selected' : '' }}>تم التوصيل</option>
                                <option value="completed" {{ $order->status == 'completed' ? 'selected' : '' }}>مكتمل</option>
                                <option value="cancelled" {{ $order->status == 'cancelled' ? 'selected' : '' }}>ملغي</option>
                            </select>
                        </div>

                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-save me-1"></i> تحديث الحالة
                        </button>
                    </form>
                </div>
            </div>
            @endif
        </div>

        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">عناصر الطلب</h5>
                </div>
                <div class="card-body">
                    @if($order->orderItems->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>#</th>
                                        <th>المنتج</th>
                                        <th>السعر</th>
                                        <th>الكمية</th>
                                        <th>المجموع</th>
                                        <th>ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($order->orderItems as $index => $item)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>
                                            @if($item->product)
                                                <a href="{{ route('products.show', $item->product->id) }}">{{ $item->product->name }}</a>
                                            @else
                                                منتج محذوف
                                            @endif
                                        </td>
                                        <td>{{ number_format($item->unit_price, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                        <td>{{ $item->quantity }}</td>
                                        <td>{{ number_format($item->subtotal, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                        <td>{{ $item->notes ?? '-' }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th colspan="4" class="text-end">المجموع الكلي:</th>
                                        <th>{{ number_format($order->total_amount, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info">
                            لا توجد عناصر في هذا الطلب
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
