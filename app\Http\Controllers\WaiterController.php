<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Table;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class WaiterController extends Controller
{
    /**
     * إنشاء مثيل جديد من المتحكم
     */
    public function __construct()
    {
        // التحقق من المصادقة فقط
        $this->middleware('auth');
    }
    /**
     * عرض واجهة النادل
     */
    public function index()
    {
        $tables = Table::all();
        $categories = Category::with(['products' => function($query) {
            $query->where('is_available', true);
        }])->get();
        $products = Product::where('is_available', true)->with('category')->get();

        // الطلبات النشطة - للمدير والكاشير: جميع الطلبات
        if (Auth::user()->hasRole('admin') || Auth::user()->hasRole('manager') || Auth::user()->hasRole('cashier')) {
            // المدير والكاشير يرون جميع الطلبات النشطة
            $activeOrders = Order::whereIn('status', ['pending', 'preparing', 'ready', 'delivered', 'suspended'])
                ->with(['table', 'orderItems.product', 'user'])
                ->orderBy('created_at', 'desc')
                ->get();
        } else {
            // المستخدمون الآخرون يرون طلباتهم فقط
            $activeOrders = Order::where('user_id', Auth::id())
                ->whereIn('status', ['pending', 'preparing', 'ready', 'delivered', 'suspended'])
                ->with(['table', 'orderItems.product'])
                ->orderBy('created_at', 'desc')
                ->get();
        }

        return view('waiter.index', compact('tables', 'categories', 'products', 'activeOrders'));
    }

    /**
     * إنشاء طلب جديد
     */
    public function createOrder(Request $request)
    {
        // تحديد ما إذا كان الطلب مسودة
        $isDraft = $request->input('status') === 'draft';

        $validated = $request->validate([
            'order_type' => 'required|in:dine_in,takeaway',
            'table_id' => $isDraft ? 'nullable|exists:tables,id' : 'required_if:order_type,dine_in|nullable|exists:tables,id',
            'customer_name' => 'nullable|string|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'car_number' => 'nullable|string|max:20',
            'payment_method' => 'nullable|string|in:cash,card',
            'status' => 'nullable|string|in:pending,draft,suspended',
            'products' => 'required|array',
            'products.*.id' => 'required|exists:products,id',
            'products.*.quantity' => 'required|integer|min:1',
            'products.*.notes' => 'nullable|string',
            'notes' => 'nullable|string',
        ]);

        DB::beginTransaction();

        try {
            // تحديد حالة الطلب
            $orderStatus = $validated['status'] ?? 'pending';

            // إنشاء الطلب
            $orderData = [
                'user_id' => Auth::id(),
                'order_type' => $validated['order_type'],
                'status' => $orderStatus,
                'notes' => $validated['notes'] ?? null,
                'payment_method' => $validated['payment_method'] ?? 'cash',
                'customer_name' => $validated['customer_name'] ?? null,
                'total_amount' => 0, // سيتم حسابه لاحقًا
            ];

            // إضافة بيانات حسب نوع الطلب
            if ($validated['order_type'] === 'dine_in') {
                $orderData['table_id'] = $validated['table_id'];

                // تحديث حالة الطاولة فقط إذا لم يكن الطلب مسودة
                if ($orderStatus !== 'draft' && isset($validated['table_id'])) {
                    $table = Table::find($validated['table_id']);
                    if ($table) {
                        $table->status = 'occupied';
                        $table->save();
                    }
                }
            } else {
                $orderData['customer_phone'] = $validated['customer_phone'] ?? null;
                $orderData['car_number'] = $validated['car_number'] ?? null;
            }

            $order = Order::create($orderData);

            // إضافة عناصر الطلب
            $totalAmount = 0;

            foreach ($validated['products'] as $productData) {
                $product = Product::find($productData['id']);
                $quantity = $productData['quantity'];
                $unitPrice = $product->price;
                $subtotal = $quantity * $unitPrice;
                $totalAmount += $subtotal;

                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'quantity' => $quantity,
                    'unit_price' => $unitPrice,
                    'subtotal' => $subtotal,
                    'notes' => $productData['notes'] ?? null,
                ]);
            }

            // تحديث إجمالي الطلب
            $order->total_amount = $totalAmount;
            $order->save();

            // معالجة الطلب حسب حالته ونوعه (تجاهل المسودات)
            if ($orderStatus !== 'draft') {
                // إنشاء فاتورة تلقائيًا للطلبات الخارجية فقط
                if ($order->order_type === 'takeaway') {
                    // حساب الضريبة والمبلغ النهائي
                    $taxRate = \App\Models\Setting::get('tax_rate', 15) / 100; // قراءة معدل الضريبة من الإعدادات
                    $taxAmount = $totalAmount * $taxRate;
                    $finalAmount = $totalAmount + $taxAmount;

                    // إنشاء الفاتورة
                    $invoice = new \App\Models\Invoice([
                        'order_id' => $order->id,
                        'user_id' => Auth::id(),
                        'invoice_number' => 'INV-' . date('Ymd') . '-' . rand(1000, 9999),
                        'total_amount' => $totalAmount,
                        'tax_amount' => $taxAmount,
                        'discount_amount' => 0,
                        'final_amount' => $finalAmount,
                        'paid_amount' => $finalAmount, // مدفوع بالكامل
                        'remaining_amount' => 0,
                        'payment_method' => $validated['payment_method'] ?? 'cash',
                        'payment_status' => 'paid', // مدفوع
                        'notes' => $validated['notes'] ?? null,
                    ]);
                    $invoice->save();

                    // نترك حالة الطلب كما هي (pending) ليظهر في صفحة الشيف
                    // سيتم تغيير الحالة إلى completed بعد أن يقوم الشيف بتحضير الطلب
                } else if ($order->order_type === 'dine_in') {
                    // تحديث حالة الطلب إلى قيد التنفيذ للطلبات داخل المطعم
                    $order->status = 'in_progress';
                    $order->save();

                    // تحديث حالة الطاولة إلى مشغولة
                    if ($order->table) {
                        $order->table->status = 'occupied';
                        $order->table->save();
                    }
                }
            }

            DB::commit();

            // إرجاع الاستجابة حسب نوع وحالة الطلب
            if ($orderStatus === 'draft') {
                return response()->json([
                    'success' => true,
                    'message' => 'تم حفظ المسودة بنجاح',
                    'order' => $order->load(['table', 'orderItems.product']),
                    'order_type' => $order->order_type,
                    'status' => 'draft'
                ]);
            } else if ($order->order_type === 'takeaway' && isset($invoice)) {
                return response()->json([
                    'success' => true,
                    'message' => 'تم إنشاء الطلب بنجاح',
                    'order' => $order->load(['table', 'orderItems.product']),
                    'order_type' => 'takeaway',
                    'invoice_id' => $invoice->id,
                    'print_urls' => [
                        '85mm' => route('invoices.print.85mm', $invoice->id)
                    ]
                ]);
            } else if ($order->order_type === 'dine_in') {
                return response()->json([
                    'success' => true,
                    'message' => 'تم حفظ الطلب بنجاح. سيتم الدفع عند انتهاء العميل من الطعام.',
                    'order' => $order->load(['table', 'orderItems.product']),
                    'order_type' => 'dine_in'
                ]);
            } else {
                return response()->json([
                    'success' => true,
                    'message' => 'تم إنشاء الطلب بنجاح',
                    'order' => $order->load(['table', 'orderItems.product']),
                ]);
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء الطلب: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * تحديث حالة الطلب
     */
    public function updateOrderStatus(Request $request, $id)
    {
        $order = Order::findOrFail($id);

        $validated = $request->validate([
            'status' => 'required|in:pending,preparing,ready,delivered,completed,cancelled,suspended,draft',
        ]);

        DB::beginTransaction();

        try {
            // تحديث حالة الطلب
            $oldStatus = $order->status;
            $order->status = $validated['status'];
            $order->save();

            // معالجة تحديث حالة الطاولة حسب التغيير
            if ($order->order_type === 'dine_in' && $order->table) {
                $table = $order->table;

                if (in_array($validated['status'], ['completed', 'cancelled'])) {
                    // إذا تم إلغاء الطلب أو إكماله، حرر الطاولة
                    $table->status = 'available';
                } elseif ($validated['status'] === 'suspended' && $oldStatus === 'draft') {
                    // إذا تم تحويل المسودة إلى معلقة، اشغل الطاولة
                    $table->status = 'occupied';
                } elseif ($validated['status'] === 'pending' && in_array($oldStatus, ['suspended', 'draft'])) {
                    // إذا تم تحويل المعلق أو المسودة إلى قيد الانتظار، اشغل الطاولة
                    $table->status = 'occupied';
                }

                $table->save();
            }

            // معالجة خاصة للمسودات المحفوظة كطلبات خارجية
            if ($validated['status'] === 'suspended' && $oldStatus === 'draft' && $order->order_type === 'takeaway') {
                // البحث عن معلومات الطاولة في الملاحظات
                if (strpos($order->notes, 'رقم الطاولة:') !== false) {
                    preg_match('/رقم الطاولة:\s*(\d+)/', $order->notes, $matches);
                    if (isset($matches[1])) {
                        $tableId = $matches[1];
                        $table = \App\Models\Table::find($tableId);
                        if ($table) {
                            // تحويل الطلب إلى داخلي وربطه بالطاولة
                            $order->order_type = 'dine_in';
                            $order->table_id = $table->id;
                            $order->save();

                            // شغل الطاولة
                            $table->status = 'occupied';
                            $table->save();
                        }
                    }
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث حالة الطلب بنجاح',
                'order' => $order->load(['table', 'orderItems.product']),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('❌ خطأ في updateOrderStatus', [
                'order_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث حالة الطلب: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * إضافة منتج إلى طلب موجود
     */
    public function addProductToOrder(Request $request, $id)
    {
        $order = Order::findOrFail($id);

        // التحقق من أن الطلب نشط (قيد الانتظار أو قيد التنفيذ أو معلق أو مسودة)
        if (!in_array($order->status, ['pending', 'in_progress', 'suspended', 'draft'])) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن إضافة منتجات إلى طلب غير نشط',
            ], 400);
        }

        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string',
        ]);

        DB::beginTransaction();

        try {
            $product = Product::findOrFail($validated['product_id']);
            $unitPrice = $product->price;
            $subtotal = $validated['quantity'] * $unitPrice;

            // التحقق مما إذا كان المنتج موجودًا بالفعل في الطلب
            $existingItem = $order->orderItems()->where('product_id', $validated['product_id'])->first();

            if ($existingItem) {
                // تحديث العنصر الموجود
                $existingItem->quantity += $validated['quantity'];
                $existingItem->subtotal = $existingItem->quantity * $existingItem->unit_price;
                $existingItem->notes = $validated['notes'] ?? $existingItem->notes;
                $existingItem->save();
            } else {
                // إنشاء عنصر جديد
                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $validated['product_id'],
                    'quantity' => $validated['quantity'],
                    'unit_price' => $unitPrice,
                    'subtotal' => $subtotal,
                    'notes' => $validated['notes'] ?? null,
                ]);
            }

            // تحديث إجمالي الطلب
            $order->total_amount = $order->orderItems()->sum('subtotal');
            $order->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تمت إضافة المنتج إلى الطلب بنجاح',
                'order' => $order->load(['table', 'orderItems.product']),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إضافة المنتج إلى الطلب: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * حذف منتج من طلب
     */
    public function removeProductFromOrder(Request $request, $id)
    {
        $order = Order::findOrFail($id);

        // التحقق من أن الطلب لم يكتمل أو يلغى
        if (in_array($order->status, ['completed', 'cancelled'])) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف منتجات من طلب مكتمل أو ملغي',
            ], 400);
        }

        $validated = $request->validate([
            'order_item_id' => 'required|exists:order_items,id',
        ]);

        DB::beginTransaction();

        try {
            $orderItem = OrderItem::where('id', $validated['order_item_id'])
                ->where('order_id', $order->id)
                ->firstOrFail();

            // حذف عنصر الطلب
            $orderItem->delete();

            // تحديث إجمالي الطلب
            $order->total_amount = $order->orderItems()->sum('subtotal');
            $order->save();

            // إذا لم يعد هناك عناصر في الطلب، قم بإلغاء الطلب
            if ($order->orderItems()->count() === 0) {
                $order->status = 'cancelled';
                $order->save();

                // تحرير الطاولة (فقط للطلبات الداخلية)
                if ($order->order_type === 'dine_in' && $order->table) {
                    $table = $order->table;
                    $table->status = 'available';
                    $table->save();
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف المنتج من الطلب بنجاح',
                'order' => $order->load(['table', 'orderItems.product']),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف المنتج من الطلب: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * الحصول على الطلبات النشطة (للطاولات والطلبات المعلقة)
     */
    public function getActiveOrders()
    {
        // الحصول على الطلبات النشطة:
        // 1. الطلبات الداخلية (قيد الانتظار أو قيد التنفيذ أو معلقة أو مسودات)
        // 2. الطلبات الخارجية المعلقة أو المسودات فقط
        $orders = Order::with(['table', 'user'])
            ->where(function ($query) {
                // الطلبات الداخلية النشطة
                $query->where('order_type', 'dine_in')
                      ->whereIn('status', ['pending', 'in_progress', 'suspended', 'draft']);
            })
            ->orWhere(function ($query) {
                // الطلبات الخارجية المعلقة أو المسودات فقط
                $query->where('order_type', 'takeaway')
                      ->whereIn('status', ['suspended', 'draft']);
            })
            ->orderBy('created_at', 'desc')
            ->get();

        // التأكد من أن total_amount هو رقم
        $orders->transform(function ($order) {
            $order->total_amount = (float) $order->total_amount;
            return $order;
        });

        return response()->json([
            'success' => true,
            'orders' => $orders
        ]);
    }

    /**
     * الحصول على تفاصيل طلب محدد
     */
    public function getOrder($id)
    {
        $order = Order::with(['table', 'user', 'orderItems.product'])
            ->findOrFail($id);

        // التأكد من أن total_amount هو رقم
        $order->total_amount = (float) $order->total_amount;

        // التأكد من أن unit_price و subtotal في عناصر الطلب هي أرقام
        if ($order->orderItems) {
            foreach ($order->orderItems as $item) {
                $item->unit_price = (float) $item->unit_price;
                $item->subtotal = (float) $item->subtotal;
                $item->quantity = (int) $item->quantity;
            }
        }

        return response()->json([
            'success' => true,
            'order' => $order
        ]);
    }

    /**
     * دفع طلب نشط
     */
    public function payOrder($id, Request $request)
    {
        try {
            DB::beginTransaction();

            // الحصول على الطلب
            $order = Order::with(['table', 'orderItems.product'])
                ->findOrFail($id);

            // التحقق من أن الطلب نشط
            if (!in_array($order->status, ['pending', 'in_progress', 'suspended', 'draft'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن دفع طلب غير نشط'
                ]);
            }

            // حساب الضريبة والمبلغ النهائي
            $totalAmount = $order->total_amount;
            $taxRate = \App\Models\Setting::get('tax_rate', 15) / 100; // قراءة معدل الضريبة من الإعدادات
            $taxAmount = $totalAmount * $taxRate;
            $finalAmount = $totalAmount + $taxAmount;

            // إنشاء الفاتورة
            $invoice = new \App\Models\Invoice([
                'order_id' => $order->id,
                'user_id' => Auth::id(),
                'invoice_number' => 'INV-' . date('Ymd') . '-' . rand(1000, 9999),
                'total_amount' => $totalAmount,
                'tax_amount' => $taxAmount,
                'discount_amount' => 0,
                'final_amount' => $finalAmount,
                'paid_amount' => $finalAmount, // مدفوع بالكامل
                'remaining_amount' => 0,
                'payment_method' => $request->payment_method ?? 'cash',
                'payment_status' => 'paid', // مدفوع
                'notes' => $order->notes,
            ]);
            $invoice->save();

            // تحديد حالة الطلب بعد الدفع
            if ($order->status === 'suspended' || $order->status === 'draft') {
                // إذا كان الطلب معلق أو مسودة، حوله إلى قيد الانتظار ليذهب للمطبخ
                $order->status = 'pending';
            } else {
                // إذا كان الطلب في حالة أخرى، اكمله مباشرة
                $order->status = 'completed';
            }
            $order->save();

            // تحديث حالة الطاولة حسب حالة الطلب
            if ($order->table) {
                if ($order->status === 'completed') {
                    // إذا اكتمل الطلب، حرر الطاولة
                    $order->table->status = 'available';
                } else {
                    // إذا كان الطلب قيد الانتظار، اشغل الطاولة
                    $order->table->status = 'occupied';
                }
                $order->table->save();
            }

            DB::commit();

            // تحديد رسالة النجاح حسب حالة الطلب
            $message = 'تم دفع الطلب بنجاح';
            if ($order->status === 'pending') {
                $message = 'تم دفع الطلب بنجاح وتم إرساله للمطبخ للتحضير';
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'order' => $order->load(['table', 'orderItems.product']),
                'invoice_id' => $invoice->id,
                'print_urls' => [
                    '85mm' => route('invoices.print.85mm', $invoice->id)
                ]
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء دفع الطلب: ' . $e->getMessage()
            ]);
        }
    }
    /**
     * تعليق طلب
     */
    public function suspendOrder(Request $request)
    {
        $validated = $request->validate([
            'order_type' => 'required|in:dine_in,takeaway',
            'table_id' => 'required_if:order_type,dine_in|nullable|exists:tables,id',
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'car_number' => 'nullable|string|max:20',
            'notes' => 'nullable|string',
            'products' => 'required|array',
            'products.*.id' => 'required|exists:products,id',
            'products.*.quantity' => 'required|integer|min:1',
            'products.*.notes' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            // إنشاء الطلب
            $orderData = [
                'user_id' => Auth::id(),
                'order_type' => $validated['order_type'],
                'status' => 'suspended', // حالة الطلب معلق
                'preparation_status' => 'pending', // حالة التحضير قيد الانتظار
                'notes' => $validated['notes'] ?? null,
                'customer_name' => $validated['customer_name'],
                'customer_phone' => $validated['customer_phone'] ?? null,
                'car_number' => $validated['car_number'] ?? null,
                'total_amount' => 0, // سيتم حسابه لاحقًا
            ];

            // إذا كان الطلب داخل المطعم، قم بإضافة معرف الطاولة
            if ($validated['order_type'] === 'dine_in' && isset($validated['table_id'])) {
                $orderData['table_id'] = $validated['table_id'];

                // تحديث حالة الطاولة إلى مشغولة
                $table = Table::findOrFail($validated['table_id']);
                $table->status = 'occupied';
                $table->save();
            }

            $order = Order::create($orderData);

            // إضافة منتجات الطلب
            $totalAmount = 0;

            foreach ($validated['products'] as $product) {
                $productModel = Product::findOrFail($product['id']);
                $unitPrice = $productModel->price;
                $subtotal = $product['quantity'] * $unitPrice;
                $totalAmount += $subtotal;

                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product['id'],
                    'quantity' => $product['quantity'],
                    'unit_price' => $unitPrice,
                    'subtotal' => $subtotal,
                    'notes' => $product['notes'] ?? null,
                ]);
            }

            // تحديث إجمالي الطلب
            $order->total_amount = $totalAmount;
            $order->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تعليق الطلب بنجاح',
                'order' => $order->load(['table', 'orderItems.product']),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تعليق الطلب: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * إنشاء طلب للمطبخ
     */
    public function createKitchenOrder(Request $request)
    {
        try {
            // الحصول على آخر طلب
            $latestOrder = Order::with(['orderItems.product', 'table', 'user'])
                ->latest()
                ->first();

            if (!$latestOrder) {
                return response()->json([
                    'success' => false,
                    'message' => 'لم يتم العثور على طلبات'
                ]);
            }

            // إنشاء رابط طباعة طلب المطبخ (نستخدم view مؤقت)
            $printUrl = url("waiter/kitchen-order/{$latestOrder->id}");

            return response()->json([
                'success' => true,
                'print_url' => $printUrl,
                'order' => $latestOrder
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء طلب المطبخ: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * طباعة طلب المطبخ
     */
    public function printKitchenOrder($id)
    {
        $order = Order::with(['orderItems.product', 'table', 'user'])->findOrFail($id);
        return view('waiter.kitchen_order_print', compact('order'));
    }
}