# نظام إدارة المطعم

نظام متكامل لإدارة المطاعم مبني باستخدام Laravel، يوفر واجهة سهلة الاستخدام باللغة العربية لإدارة جميع جوانب المطعم.

## الموديولات الأساسية

### 1. 👤 المستخدمون
- تسجيل الدخول والخروج
- صلاحيات متعددة (مدير، نادل، كاشير)

### 2. 📋 الطلبات
- إنشاء طلب جديد
- تحديد الطاولة
- اختيار العناصر من القائمة
- إرسال الطلب إلى المطبخ
- تغيير حالة الطلب (قيد التحضير، جاهز، تم التوصيل)

### 3. 🍽️ الطاولات
- إدارة الطاولات (إضافة، تعديل، حذف)
- حالة الطاولة (شاغرة، مشغولة)

### 4. 📦 المنتجات / القائمة
- إضافة عناصر إلى القائمة (اسم، وصف، صورة، سعر)
- تصنيفات (مقبلات، وجبات رئيسية، مشروبات، ...)

### 5. 💵 الفواتير والدفع
- إنشاء فاتورة للطلب
- طرق الدفع (نقدًا، بطاقة)
- تسجيل الفواتير السابقة

### 6. 📊 التقارير
- مبيعات يومية / شهرية
- الطلبات حسب النادل
- أفضل الأطباق مبيعًا

## متطلبات النظام

- PHP >= 8.1
- Composer
- MySQL أو أي نظام قاعدة بيانات آخر مدعوم من Laravel
- متصفح ويب حديث

## طريقة التثبيت

1. استنساخ المشروع:
```
git clone https://github.com/yourusername/restaurant-management.git
```

2. الانتقال إلى مجلد المشروع:
```
cd restaurant-management
```

3. تثبيت التبعيات:
```
composer install
```

4. إنشاء ملف البيئة:
```
cp .env.example .env
```

5. توليد مفتاح التطبيق:
```
php artisan key:generate
```

6. تكوين قاعدة البيانات في ملف .env

7. تشغيل الهجرات وإنشاء البيانات الأولية:
```
php artisan migrate --seed
```

8. تشغيل الخادم المحلي:
```
php artisan serve
```

9. الوصول إلى التطبيق على العنوان:
```
http://localhost:8000
```

## بيانات تسجيل الدخول الافتراضية

- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: password

## الترخيص

هذا المشروع مرخص تحت [رخصة MIT](https://opensource.org/licenses/MIT).