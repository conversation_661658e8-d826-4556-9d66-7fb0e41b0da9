<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class Setting extends Model
{
    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'label',
        'description',
        'is_public'
    ];

    protected $casts = [
        'is_public' => 'boolean',
    ];

    /**
     * الحصول على قيمة إعداد معين
     */
    public static function get($key, $default = null)
    {
        $cacheKey = "setting_{$key}";

        return Cache::remember($cacheKey, 3600, function () use ($key, $default) {
            $setting = static::where('key', $key)->first();

            if (!$setting) {
                return $default;
            }

            // تحويل القيمة حسب النوع
            return static::castValue($setting->value, $setting->type);
        });
    }

    /**
     * تحديث قيمة إعداد معين
     */
    public static function set($key, $value, $type = 'text', $label = null, $group = 'general', $description = null)
    {
        // إذا كان الإعداد موجود، نحدث القيمة والنوع فقط
        $existingSetting = static::where('key', $key)->first();

        if ($existingSetting) {
            $existingSetting->update([
                'value' => $value,
                'type' => $type
            ]);
            $setting = $existingSetting;
        } else {
            // إذا كان إعداد جديد، نحتاج لجميع البيانات المطلوبة
            $setting = static::create([
                'key' => $key,
                'value' => $value,
                'type' => $type,
                'label' => $label ?: static::generateLabel($key),
                'group' => $group,
                'description' => $description,
                'is_public' => false
            ]);
        }

        // مسح cache
        Cache::forget("setting_{$key}");

        return $setting;
    }

    /**
     * توليد label تلقائي من key
     */
    protected static function generateLabel($key)
    {
        $labels = [
            'restaurant_logo' => 'شعار المطعم',
            'restaurant_name' => 'اسم المطعم',
            'restaurant_address' => 'عنوان المطعم',
            'restaurant_phone' => 'رقم الهاتف',
            'restaurant_email' => 'البريد الإلكتروني',
            'restaurant_website' => 'الموقع الإلكتروني',
            'currency' => 'رمز العملة',
            'currency_code' => 'كود العملة',
            'tax_rate' => 'معدل الضريبة',
            'tax_number' => 'الرقم الضريبي',
            'default_print_size' => 'حجم الطباعة الافتراضي',
            'print_logo' => 'طباعة الشعار',
            'auto_print_kitchen_order' => 'طباعة طلب المطبخ تلقائياً'
        ];

        return $labels[$key] ?? ucfirst(str_replace('_', ' ', $key));
    }

    /**
     * تحويل القيمة حسب النوع
     */
    protected static function castValue($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'number':
                return is_numeric($value) ? (float) $value : 0;
            case 'json':
                return json_decode($value, true);
            default:
                return $value;
        }
    }

    /**
     * الحصول على جميع الإعدادات حسب المجموعة
     */
    public static function getGroup($group)
    {
        return static::where('group', $group)->get()->pluck('value', 'key');
    }

    /**
     * الحصول على الإعدادات العامة للمطعم
     */
    public static function getRestaurantInfo()
    {
        return [
            'name' => static::get('restaurant_name', 'مطعم الوجبات اللذيذة'),
            'address' => static::get('restaurant_address', 'الرياض، المملكة العربية السعودية'),
            'phone' => static::get('restaurant_phone', '0123456789'),
            'email' => static::get('restaurant_email', '<EMAIL>'),
            'logo' => static::get('restaurant_logo'),
            'currency' => static::get('currency', 'ر.س'),
            'currency_code' => static::get('currency_code', 'SAR'),
            'tax_rate' => static::get('tax_rate', 15),
            'tax_number' => static::get('tax_number'),
        ];
    }

    /**
     * رفع ملف الشعار
     */
    public static function uploadLogo($file)
    {
        if ($file) {
            // حذف الشعار القديم
            $oldLogo = static::get('restaurant_logo');
            if ($oldLogo && Storage::disk('public')->exists($oldLogo)) {
                Storage::disk('public')->delete($oldLogo);
            }

            // رفع الشعار الجديد
            $path = $file->store('logos', 'public');
            static::set('restaurant_logo', $path, 'image', 'شعار المطعم', 'restaurant', 'شعار المطعم الذي سيظهر في الفواتير والتقارير');

            return $path;
        }

        return null;
    }
}
