<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\AutoBackup::class,
        Commands\BackupScheduleCommand::class,
    ];

    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // نسخة احتياطية يومية لقاعدة البيانات في الساعة 2:00 صباحاً
        $schedule->command('backup:schedule database')
                 ->dailyAt('02:00')
                 ->name('daily-database-backup')
                 ->withoutOverlapping()
                 ->runInBackground();

        // نسخة احتياطية أسبوعية كاملة يوم الأحد في الساعة 3:00 صباحاً
        $schedule->command('backup:schedule full')
                 ->weeklyOn(0, '03:00')
                 ->name('weekly-full-backup')
                 ->withoutOverlapping()
                 ->runInBackground();

        // تنظيف النسخ الاحتياطية القديمة (أكثر من 30 يوم)
        $schedule->call(function () {
            $oldBackups = \App\Models\Backup::where('created_at', '<', now()->subDays(30))->get();
            foreach ($oldBackups as $backup) {
                $backup->deleteFile();
            }
        })->daily()->name('cleanup-old-backups');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
