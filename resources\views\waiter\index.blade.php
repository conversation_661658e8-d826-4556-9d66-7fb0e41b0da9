@extends('layouts.waiter')

@section('title', 'واجهة النادل')

@section('styles')
<style>
    .pos-container {
        background-color: #f8f9fa;
        border-radius: 15px;
        padding: 10px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    }

    /* Tablet and larger screens */
    @media (min-width: 768px) {
        .pos-container {
            padding: 20px;
        }
    }

    .pos-header {
        display: flex;
        flex-direction: column;
        gap: 15px;
        margin-bottom: 20px;
    }

    .pos-header > div:first-child {
        text-align: center;
    }

    .pos-header h2 {
        font-size: 1.5rem;
        margin-bottom: 5px;
    }

    /* Tablet and larger screens */
    @media (min-width: 768px) {
        .pos-header {
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
        }

        .pos-header > div:first-child {
            text-align: right;
        }

        .pos-header h2 {
            font-size: 2rem;
        }
    }

    .pos-actions {
        display: flex;
        gap: 10px;
    }

    .pos-action-btn {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }

    .pos-main {
        display: flex;
        gap: 20px;
        flex-direction: column;
    }

    .pos-cart {
        background-color: white;
        border-radius: 10px;
        padding: 15px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
        order: 2;
    }

    .pos-products {
        background-color: white;
        border-radius: 10px;
        padding: 15px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
        order: 1;
    }

    /* Desktop and larger screens */
    @media (min-width: 992px) {
        .pos-main {
            flex-direction: row;
        }

        .pos-cart {
            flex: 1;
            order: 1;
        }

        .pos-products {
            flex: 1;
            order: 2;
        }
    }

    .pos-cart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .pos-cart-items {
        max-height: 400px;
        overflow-y: auto;
        margin-bottom: 15px;
    }

    .pos-cart-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        border-bottom: 1px solid #eee;
    }

    .pos-cart-item-remove {
        color: #dc3545;
        cursor: pointer;
    }

    .pos-cart-item-info {
        flex: 1;
        margin: 0 10px;
    }

    .pos-cart-item-quantity {
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .pos-cart-item-quantity button {
        width: 25px;
        height: 25px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        padding: 0;
    }

    .pos-cart-total {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 10px;
        margin-top: 15px;
    }

    .pos-cart-total-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }

    .pos-cart-total-final {
        font-size: 24px;
        font-weight: bold;
        color: #0d6efd;
    }

    .pos-products-filters {
        display: flex;
        flex-direction: column;
        gap: 10px;
        margin-bottom: 15px;
    }

    .pos-products-filter {
        width: 100%;
    }

    /* Tablet and larger screens */
    @media (min-width: 768px) {
        .pos-products-filters {
            flex-direction: row;
            gap: 15px;
        }

        .pos-products-filter {
            flex: 1;
        }
    }

    .pos-products-categories {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
        overflow-x: auto;
        padding-bottom: 10px;
    }

    .pos-category-btn {
        white-space: nowrap;
        padding: 8px 15px;
    }

    .pos-products-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        max-height: 400px;
        overflow-y: auto;
        padding-right: 5px;
    }

    /* Tablet screens */
    @media (min-width: 576px) {
        .pos-products-grid {
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
        }
    }

    /* Desktop screens */
    @media (min-width: 768px) {
        .pos-products-grid {
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            max-height: 500px;
        }
    }

    /* Large desktop screens */
    @media (min-width: 1200px) {
        .pos-products-grid {
            grid-template-columns: repeat(5, 1fr);
        }
    }

    .pos-product-card {
        border: 1px solid #eee;
        border-radius: 10px;
        padding: 10px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
    }

    .pos-product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .pos-product-image {
        width: 100%;
        height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;
        overflow: hidden;
        border-radius: 5px;
        background-color: #f8f9fa;
    }

    .pos-product-image img {
        max-width: 100%;
        max-height: 100%;
        object-fit: cover;
    }

    .pos-product-image .no-image {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #adb5bd;
        background-color: #e9ecef;
    }

    .pos-product-name {
        font-weight: bold;
        margin-bottom: 5px;
        font-size: 14px;
        height: 40px;
        overflow: hidden;
    }

    .pos-product-price {
        color: #0d6efd;
        font-weight: bold;
    }

    .pos-payment-methods {
        display: grid;
        grid-template-columns: 1fr;
        gap: 10px;
        margin-top: 15px;
    }

    /* Mobile landscape and larger */
    @media (min-width: 576px) {
        .pos-payment-methods {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    /* Tablet and larger */
    @media (min-width: 768px) {
        .pos-payment-methods {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    .pos-payment-btn {
        padding: 10px;
        text-align: center;
        border-radius: 5px;
    }

    .table-selector {
        max-height: 300px;
        overflow-y: auto;
    }

    .table-card {
        cursor: pointer;
        transition: all 0.3s;
    }

    .table-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .table-card.selected {
        border: 2px solid #0d6efd;
    }

    .table-card.available {
        background-color: #d1e7dd;
    }

    .table-card.occupied {
        background-color: #f8d7da;
    }

    .table-card.reserved {
        background-color: #fff3cd;
    }

    /* أنماط نافذة الدفع النقدي */
    .payment-summary {
        border: 1px solid #dee2e6;
    }

    .change-amount {
        border: 1px solid #dee2e6;
    }

    #amountPaid {
        font-size: 1.2rem;
        font-weight: bold;
    }

    #modalTotalAmount, #changeAmount {
        font-size: 1.2rem;
    }

    /* أنماط بطاقات مقاس الطباعة */
    .print-size-card {
        cursor: pointer;
        transition: all 0.3s;
        height: 100%;
    }

    .print-size-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .print-size-card.selected {
        background-color: #d1e7dd;
        border-color: #0d6efd;
    }

    /* تنسيق نافذة إضافة الأصناف */
    .add-items-categories {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        margin-bottom: 15px;
        overflow-x: auto;
        padding-bottom: 5px;
    }

    .add-items-category-btn {
        white-space: nowrap;
        flex-shrink: 0;
    }

    .add-items-products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
        max-height: 400px;
        overflow-y: auto;
        padding: 10px;
        border: 1px solid #dee2e6;
        border-radius: 5px;
    }

    .add-items-product-card {
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s;
    }

    .add-items-product-card:hover {
        border-color: #0d6efd;
        box-shadow: 0 0 5px rgba(13, 110, 253, 0.3);
    }

    .add-items-product-image {
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;
    }

    .add-items-product-image img {
        max-height: 100%;
        max-width: 100%;
        object-fit: contain;
    }

    .add-items-product-image .no-image {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        color: #6c757d;
    }

    .add-items-product-name {
        font-weight: bold;
        margin-bottom: 5px;
        font-size: 14px;
        height: 40px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .add-items-product-price {
        color: #0d6efd;
        font-weight: bold;
    }

    /* تنسيق بطاقات طرق الدفع */
    .payment-method-card {
        cursor: pointer;
        transition: all 0.3s;
        height: 100%;
        border: 2px solid #dee2e6;
    }

    .payment-method-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .payment-method-card.selected {
        border-color: #0d6efd;
        background-color: #f8f9fa;
    }

    /* تنسيقات الوضع الليلي */
    body.dark-mode {
        background-color: #121212;
        color: #e0e0e0;
    }

    body.dark-mode .card,
    body.dark-mode .modal-content,
    body.dark-mode .list-group-item {
        background-color: #1e1e1e;
        color: #e0e0e0;
        border-color: #333;
    }

    body.dark-mode .table {
        color: #e0e0e0;
    }

    body.dark-mode .table-striped > tbody > tr:nth-of-type(odd) {
        background-color: rgba(255, 255, 255, 0.05);
    }

    body.dark-mode .form-control,
    body.dark-mode .form-select,
    body.dark-mode .input-group-text {
        background-color: #2a2a2a;
        color: #e0e0e0;
        border-color: #444;
    }

    body.dark-mode .btn-light {
        background-color: #2a2a2a;
        color: #e0e0e0;
        border-color: #444;
    }

    body.dark-mode .text-muted {
        color: #aaa !important;
    }

    body.dark-mode .pos-product-card,
    body.dark-mode .add-items-product-card,
    body.dark-mode .print-option-card,
    body.dark-mode .payment-method-card {
        background-color: #2a2a2a;
        border-color: #444;
    }

    body.dark-mode .pos-product-card:hover,
    body.dark-mode .add-items-product-card:hover,
    body.dark-mode .print-option-card:hover,
    body.dark-mode .payment-method-card:hover {
        background-color: #333;
        border-color: #555;
    }

    /* تنسيقات أحجام الخط */
    body.font-small {
        font-size: 0.85rem;
    }

    body.font-medium {
        font-size: 1rem;
    }

    body.font-large {
        font-size: 1.15rem;
    }

    /* تنسيقات إضافية للبطاقات */
    .print-option-card {
        cursor: pointer;
        transition: all 0.3s;
        height: 100%;
        border: 2px solid #dee2e6;
    }

    .print-option-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="pos-container">
        <div class="pos-header">
            <div>
                <h2>واجهة الكاشير</h2>
                <p class="text-muted">{{ now()->format('Y/m/d') }} - {{ auth()->user()->name }}</p>
            </div>
            <div class="pos-actions">
                <button class="pos-action-btn btn btn-primary" id="activeOrdersBtn" data-bs-toggle="modal" data-bs-target="#activeOrdersModal">
                    <i class="fas fa-list-alt"></i>
                </button>
                <button class="pos-action-btn btn btn-warning position-relative" id="notificationsBtn" data-bs-toggle="modal" data-bs-target="#notificationsModal">
                    <i class="fas fa-bell"></i>
                    <span id="notificationsBadge" class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger d-none">
                        0
                        <span class="visually-hidden">إشعارات غير مقروءة</span>
                    </span>
                </button>
                @if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('manager') || auth()->user()->hasRole('cashier'))
                <button class="pos-action-btn btn btn-danger">
                    <i class="fas fa-times"></i>
                </button>
                <button class="pos-action-btn btn btn-success">
                    <i class="fas fa-calculator"></i>
                </button>
                @endif
                <button class="pos-action-btn btn btn-info">
                    <i class="fas fa-print"></i>
                </button>
                @if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('manager') || auth()->user()->hasRole('cashier'))
                <button class="pos-action-btn btn btn-secondary">
                    <i class="fas fa-cog"></i>
                </button>
                @endif
            </div>
        </div>

        <div class="pos-main">
            <div class="pos-cart">
                <div class="pos-cart-header">
                    <div class="btn-group w-100 mb-3" role="group" aria-label="نوع الطلب">
                        <input type="radio" class="btn-check" name="orderType" id="dineIn" value="dine_in" checked>
                        <label class="btn btn-primary" for="dineIn" style="display: flex; justify-content: center; align-items: center;">
                            <i class="fas fa-utensils me-2"></i> طلب داخلي
                        </label>

                        <input type="radio" class="btn-check" name="orderType" id="takeaway" value="takeaway">
                        <label class="btn btn-outline-primary" for="takeaway" style="display: flex; justify-content: center; align-items: center;">
                            <i class="fas fa-shopping-bag me-2"></i> طلب خارجي
                        </label>
                    </div>

                    <div id="dineInOptions" class="order-type-options">
                        <div class="input-group">
                            <span class="input-group-text" style="background-color: #f8f9fa; border-left: none;"><i class="fas fa-chair"></i></span>
                            <button class="form-control text-start" id="selectedTable" data-bs-toggle="modal" data-bs-target="#tableModal" style="text-align: right !important; border-right: none;">
                                اختر طاولة
                            </button>
                        </div>
                    </div>

                    <div id="takeawayOptions" class="order-type-options d-none">
                        <button class="btn btn-outline-primary w-100" id="openTakeawayInfoBtn" data-bs-toggle="modal" data-bs-target="#takeawayInfoModal" style="display: flex; justify-content: center; align-items: center; height: 38px;">
                            <i class="fas fa-info-circle me-2"></i> إضافة معلومات للطلب الخارجي
                        </button>

                        <!-- حقول مخفية لتخزين القيم -->
                        <input type="hidden" id="customerPhone" value="">
                        <input type="hidden" id="carNumber" value="">
                    </div>

                    <div class="d-flex justify-content-between mt-2">
                        <button class="btn btn-outline-primary" id="orderInfoBtn" data-bs-toggle="modal" data-bs-target="#orderInfoModal" style="display: flex; justify-content: center; align-items: center; width: 48%;">
                            <i class="fas fa-info-circle me-2"></i> معلومات الطلب
                        </button>
                        <button class="btn btn-outline-danger" id="clearCart" style="display: flex; justify-content: center; align-items: center; width: 48%;">
                            <i class="fas fa-trash-alt me-2"></i> مسح السلة
                        </button>
                    </div>
                </div>

                <div class="pos-cart-items" id="cartItems">
                    <!-- سيتم إضافة عناصر السلة هنا بواسطة JavaScript -->
                </div>

                <div class="pos-cart-total">
                    <div class="pos-cart-total-row">
                        <span>الإجمالي:</span>
                        <span id="subtotal">0.00 {{ \App\Models\Setting::get('currency', 'ر.س') }}</span>
                    </div>
                    <div class="pos-cart-total-row">
                        <span>الضريبة ({{ \App\Models\Setting::get('tax_rate', 15) }}%):</span>
                        <span id="tax">0.00 {{ \App\Models\Setting::get('currency', 'ر.س') }}</span>
                    </div>
                    <div class="pos-cart-total-row">
                        <span>الخصم:</span>
                        <span id="discount">0.00 {{ \App\Models\Setting::get('currency', 'ر.س') }}</span>
                    </div>
                    <hr>
                    <div class="pos-cart-total-row">
                        <span>المجموع:</span>
                        <span class="pos-cart-total-final" id="total">0.00 {{ \App\Models\Setting::get('currency', 'ر.س') }}</span>
                    </div>
                </div>

                <div class="pos-payment-methods" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
                    @if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('manager') || auth()->user()->hasRole('cashier'))
                    <button class="btn btn-info pos-payment-btn" id="draftBtn" style="display: flex; justify-content: center; align-items: center; height: 50px;">
                        <i class="fas fa-save me-2"></i> حفظ كمسودة
                    </button>
                    <button class="btn btn-primary pos-payment-btn" id="quotationBtn" style="display: flex; justify-content: center; align-items: center; height: 50px;">
                        <i class="fas fa-file-invoice me-2"></i> عرض سعر
                    </button>
                    <button class="btn btn-danger pos-payment-btn" id="cancelBtn" style="display: flex; justify-content: center; align-items: center; height: 50px;">
                        <i class="fas fa-times me-2"></i> إلغاء
                    </button>
                    @endif
                    <button class="btn btn-warning pos-payment-btn" id="suspendBtn" style="display: flex; justify-content: center; align-items: center; height: 50px;">
                        <i class="fas fa-pause me-2"></i> تعليق
                    </button>
                    <button class="btn btn-success pos-payment-btn" id="cardBtn" style="display: flex; justify-content: center; align-items: center; height: 50px;">
                        <i class="fas fa-credit-card me-2"></i> بطاقة
                    </button>
                    <button class="btn btn-primary pos-payment-btn" id="cashBtn" style="display: flex; justify-content: center; align-items: center; height: 50px;">
                        <i class="fas fa-money-bill-wave me-2"></i> نقدي
                    </button>
                </div>
            </div>

            <div class="pos-products">
                <div class="pos-products-filters">
                    <div class="pos-products-filter">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="searchProduct" placeholder="بحث عن منتج...">
                        </div>
                    </div>
                    <div class="pos-products-filter">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-filter"></i></span>
                            <select class="form-select" id="filterProducts">
                                <option value="all">جميع المنتجات</option>
                                <option value="popular">الأكثر مبيعًا</option>
                                <option value="recent">أضيف مؤخرًا</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="pos-products-categories">
                    <button class="btn btn-outline-primary pos-category-btn active" data-category="all">الكل</button>
                    @foreach($categories as $category)
                        <button class="btn btn-outline-primary pos-category-btn" data-category="{{ $category->id }}">{{ $category->name }}</button>
                    @endforeach
                </div>

                <div class="pos-products-grid" id="productsGrid">
                    @foreach($products as $product)
                        <div class="pos-product-card" data-id="{{ $product->id }}" data-category="{{ $product->category_id }}">
                            <div class="pos-product-image">
                                @if($product->image)
                                    <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}" class="img-fluid">
                                @else
                                    <div class="no-image">
                                        <i class="fas fa-utensils fa-2x"></i>
                                    </div>
                                @endif
                            </div>
                            <div class="pos-product-name">{{ $product->name }}</div>
                            <div class="pos-product-price">{{ number_format($product->price, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal اختيار الطاولة -->
<div class="modal fade" id="tableModal" tabindex="-1" aria-labelledby="tableModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tableModalLabel">اختر طاولة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row table-selector">
                    @foreach($tables as $table)
                        <div class="col-md-3 mb-3">
                            <div class="card table-card {{ $table->status }}" data-id="{{ $table->id }}" data-name="{{ $table->name }}">
                                <div class="card-body text-center">
                                    <i class="fas fa-chair fa-2x mb-2"></i>
                                    <h5 class="card-title">{{ $table->name }}</h5>
                                    <p class="card-text">
                                        @if($table->status == 'available')
                                            <span class="badge bg-success">متاحة</span>
                                        @elseif($table->status == 'occupied')
                                            <span class="badge bg-danger">مشغولة</span>
                                        @else
                                            <span class="badge bg-warning">محجوزة</span>
                                        @endif
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelTableBtn">إلغاء</button>
                <button type="button" class="btn btn-primary" id="selectTableBtn">اختيار</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal معلومات الطلب -->
<div class="modal fade" id="orderInfoModal" tabindex="-1" aria-labelledby="orderInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="orderInfoModalLabel">معلومات الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="orderNotes" class="form-label">ملاحظات الطلب</label>
                    <textarea class="form-control" id="orderNotes" rows="3" placeholder="أضف ملاحظات خاصة بهذا الطلب..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="saveOrderInfoBtn" data-bs-dismiss="modal">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة ملاحظات للمنتج -->
<div class="modal fade" id="productNotesModal" tabindex="-1" aria-labelledby="productNotesModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="productNotesModalLabel">إضافة ملاحظات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="productId">
                <div class="mb-3">
                    <label for="productQuantity" class="form-label">الكمية</label>
                    <input type="number" class="form-control" id="productQuantity" min="1" value="1">
                </div>
                <div class="mb-3">
                    <label for="productNotes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="productNotes" rows="3" placeholder="أضف ملاحظات خاصة بهذا المنتج..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelProductNotesBtn">إلغاء</button>
                <button type="button" class="btn btn-primary" id="addToCartBtn">إضافة إلى السلة</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal معلومات الطلب الخارجي -->
<div class="modal fade" id="takeawayInfoModal" tabindex="-1" aria-labelledby="takeawayInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="takeawayInfoModalLabel">معلومات الطلب الخارجي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="modalCustomerPhone" class="form-label">رقم الهاتف (اختياري)</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-phone"></i></span>
                        <input type="text" class="form-control" id="modalCustomerPhone" placeholder="أدخل رقم الهاتف">
                    </div>
                </div>
                <div class="mb-3">
                    <label for="modalCarNumber" class="form-label">رقم السيارة (اختياري)</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-car"></i></span>
                        <input type="text" class="form-control" id="modalCarNumber" placeholder="أدخل رقم السيارة">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="saveTakeawayInfoBtn" data-bs-dismiss="modal">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal اختيار مقاس الطباعة -->
<div class="modal fade" id="printSizeModal" tabindex="-1" aria-labelledby="printSizeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="printSizeModalLabel">طباعة الفاتورة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="mb-3">هل ترغب في طباعة الفاتورة؟</p>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card print-size-card" data-size="85mm">
                            <div class="card-body text-center">
                                <i class="fas fa-receipt fa-3x mb-2"></i>
                                <h5 class="card-title">طباعة 85مم</h5>
                                <p class="card-text">طباعة الفاتورة بمقاس 85مم</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card print-size-card" data-size="no-print">
                            <div class="card-body text-center">
                                <i class="fas fa-times-circle fa-3x mb-2"></i>
                                <h5 class="card-title">بدون طباعة</h5>
                                <p class="card-text">متابعة بدون طباعة الفاتورة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelPrintBtn">إلغاء</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal الطلبات النشطة -->
<div class="modal fade" id="activeOrdersModal" tabindex="-1" aria-labelledby="activeOrdersModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="activeOrdersModalLabel">الطلبات النشطة والمعلقة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="activeOrdersTable">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الطاولة</th>
                                <th>الوقت</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                @if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('manager') || auth()->user()->hasRole('cashier'))
                                <th>النادل</th>
                                @endif
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
                <div id="noActiveOrders" class="text-center py-4 d-none">
                    <i class="fas fa-info-circle fa-2x mb-2 text-muted"></i>
                    <p>لا توجد طلبات نشطة حالياً</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="refreshActiveOrdersBtn">
                    <i class="fas fa-sync-alt me-1"></i> تحديث
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal تفاصيل الطلب النشط -->
<div class="modal fade" id="activeOrderDetailsModal" tabindex="-1" aria-labelledby="activeOrderDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="activeOrderDetailsModalLabel">تفاصيل الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>رقم الطلب:</strong> <span id="orderDetailId"></span></p>
                        <p><strong>الطاولة:</strong> <span id="orderDetailTable"></span></p>
                        <p><strong>الوقت:</strong> <span id="orderDetailTime"></span></p>
                        <p><strong>اسم العميل:</strong> <span id="orderDetailCustomerName"></span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>الحالة:</strong> <span id="orderDetailStatus"></span></p>
                        <p><strong>النادل:</strong> <span id="orderDetailWaiter"></span></p>
                        <p><strong>ملاحظات:</strong> <span id="orderDetailNotes"></span></p>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped" id="orderItemsTable">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>المنتج</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>المجموع</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                        </tbody>
                        <tfoot>
                            <tr>
                                <th colspan="4" class="text-end">المجموع:</th>
                                <th id="orderDetailSubtotal">0.00 {{ \App\Models\Setting::get('currency', 'ر.س') }}</th>
                                <th></th>
                            </tr>
                            <tr>
                                <th colspan="4" class="text-end">الضريبة ({{ \App\Models\Setting::get('tax_rate', 15) }}%):</th>
                                <th id="orderDetailTax">0.00 {{ \App\Models\Setting::get('currency', 'ر.س') }}</th>
                                <th></th>
                            </tr>
                            <tr>
                                <th colspan="4" class="text-end">الإجمالي:</th>
                                <th id="orderDetailTotal">0.00 {{ \App\Models\Setting::get('currency', 'ر.س') }}</th>
                                <th></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="backToActiveOrdersBtn">رجوع</button>
                <button type="button" class="btn btn-info" id="addItemsToOrderBtn">
                    <i class="fas fa-plus me-1"></i> إضافة أصناف
                </button>
                <button type="button" class="btn btn-success" id="payActiveOrderBtn">
                    <i class="fas fa-money-bill-wave me-1"></i> دفع الطلب
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة أصناف للطلب -->
<div class="modal fade" id="addItemsModal" tabindex="-1" aria-labelledby="addItemsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addItemsModalLabel">إضافة أصناف للطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>رقم الطلب:</strong> <span id="addItemsOrderId"></span></p>
                        <p><strong>الطاولة:</strong> <span id="addItemsOrderTable"></span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>الحالة:</strong> <span id="addItemsOrderStatus"></span></p>
                        <p><strong>العميل:</strong> <span id="addItemsOrderCustomer"></span></p>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="addItemsSearchProduct" placeholder="بحث عن منتج...">
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <div class="add-items-categories">
                            <button class="btn btn-outline-primary add-items-category-btn active" data-category="all">الكل</button>
                            @foreach($categories as $category)
                                <button class="btn btn-outline-primary add-items-category-btn" data-category="{{ $category->id }}">{{ $category->name }}</button>
                            @endforeach
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="add-items-products-grid" id="addItemsProductsGrid">
                            @foreach($products as $product)
                                <div class="add-items-product-card" data-id="{{ $product->id }}" data-category="{{ $product->category_id }}">
                                    <div class="add-items-product-image">
                                        @if($product->image)
                                            <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}" class="img-fluid">
                                        @else
                                            <div class="no-image">
                                                <i class="fas fa-utensils fa-2x"></i>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="add-items-product-name">{{ $product->name }}</div>
                                    <div class="add-items-product-price">{{ number_format($product->price, 2) }} {{ \App\Models\Setting::get('currency', 'ر.س') }}</div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelAddItemsBtn">إلغاء</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة ملاحظات للمنتج المضاف -->
<div class="modal fade" id="addItemNotesModal" tabindex="-1" aria-labelledby="addItemNotesModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addItemNotesModalLabel">إضافة منتج للطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="addItemProductId">
                <div class="mb-3">
                    <label for="addItemQuantity" class="form-label">الكمية</label>
                    <input type="number" class="form-control" id="addItemQuantity" min="1" value="1">
                </div>
                <div class="mb-3">
                    <label for="addItemNotes" class="form-label">ملاحظات</label>
                    <textarea class="form-control" id="addItemNotes" rows="3" placeholder="أضف ملاحظات خاصة بهذا المنتج..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelAddItemNotesBtn">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmAddItemBtn">إضافة</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal تعليق الطلب -->
<div class="modal fade" id="suspendOrderModal" tabindex="-1" aria-labelledby="suspendOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="suspendOrderModalLabel">تعليق الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="customerName" class="form-label">اسم العميل</label>
                    <input type="text" class="form-control" id="customerName" placeholder="أدخل اسم العميل">
                </div>
                <div class="mb-3">
                    <label for="suspendNotes" class="form-label">ملاحظات إضافية</label>
                    <textarea class="form-control" id="suspendNotes" rows="3" placeholder="أضف ملاحظات إضافية للطلب المعلق..."></textarea>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    سيتم تعليق الطلب وحفظه لحين انتهاء العميل من الطعام. يمكنك العودة إليه لاحقاً من خلال قائمة الطلبات النشطة.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelSuspendBtn">إلغاء</button>
                <button type="button" class="btn btn-warning" id="confirmSuspendBtn">
                    <i class="fas fa-pause me-1"></i> تعليق الطلب
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal إلغاء الطلبات -->
<div class="modal fade" id="cancelOrdersModal" tabindex="-1" aria-labelledby="cancelOrdersModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cancelOrdersModalLabel">إلغاء الطلبات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تنبيه:</strong> يمكنك إلغاء الطلبات النشطة فقط. لا يمكن إلغاء الطلبات المكتملة.
                </div>

                <div class="mb-3">
                    <label for="cancelOrderSearch" class="form-label">البحث عن طلب</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" id="cancelOrderSearch" placeholder="أدخل رقم الطلب أو اسم العميل...">
                        <button class="btn btn-primary" id="searchCancelOrderBtn">بحث</button>
                    </div>
                </div>

                <div id="cancelOrdersContainer" class="d-none">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>الطاولة</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="cancelOrdersTableBody">
                                <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="noCancelOrders" class="text-center py-4">
                    <i class="fas fa-info-circle fa-2x mb-2 text-muted"></i>
                    <p>قم بالبحث عن طلب لإلغائه</p>
                </div>

                <!-- قسم تأكيد الإلغاء -->
                <div id="confirmCancelSection" class="mt-3 d-none">
                    <hr>
                    <h5>تأكيد إلغاء الطلب</h5>
                    <div class="alert alert-danger">
                        <p><strong>هل أنت متأكد من رغبتك في إلغاء الطلب رقم: <span id="cancelOrderId"></span>؟</strong></p>
                        <p>هذا الإجراء لا يمكن التراجع عنه.</p>
                    </div>
                    <div class="mb-3">
                        <label for="cancelReason" class="form-label">سبب الإلغاء</label>
                        <select class="form-select" id="cancelReason">
                            <option value="">اختر سبب الإلغاء...</option>
                            <option value="customer_request">طلب العميل</option>
                            <option value="out_of_stock">نفاد المخزون</option>
                            <option value="wrong_order">طلب خاطئ</option>
                            <option value="other">سبب آخر</option>
                        </select>
                    </div>
                    <div class="mb-3 d-none" id="otherReasonContainer">
                        <label for="otherCancelReason" class="form-label">سبب آخر</label>
                        <textarea class="form-control" id="otherCancelReason" rows="2" placeholder="أدخل سبب الإلغاء..."></textarea>
                    </div>
                    <div class="d-flex justify-content-end">
                        <button class="btn btn-secondary me-2" id="backToCancelOrdersBtn">رجوع</button>
                        <button class="btn btn-danger" id="confirmCancelOrderBtn">تأكيد الإلغاء</button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal خيارات الطباعة -->
<div class="modal fade" id="printOptionsModal" tabindex="-1" aria-labelledby="printOptionsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="printOptionsModalLabel">خيارات الطباعة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card print-option-card" data-option="last-invoice">
                            <div class="card-body text-center">
                                <i class="fas fa-receipt fa-3x mb-2 text-primary"></i>
                                <h5 class="card-title">آخر فاتورة</h5>
                                <p class="card-text">طباعة آخر فاتورة تم إصدارها</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card print-option-card" data-option="daily-report">
                            <div class="card-body text-center">
                                <i class="fas fa-file-alt fa-3x mb-2 text-success"></i>
                                <h5 class="card-title">تقرير اليوم</h5>
                                <p class="card-text">طباعة تقرير مبيعات اليوم</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card print-option-card" data-option="kitchen-order">
                            <div class="card-body text-center">
                                <i class="fas fa-utensils fa-3x mb-2 text-warning"></i>
                                <h5 class="card-title">طلب المطبخ</h5>
                                <p class="card-text">طباعة طلب للمطبخ</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card print-option-card" data-option="custom-invoice">
                            <div class="card-body text-center">
                                <i class="fas fa-search fa-3x mb-2 text-info"></i>
                                <h5 class="card-title">فاتورة محددة</h5>
                                <p class="card-text">البحث عن فاتورة محددة وطباعتها</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قسم البحث عن فاتورة محددة -->
                <div id="customInvoiceSection" class="mt-3 d-none">
                    <hr>
                    <h5>البحث عن فاتورة</h5>
                    <div class="mb-3">
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="invoiceSearchInput" placeholder="أدخل رقم الفاتورة أو اسم العميل...">
                            <button class="btn btn-primary" id="searchInvoiceBtn">بحث</button>
                        </div>
                    </div>
                    <div id="invoiceSearchResults" class="d-none">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>التاريخ</th>
                                        <th>المبلغ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="invoiceSearchResultsBody">
                                    <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal التقارير -->
<div class="modal fade" id="reportsModal" tabindex="-1" aria-labelledby="reportsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reportsModalLabel">التقارير</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <ul class="nav nav-tabs mb-3" id="reportsTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="sales-tab" data-bs-toggle="tab" data-bs-target="#sales" type="button" role="tab" aria-controls="sales" aria-selected="true">المبيعات</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="products-tab" data-bs-toggle="tab" data-bs-target="#products" type="button" role="tab" aria-controls="products" aria-selected="false">المنتجات</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="tables-tab" data-bs-toggle="tab" data-bs-target="#tables" type="button" role="tab" aria-controls="tables" aria-selected="false">الطاولات</button>
                    </li>
                </ul>
                <div class="tab-content" id="reportsTabContent">
                    <div class="tab-pane fade show active" id="sales" role="tabpanel" aria-labelledby="sales-tab">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                    <input type="date" class="form-control" id="salesStartDate" value="{{ date('Y-m-d', strtotime('-7 days')) }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                    <input type="date" class="form-control" id="salesEndDate" value="{{ date('Y-m-d') }}">
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-12">
                                <button class="btn btn-primary" id="generateSalesReportBtn">
                                    <i class="fas fa-chart-line me-1"></i> إنشاء تقرير المبيعات
                                </button>
                            </div>
                        </div>
                        <div id="salesReportContainer" class="d-none">
                            <div class="alert alert-info">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5 class="mb-1">تقرير المبيعات</h5>
                                        <p class="mb-0" id="salesReportDateRange"></p>
                                    </div>
                                    <button class="btn btn-sm btn-info" id="printSalesReportBtn">
                                        <i class="fas fa-print"></i> طباعة
                                    </button>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered" id="salesReportTable">
                                    <thead>
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>عدد الطلبات</th>
                                            <th>إجمالي المبيعات</th>
                                            <th>إجمالي الضرائب</th>
                                            <th>المجموع الكلي</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th>المجموع</th>
                                            <th id="totalOrders">0</th>
                                            <th id="totalSales">0.00 {{ \App\Models\Setting::get('currency', 'ر.س') }}</th>
                                            <th id="totalTax">0.00 {{ \App\Models\Setting::get('currency', 'ر.س') }}</th>
                                            <th id="grandTotal">0.00 {{ \App\Models\Setting::get('currency', 'ر.س') }}</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="products" role="tabpanel" aria-labelledby="products-tab">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                    <input type="date" class="form-control" id="productsStartDate" value="{{ date('Y-m-d', strtotime('-30 days')) }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                    <input type="date" class="form-control" id="productsEndDate" value="{{ date('Y-m-d') }}">
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-12">
                                <button class="btn btn-primary" id="generateProductsReportBtn">
                                    <i class="fas fa-chart-bar me-1"></i> إنشاء تقرير المنتجات
                                </button>
                            </div>
                        </div>
                        <div id="productsReportContainer" class="d-none">
                            <!-- سيتم ملء هذا القسم بواسطة JavaScript -->
                        </div>
                    </div>
                    <div class="tab-pane fade" id="tables" role="tabpanel" aria-labelledby="tables-tab">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                    <input type="date" class="form-control" id="tablesStartDate" value="{{ date('Y-m-d', strtotime('-7 days')) }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                    <input type="date" class="form-control" id="tablesEndDate" value="{{ date('Y-m-d') }}">
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-12">
                                <button class="btn btn-primary" id="generateTablesReportBtn">
                                    <i class="fas fa-chart-pie me-1"></i> إنشاء تقرير الطاولات
                                </button>
                            </div>
                        </div>
                        <div id="tablesReportContainer" class="d-none">
                            <!-- سيتم ملء هذا القسم بواسطة JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal الآلة الحاسبة -->
<div class="modal fade" id="calculatorModal" tabindex="-1" aria-labelledby="calculatorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="calculatorModalLabel">الآلة الحاسبة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="calculator">
                    <div class="calculator-display mb-2">
                        <input type="text" class="form-control text-end" id="calculatorDisplay" readonly>
                    </div>
                    <div class="calculator-keys">
                        <div class="row g-1 mb-1">
                            <div class="col-3">
                                <button class="btn btn-light w-100 calculator-key" data-key="7">7</button>
                            </div>
                            <div class="col-3">
                                <button class="btn btn-light w-100 calculator-key" data-key="8">8</button>
                            </div>
                            <div class="col-3">
                                <button class="btn btn-light w-100 calculator-key" data-key="9">9</button>
                            </div>
                            <div class="col-3">
                                <button class="btn btn-warning w-100 calculator-key" data-key="/">/</button>
                            </div>
                        </div>
                        <div class="row g-1 mb-1">
                            <div class="col-3">
                                <button class="btn btn-light w-100 calculator-key" data-key="4">4</button>
                            </div>
                            <div class="col-3">
                                <button class="btn btn-light w-100 calculator-key" data-key="5">5</button>
                            </div>
                            <div class="col-3">
                                <button class="btn btn-light w-100 calculator-key" data-key="6">6</button>
                            </div>
                            <div class="col-3">
                                <button class="btn btn-warning w-100 calculator-key" data-key="*">×</button>
                            </div>
                        </div>
                        <div class="row g-1 mb-1">
                            <div class="col-3">
                                <button class="btn btn-light w-100 calculator-key" data-key="1">1</button>
                            </div>
                            <div class="col-3">
                                <button class="btn btn-light w-100 calculator-key" data-key="2">2</button>
                            </div>
                            <div class="col-3">
                                <button class="btn btn-light w-100 calculator-key" data-key="3">3</button>
                            </div>
                            <div class="col-3">
                                <button class="btn btn-warning w-100 calculator-key" data-key="-">-</button>
                            </div>
                        </div>
                        <div class="row g-1 mb-1">
                            <div class="col-3">
                                <button class="btn btn-light w-100 calculator-key" data-key="0">0</button>
                            </div>
                            <div class="col-3">
                                <button class="btn btn-light w-100 calculator-key" data-key=".">.</button>
                            </div>
                            <div class="col-3">
                                <button class="btn btn-danger w-100 calculator-key" data-key="c">C</button>
                            </div>
                            <div class="col-3">
                                <button class="btn btn-warning w-100 calculator-key" data-key="+">+</button>
                            </div>
                        </div>
                        <div class="row g-1">
                            <div class="col-12">
                                <button class="btn btn-success w-100 calculator-key" data-key="=">=</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal الإشعارات -->
<div class="modal fade" id="notificationsModal" tabindex="-1" aria-labelledby="notificationsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="notificationsModalLabel">
                    الإشعارات
                    <small class="text-muted">(آخر 15 إشعار)</small>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="list-group" id="notificationsList">
                    <!-- سيتم ملء هذه القائمة بواسطة JavaScript -->
                </div>
                <div id="noNotifications" class="text-center py-4">
                    <i class="fas fa-bell-slash fa-2x mb-2 text-muted"></i>
                    <p>لا توجد إشعارات جديدة</p>
                </div>
                <div class="text-center mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        يتم عرض آخر 15 إشعار فقط للحفاظ على الأداء
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="markAllReadBtn">
                    <i class="fas fa-check-double me-1"></i> تعيين الكل كمقروء
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal الإعدادات -->
<div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="settingsModalLabel">إعدادات النظام</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="list-group">
                    <a href="{{ route('users.edit', Auth::id()) }}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-user-circle me-2 text-primary"></i>
                                <strong>الملف الشخصي</strong>
                            </div>
                            <i class="fas fa-chevron-left"></i>
                        </div>
                        <small class="text-muted">تعديل بيانات الملف الشخصي وكلمة المرور</small>
                    </a>
                    <a href="{{ route('dashboard') }}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-tachometer-alt me-2 text-success"></i>
                                <strong>لوحة التحكم</strong>
                            </div>
                            <i class="fas fa-chevron-left"></i>
                        </div>
                        <small class="text-muted">الانتقال إلى لوحة التحكم الرئيسية</small>
                    </a>
                    <div class="list-group-item">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="darkModeSwitch">
                            <label class="form-check-label" for="darkModeSwitch">الوضع الليلي</label>
                        </div>
                        <small class="text-muted">تفعيل/تعطيل الوضع الليلي للواجهة</small>
                    </div>
                    <div class="list-group-item">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="soundsSwitch" checked>
                            <label class="form-check-label" for="soundsSwitch">أصوات التنبيهات</label>
                        </div>
                        <small class="text-muted">تفعيل/تعطيل أصوات التنبيهات</small>
                    </div>
                    <div class="list-group-item">
                        <div class="mb-2"><strong>حجم الخط</strong></div>
                        <div class="btn-group w-100" role="group">
                            <input type="radio" class="btn-check" name="fontSizeOptions" id="fontSizeSmall" autocomplete="off">
                            <label class="btn btn-outline-primary" for="fontSizeSmall">صغير</label>

                            <input type="radio" class="btn-check" name="fontSizeOptions" id="fontSizeMedium" autocomplete="off" checked>
                            <label class="btn btn-outline-primary" for="fontSizeMedium">متوسط</label>

                            <input type="radio" class="btn-check" name="fontSizeOptions" id="fontSizeLarge" autocomplete="off">
                            <label class="btn btn-outline-primary" for="fontSizeLarge">كبير</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="saveSettingsBtn">حفظ التغييرات</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal اختيار طريقة الدفع -->
<div class="modal fade" id="paymentMethodModal" tabindex="-1" aria-labelledby="paymentMethodModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="paymentMethodModalLabel">اختر طريقة الدفع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card payment-method-card" data-method="cash">
                            <div class="card-body text-center">
                                <i class="fas fa-money-bill-wave fa-3x mb-2 text-success"></i>
                                <h5 class="card-title">دفع نقدي</h5>
                                <p class="card-text">الدفع باستخدام النقود</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card payment-method-card" data-method="card">
                            <div class="card-body text-center">
                                <i class="fas fa-credit-card fa-3x mb-2 text-primary"></i>
                                <h5 class="card-title">دفع بالبطاقة</h5>
                                <p class="card-text">الدفع باستخدام بطاقة الائتمان</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal الدفع النقدي -->
<div class="modal fade" id="cashPaymentModal" tabindex="-1" aria-labelledby="cashPaymentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cashPaymentModalLabel">الدفع النقدي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="payment-summary p-3 bg-light rounded mb-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span>إجمالي الفاتورة:</span>
                                <span id="modalTotalAmount" class="fw-bold">0.00 ر.س</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-12">
                        <label for="amountPaid" class="form-label">المبلغ المدفوع</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="amountPaid" min="0" step="0.01" placeholder="أدخل المبلغ المدفوع">
                            <span class="input-group-text">{{ \App\Models\Setting::get('currency', 'ر.س') }}</span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="change-amount p-3 bg-light rounded">
                            <div class="d-flex justify-content-between">
                                <span>المبلغ المسترجع:</span>
                                <span id="changeAmount" class="fw-bold text-success">0.00 {{ \App\Models\Setting::get('currency', 'ر.س') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelCashPaymentBtn">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmPaymentBtn">تأكيد الدفع</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // المتغيرات العامة
        let cart = [];
        let selectedTable = null;
        let selectedTableId = null;
        let orderType = 'dine_in';
        let orderNotes = '';
        let customerPhone = '';
        let carNumber = '';
        const currency = '{{ \App\Models\Setting::get('currency', 'ر.س') }}';
        const taxRate = {{ \App\Models\Setting::get('tax_rate', 15) }} / 100; // تحويل النسبة المئوية إلى عدد عشري

        // تهيئة صوت الإشعارات
        const notificationSound = new Audio('{{ asset('sounds/notification.mp3') }}');

        // الحصول على آخر وقت تم فيه عرض الإشعارات
        let lastNotificationCheck = localStorage.getItem('lastNotificationCheck') || 0;

        // تحديث آخر وقت تم فيه عرض الإشعارات
        function updateLastNotificationCheck() {
            lastNotificationCheck = Date.now();
            localStorage.setItem('lastNotificationCheck', lastNotificationCheck);
        }

        // تحديث السلة
        function updateCart() {
            const cartItemsContainer = document.getElementById('cartItems');
            cartItemsContainer.innerHTML = '';

            if (cart.length === 0) {
                cartItemsContainer.innerHTML = '<div class="text-center py-4 text-muted">السلة فارغة</div>';
                updateTotals();
                return;
            }

            cart.forEach((item, index) => {
                const cartItem = document.createElement('div');
                cartItem.className = 'pos-cart-item';
                cartItem.innerHTML = `
                    <div class="pos-cart-item-remove" data-index="${index}">
                        <i class="fas fa-times"></i>
                    </div>
                    <div class="pos-cart-item-info">
                        <div class="fw-bold">${item.name}</div>
                        <div class="small text-muted">${item.notes ? item.notes : 'بدون ملاحظات'}</div>
                    </div>
                    <div class="pos-cart-item-quantity">
                        <button class="btn btn-sm btn-outline-secondary decrease-quantity" data-index="${index}">-</button>
                        <span class="mx-2">${item.quantity}</span>
                        <button class="btn btn-sm btn-outline-secondary increase-quantity" data-index="${index}">+</button>
                    </div>
                    <div class="text-end">
                        <div>${(item.price * item.quantity).toFixed(2)} ${currency}</div>
                    </div>
                `;
                cartItemsContainer.appendChild(cartItem);
            });

            // إضافة مستمعي الأحداث لأزرار الحذف والكمية
            document.querySelectorAll('.pos-cart-item-remove').forEach(btn => {
                btn.addEventListener('click', function() {
                    const index = parseInt(this.dataset.index);
                    cart.splice(index, 1);
                    updateCart();
                });
            });

            document.querySelectorAll('.decrease-quantity').forEach(btn => {
                btn.addEventListener('click', function() {
                    const index = parseInt(this.dataset.index);
                    if (cart[index].quantity > 1) {
                        cart[index].quantity--;
                        updateCart();
                    }
                });
            });

            document.querySelectorAll('.increase-quantity').forEach(btn => {
                btn.addEventListener('click', function() {
                    const index = parseInt(this.dataset.index);
                    cart[index].quantity++;
                    updateCart();
                });
            });

            updateTotals();
        }

        // تحديث المجاميع
        function updateTotals() {
            const subtotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);
            const tax = subtotal * taxRate; // استخدام معدل الضريبة من الإعدادات
            const discount = 0; // يمكن تعديله لاحقًا
            const total = subtotal + tax - discount;

            document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ' + currency;
            document.getElementById('tax').textContent = tax.toFixed(2) + ' ' + currency;
            document.getElementById('discount').textContent = discount.toFixed(2) + ' ' + currency;
            document.getElementById('total').textContent = total.toFixed(2) + ' ' + currency;
        }

        // إضافة منتج إلى السلة
        function addToCart(product, quantity, notes) {
            // التحقق مما إذا كان المنتج موجودًا بالفعل في السلة
            const existingItemIndex = cart.findIndex(item => item.id === product.id);

            if (existingItemIndex !== -1) {
                // تحديث الكمية إذا كان المنتج موجودًا بالفعل
                cart[existingItemIndex].quantity += quantity;
                if (notes) {
                    cart[existingItemIndex].notes = notes;
                }
            } else {
                // إضافة منتج جديد إلى السلة
                cart.push({
                    id: product.id,
                    name: product.name,
                    price: parseFloat(product.price),
                    quantity: quantity,
                    notes: notes
                });
            }

            updateCart();
        }

        // تصفية المنتجات حسب الفئة
        function filterProductsByCategory(categoryId) {
            const productCards = document.querySelectorAll('.pos-product-card');

            productCards.forEach(card => {
                if (categoryId === 'all' || card.dataset.category === categoryId) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // البحث عن المنتجات
        function searchProducts(query) {
            const productCards = document.querySelectorAll('.pos-product-card');
            const lowerCaseQuery = query.toLowerCase();

            productCards.forEach(card => {
                const productName = card.querySelector('.pos-product-name').textContent.toLowerCase();
                if (productName.includes(lowerCaseQuery)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // مسح السلة
        document.getElementById('clearCart').addEventListener('click', function() {
            if (confirm('هل أنت متأكد من رغبتك في مسح السلة؟')) {
                cart = [];
                updateCart();
            }
        });

        // اختيار الفئة
        document.querySelectorAll('.pos-category-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.pos-category-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                filterProductsByCategory(this.dataset.category);
            });
        });

        // البحث عن المنتجات
        document.getElementById('searchProduct').addEventListener('input', function() {
            searchProducts(this.value);
        });

        // النقر على بطاقة المنتج
        document.querySelectorAll('.pos-product-card').forEach(card => {
            card.addEventListener('click', function() {
                const productId = this.dataset.id;
                const productName = this.querySelector('.pos-product-name').textContent;
                const productPrice = parseFloat(this.querySelector('.pos-product-price').textContent);

                // فتح نافذة إضافة ملاحظات
                document.getElementById('productId').value = productId;
                document.getElementById('productQuantity').value = 1;
                document.getElementById('productNotes').value = '';
                document.getElementById('productNotesModalLabel').textContent = 'إضافة ' + productName;

                const productNotesModal = new bootstrap.Modal(document.getElementById('productNotesModal'));
                productNotesModal.show();

                // تخزين بيانات المنتج في عنصر الزر
                document.getElementById('addToCartBtn').dataset.name = productName;
                document.getElementById('addToCartBtn').dataset.price = productPrice;
            });
        });

        // إضافة المنتج إلى السلة بعد إدخال الملاحظات
        document.getElementById('addToCartBtn').addEventListener('click', function() {
            const productId = document.getElementById('productId').value;
            const productName = this.dataset.name;
            const productPrice = this.dataset.price;
            const quantity = parseInt(document.getElementById('productQuantity').value);
            const notes = document.getElementById('productNotes').value;

            addToCart({
                id: productId,
                name: productName,
                price: productPrice
            }, quantity, notes);

            bootstrap.Modal.getInstance(document.getElementById('productNotesModal')).hide();
        });

        // إلغاء إضافة المنتج
        document.getElementById('cancelProductNotesBtn').addEventListener('click', function() {
            // إغلاق نافذة إضافة ملاحظات المنتج
            bootstrap.Modal.getInstance(document.getElementById('productNotesModal')).hide();
        });

        // اختيار طاولة
        document.querySelectorAll('.table-card').forEach(card => {
            card.addEventListener('click', function() {
                document.querySelectorAll('.table-card').forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');
                selectedTable = this.dataset.name;
                selectedTableId = this.dataset.id;
            });
        });

        // تأكيد اختيار الطاولة
        document.getElementById('selectTableBtn').addEventListener('click', function() {
            if (selectedTable) {
                document.getElementById('selectedTable').textContent = selectedTable;
                bootstrap.Modal.getInstance(document.getElementById('tableModal')).hide();
            } else {
                alert('الرجاء اختيار طاولة');
            }
        });

        // إلغاء اختيار الطاولة
        document.getElementById('cancelTableBtn').addEventListener('click', function() {
            // إعادة تعيين الطاولة المحددة
            selectedTable = null;
            selectedTableId = null;

            // إغلاق نافذة اختيار الطاولة
            bootstrap.Modal.getInstance(document.getElementById('tableModal')).hide();
        });

        // التبديل بين أنواع الطلبات
        document.querySelectorAll('input[name="orderType"]').forEach(radio => {
            radio.addEventListener('change', function() {
                orderType = this.value;

                // إخفاء جميع خيارات الطلب
                document.querySelectorAll('.order-type-options').forEach(option => {
                    option.classList.add('d-none');
                });

                // إظهار الخيارات المناسبة لنوع الطلب
                if (orderType === 'dine_in') {
                    document.getElementById('dineInOptions').classList.remove('d-none');
                } else if (orderType === 'takeaway') {
                    document.getElementById('takeawayOptions').classList.remove('d-none');
                }
            });
        });

        // حفظ معلومات الطلب
        document.getElementById('saveOrderInfoBtn').addEventListener('click', function() {
            orderNotes = document.getElementById('orderNotes').value;
        });

        // عند فتح نافذة معلومات الطلب الخارجي
        document.getElementById('takeawayInfoModal').addEventListener('show.bs.modal', function() {
            // نقل القيم من الحقول المخفية إلى حقول النافذة المنبثقة
            document.getElementById('modalCustomerPhone').value = customerPhone;
            document.getElementById('modalCarNumber').value = carNumber;
        });

        // حفظ معلومات الطلب الخارجي
        document.getElementById('saveTakeawayInfoBtn').addEventListener('click', function() {
            // نقل القيم من حقول النافذة المنبثقة إلى المتغيرات والحقول المخفية
            customerPhone = document.getElementById('modalCustomerPhone').value;
            carNumber = document.getElementById('modalCarNumber').value;

            // تحديث الحقول المخفية
            document.getElementById('customerPhone').value = customerPhone;
            document.getElementById('carNumber').value = carNumber;

            // إظهار رسالة تأكيد إذا تم إدخال معلومات
            if (customerPhone || carNumber) {
                const infoBtn = document.getElementById('openTakeawayInfoBtn');
                infoBtn.classList.add('btn-success');
                infoBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> <small>تم إضافة معلومات للطلب</small>';
            } else {
                const infoBtn = document.getElementById('openTakeawayInfoBtn');
                infoBtn.classList.remove('btn-success');
                infoBtn.classList.add('btn-outline-primary');
                infoBtn.innerHTML = '<i class="fas fa-info-circle me-1"></i> <small>إضافة معلومات للطلب الخارجي</small>';
            }
        });

        // التحقق من صحة الطلب
        function validateOrder() {
            console.log('🔍 بدء التحقق من صحة الطلب...');
            console.log('📋 نوع الطلب:', orderType);
            console.log('🏠 معرف الطاولة المختارة:', selectedTableId);
            console.log('🛒 عدد العناصر في السلة:', cart.length);

            if (orderType === 'dine_in' && !selectedTableId) {
                console.log('❌ فشل التحقق: لم يتم اختيار طاولة للطلب الداخلي');
                alert('الرجاء اختيار طاولة أولاً');
                return false;
            }

            if (cart.length === 0) {
                console.log('❌ فشل التحقق: السلة فارغة');
                alert('السلة فارغة، الرجاء إضافة منتجات');
                return false;
            }

            console.log('✅ تم التحقق من صحة الطلب بنجاح');
            return true;
        }

        // حفظ الطلب كمسودة
        function saveDraft() {
            console.log('💾 بدء حفظ المسودة...');

            // للمسودات، نحفظها كطلبات خارجية لتجنب مشكلة الطاولة المطلوبة
            let draftNotes = orderNotes || 'مسودة طلب';

            // إضافة معلومات الطاولة إذا كان الطلب داخلي
            if (orderType === 'dine_in' && selectedTable && selectedTable.name) {
                draftNotes += ` | الطاولة المقصودة: ${selectedTable.name}`;
            } else if (orderType === 'dine_in' && selectedTableId) {
                draftNotes += ` | رقم الطاولة: ${selectedTableId}`;
            }

            const orderData = {
                order_type: 'takeaway', // حفظ المسودات كطلبات خارجية
                customer_name: 'مسودة - ' + (new Date().toLocaleString('ar-SA')),
                customer_phone: customerPhone || null,
                car_number: carNumber || null,
                notes: draftNotes,
                status: 'draft', // حالة الطلب مسودة
                products: cart.map(item => ({
                    id: item.id,
                    quantity: item.quantity,
                    notes: item.notes
                }))
            };

            console.log('📦 بيانات المسودة المرسلة:', orderData);

            // إرسال الطلب إلى الخادم
            console.log('📡 إرسال طلب حفظ المسودة إلى:', '{{ route("waiter.orders.create") }}');
            fetch('{{ route("waiter.orders.create") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify(orderData)
            })
            .then(response => {
                console.log('📡 استجابة الخادم - الحالة:', response.status);
                console.log('📄 استجابة الخادم - نوع المحتوى:', response.headers.get('content-type'));

                if (!response.ok) {
                    console.error('❌ خطأ في الاستجابة:', response.status, response.statusText);
                    throw new Error('خطأ في الشبكة: ' + response.status);
                }

                return response.json();
            })
            .then(data => {
                console.log('✅ استجابة حفظ المسودة:', data);

                if (data.success) {
                    alert('✅ تم حفظ المسودة بنجاح!\nرقم الطلب: ' + data.order.id);

                    // إعادة تعيين بيانات الطلب
                    resetOrderData();
                } else {
                    console.error('❌ فشل في حفظ المسودة:', data.message);
                    alert('❌ فشل في حفظ المسودة: ' + (data.message || 'خطأ غير معروف'));
                }
            })
            .catch(error => {
                console.error('❌ خطأ في حفظ المسودة:', error);
                alert('❌ حدث خطأ أثناء حفظ المسودة: ' + error.message);
            });
        }

        // تعليق الطلب
        function suspendOrder(customerName, notes) {
            console.log('⏸️ بدء تعليق الطلب...');
            console.log('👤 اسم العميل:', customerName);
            console.log('📝 الملاحظات:', notes);

            // تجهيز بيانات الطلب
            const orderData = {
                order_type: orderType,
                table_id: orderType === 'dine_in' ? selectedTableId : null,
                customer_name: customerName,
                customer_phone: orderType !== 'dine_in' ? customerPhone : null,
                car_number: orderType !== 'dine_in' ? carNumber : null,
                notes: notes || orderNotes,
                status: 'suspended', // حالة الطلب معلق
                products: cart.map(item => ({
                    id: item.id,
                    quantity: item.quantity,
                    notes: item.notes
                }))
            };

            console.log('📦 بيانات الطلب المرسلة:', orderData);

            // إرسال الطلب إلى الخادم
            console.log('📡 إرسال طلب التعليق إلى:', '{{ route("waiter.orders.suspend") }}');
            fetch('{{ route("waiter.orders.suspend") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify(orderData)
            })
            .then(response => {
                console.log('📡 استجابة الخادم - الحالة:', response.status);
                console.log('📄 استجابة الخادم - نوع المحتوى:', response.headers.get('content-type'));

                if (!response.ok) {
                    console.error('❌ خطأ في الاستجابة:', response.status, response.statusText);
                    throw new Error('خطأ في الشبكة: ' + response.status);
                }

                return response.json();
            })
            .then(data => {
                console.log('📋 بيانات الاستجابة:', data);

                if (data.success) {
                    console.log('✅ تم تعليق الطلب بنجاح');
                    alert('تم تعليق الطلب بنجاح. يمكنك العودة إليه لاحقاً من قائمة الطلبات النشطة.');
                    resetOrderData();
                } else {
                    console.error('❌ فشل في تعليق الطلب:', data.message);
                    alert('حدث خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('💥 خطأ في تعليق الطلب:', error);
                console.error('📋 تفاصيل الخطأ:', error.message);
                alert('حدث خطأ أثناء تعليق الطلب: ' + error.message);
            });
        }

        // متغير لتخزين روابط الطباعة
        let printUrls = {};

        // إرسال الطلب إلى الخادم
        function submitOrder(paymentMethod) {
            // تجهيز بيانات الطلب
            const orderData = {
                order_type: orderType,
                table_id: orderType === 'dine_in' ? selectedTableId : null,
                customer_phone: orderType !== 'dine_in' ? customerPhone : null,
                car_number: orderType !== 'dine_in' ? carNumber : null,
                notes: orderNotes,
                payment_method: paymentMethod,
                products: cart.map(item => ({
                    id: item.id,
                    quantity: item.quantity,
                    notes: item.notes
                }))
            };

            // إرسال الطلب إلى الخادم
            fetch('{{ route("waiter.orders.create") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify(orderData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // معالجة مختلفة حسب نوع الطلب
                    if (data.order_type === 'takeaway' && data.print_urls) {
                        // للطلبات الخارجية: عرض نافذة اختيار مقاس الطباعة
                        printUrls = data.print_urls;
                        const printSizeModal = new bootstrap.Modal(document.getElementById('printSizeModal'));
                        printSizeModal.show();
                    } else if (data.order_type === 'dine_in') {
                        // للطلبات داخل المطعم: عرض رسالة نجاح وإعادة تعيين البيانات
                        alert(data.message);
                        resetOrderData();
                    } else {
                        // للحالات الأخرى
                        alert('تم إنشاء الطلب بنجاح');
                        resetOrderData();
                    }
                } else {
                    alert('حدث خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء إرسال الطلب');
            });
        }

        // إعادة تعيين بيانات الطلب
        function resetOrderData() {
            // إعادة تعيين السلة والبيانات المحددة
            cart = [];
            updateCart();

            // إعادة تعيين البيانات حسب نوع الطلب
            if (orderType === 'dine_in') {
                selectedTable = null;
                selectedTableId = null;
                document.getElementById('selectedTable').textContent = 'اختر طاولة';
            } else if (orderType === 'takeaway') {
                document.getElementById('customerPhone').value = '';
                document.getElementById('carNumber').value = '';
                customerPhone = '';
                carNumber = '';
            }

            // إعادة تعيين ملاحظات الطلب
            orderNotes = '';
            document.getElementById('orderNotes').value = '';

            // إعادة تحميل الصفحة لتحديث حالة الطاولات
            window.location.reload();
        }

        // الدفع بالبطاقة
        document.getElementById('cardBtn').addEventListener('click', function() {
            if (!validateOrder()) {
                return;
            }

            if (confirm('هل أنت متأكد من إتمام الدفع بالبطاقة؟')) {
                submitOrder('card');
            }
        });

        // فتح نافذة الدفع النقدي
        document.getElementById('cashBtn').addEventListener('click', function() {
            if (!validateOrder()) {
                return;
            }

            // حساب إجمالي الفاتورة
            const subtotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);
            const tax = subtotal * taxRate; // استخدام معدل الضريبة من الإعدادات
            const total = subtotal + tax;

            // عرض إجمالي الفاتورة في النافذة
            document.getElementById('modalTotalAmount').textContent = total.toFixed(2) + ' ' + currency;

            // إعادة تعيين حقل المبلغ المدفوع والمبلغ المسترجع
            document.getElementById('amountPaid').value = '';
            document.getElementById('changeAmount').textContent = '0.00 ' + currency;

            // فتح نافذة الدفع النقدي
            const cashPaymentModal = new bootstrap.Modal(document.getElementById('cashPaymentModal'));
            cashPaymentModal.show();
        });

        // حساب المبلغ المسترجع عند تغيير المبلغ المدفوع
        document.getElementById('amountPaid').addEventListener('input', function() {
            const totalAmount = parseFloat(document.getElementById('modalTotalAmount').textContent);
            const amountPaid = parseFloat(this.value) || 0;
            const changeAmount = amountPaid - totalAmount;

            document.getElementById('changeAmount').textContent = changeAmount >= 0 ? changeAmount.toFixed(2) + ' ' + currency : '0.00 ' + currency;

            // تغيير لون المبلغ المسترجع حسب القيمة
            if (changeAmount < 0) {
                document.getElementById('changeAmount').className = 'fw-bold text-danger';
            } else {
                document.getElementById('changeAmount').className = 'fw-bold text-success';
            }
        });

        // تأكيد الدفع وإرسال الطلب
        document.getElementById('confirmPaymentBtn').addEventListener('click', function() {
            const totalAmount = parseFloat(document.getElementById('modalTotalAmount').textContent);
            const amountPaid = parseFloat(document.getElementById('amountPaid').value) || 0;

            if (amountPaid < totalAmount) {
                alert('المبلغ المدفوع أقل من إجمالي الفاتورة');
                return;
            }

            // إغلاق نافذة الدفع النقدي
            bootstrap.Modal.getInstance(document.getElementById('cashPaymentModal')).hide();

            // إرسال الطلب مع طريقة الدفع النقدي
            submitOrder('cash');
        });

        // إلغاء الدفع النقدي
        document.getElementById('cancelCashPaymentBtn').addEventListener('click', function() {
            // إغلاق نافذة الدفع النقدي
            bootstrap.Modal.getInstance(document.getElementById('cashPaymentModal')).hide();
        });

        // إضافة مستمعي الأحداث لبطاقات مقاس الطباعة
        document.querySelectorAll('.print-size-card').forEach(card => {
            card.addEventListener('click', function() {
                const size = this.getAttribute('data-size');

                // فتح صفحة الطباعة في نافذة جديدة إذا تم اختيار الطباعة
                if (size === '85mm' && printUrls['85mm']) {
                    window.open(printUrls['85mm'], '_blank');
                }
                // في حالة اختيار "بدون طباعة" أو أي خيار آخر، لا نقوم بفتح نافذة الطباعة

                // إغلاق النافذة المنبثقة
                bootstrap.Modal.getInstance(document.getElementById('printSizeModal')).hide();

                // عرض رسالة نجاح
                alert('تم إنشاء الطلب بنجاح');

                // إعادة تعيين بيانات الطلب
                resetOrderData();
            });
        });

        // إضافة مستمع الحدث لزر الإلغاء في نافذة الطباعة
        document.getElementById('cancelPrintBtn').addEventListener('click', function() {
            // إغلاق النافذة المنبثقة
            bootstrap.Modal.getInstance(document.getElementById('printSizeModal')).hide();

            // عرض رسالة نجاح
            alert('تم إنشاء الطلب بنجاح');

            // إعادة تعيين بيانات الطلب
            resetOrderData();
        });

        // إضافة مستمع الحدث لزر الإلغاء الرئيسي
        document.getElementById('cancelBtn').addEventListener('click', function() {
            if (cart.length === 0) {
                return;
            }

            if (confirm('هل أنت متأكد من رغبتك في إلغاء الطلب الحالي؟')) {
                resetOrderData();
            }
        });

        // متغير لتخزين الطلب النشط الحالي
        let currentActiveOrder = null;

        // دالة لتحميل الطلبات النشطة
        function loadActiveOrders() {
            console.log('جاري تحميل الطلبات النشطة...');
            console.log('URL:', '{{ route("waiter.orders.active") }}');

            return new Promise((resolve, reject) => {
                fetch('{{ route("waiter.orders.active") }}')
                    .then(response => {
                        console.log('استجابة الخادم:', response);
                        if (!response.ok) {
                            throw new Error('استجابة الخادم غير ناجحة: ' + response.status);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('بيانات الطلبات النشطة:', data);
                        const tableBody = document.querySelector('#activeOrdersTable tbody');
                        tableBody.innerHTML = '';

                    if (data.orders && data.orders.length > 0) {
                        document.getElementById('activeOrdersTable').classList.remove('d-none');
                        document.getElementById('noActiveOrders').classList.add('d-none');

                        data.orders.forEach(order => {
                            const row = document.createElement('tr');

                            // تنسيق التاريخ والوقت
                            const orderDate = new Date(order.created_at);
                            const formattedDate = orderDate.toLocaleDateString('ar-SA') + ' ' + orderDate.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });

                            // تنسيق الحالة
                            let statusBadge = '';
                            if (order.status === 'pending') {
                                statusBadge = '<span class="badge bg-warning">قيد الانتظار</span>';
                            } else if (order.status === 'in_progress') {
                                statusBadge = '<span class="badge bg-info">قيد التنفيذ</span>';
                            } else if (order.status === 'suspended') {
                                statusBadge = '<span class="badge bg-secondary">معلق</span>';
                            } else if (order.status === 'draft') {
                                statusBadge = '<span class="badge bg-primary">مسودة</span>';
                            } else if (order.status === 'completed') {
                                statusBadge = '<span class="badge bg-success">مكتمل</span>';
                            } else if (order.status === 'cancelled') {
                                statusBadge = '<span class="badge bg-danger">ملغي</span>';
                            }

                            // إضافة عمود النادل للمدير والكاشير فقط
                            const waiterColumn = @json(auth()->user()->hasRole('admin') || auth()->user()->hasRole('manager') || auth()->user()->hasRole('cashier'))
                                ? `<td>${order.user ? order.user.name : '-'}</td>`
                                : '';

                            // تحديد نوع الطلب للعرض
                            const tableDisplay = order.table ? order.table.name :
                                (order.order_type === 'takeaway' ? 'طلب خارجي' : '-');

                            row.innerHTML = `
                                <td>${order.id}</td>
                                <td>${tableDisplay}</td>
                                <td>${formattedDate}</td>
                                <td>${isNaN(parseFloat(order.total_amount)) ? '0.00' : parseFloat(order.total_amount).toFixed(2)} {{ \App\Models\Setting::get('currency', 'ر.س') }}</td>
                                <td>${statusBadge}</td>
                                ${waiterColumn}
                                <td>
                                    <button class="btn btn-sm btn-info view-order-btn" data-id="${order.id}">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    ${order.status === 'draft' ?
                                        `<button class="btn btn-sm btn-warning activate-draft-btn" data-id="${order.id}" title="تحويل إلى معلق">
                                            <i class="fas fa-arrow-right"></i>
                                        </button>` :
                                        `<button class="btn btn-sm btn-success pay-order-btn" data-id="${order.id}" title="دفع الطلب">
                                            <i class="fas fa-money-bill-wave"></i>
                                        </button>`
                                    }
                                </td>
                            `;

                            tableBody.appendChild(row);
                        });

                        // إضافة مستمعي الأحداث لأزرار العرض والدفع
                        document.querySelectorAll('.view-order-btn').forEach(btn => {
                            btn.addEventListener('click', function() {
                                const orderId = this.getAttribute('data-id');
                                viewOrderDetails(orderId);
                            });
                        });

                        document.querySelectorAll('.pay-order-btn').forEach(btn => {
                            btn.addEventListener('click', function() {
                                const orderId = this.getAttribute('data-id');
                                payActiveOrder(orderId);
                            });
                        });

                        document.querySelectorAll('.activate-draft-btn').forEach(btn => {
                            btn.addEventListener('click', function() {
                                const orderId = this.getAttribute('data-id');
                                activateDraft(orderId);
                            });
                        });
                    } else {
                        document.getElementById('activeOrdersTable').classList.add('d-none');
                        document.getElementById('noActiveOrders').classList.remove('d-none');
                    }
                        resolve(data);
                    })
                    .catch(error => {
                        console.error('خطأ في تحميل الطلبات النشطة:', error);
                        console.error('تفاصيل الخطأ:', error.stack);
                        alert('حدث خطأ أثناء تحميل الطلبات النشطة: ' + error.message);
                        reject(error);
                    });
            });
        }

        // دالة لعرض تفاصيل الطلب
        function viewOrderDetails(orderId) {
            fetch(`{{ url('waiter/orders') }}/${orderId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.order) {
                        currentActiveOrder = data.order;

                        // تعبئة بيانات الطلب
                        document.getElementById('orderDetailId').textContent = data.order.id;

                        // عرض الطاولة أو نوع الطلب
                        const tableDisplay = data.order.table ? data.order.table.name :
                            (data.order.order_type === 'takeaway' ? 'طلب خارجي' : '-');
                        document.getElementById('orderDetailTable').textContent = tableDisplay;

                        document.getElementById('orderDetailCustomerName').textContent = data.order.customer_name || '-';

                        // تنسيق التاريخ والوقت
                        const orderDate = new Date(data.order.created_at);
                        document.getElementById('orderDetailTime').textContent = orderDate.toLocaleDateString('ar-SA') + ' ' + orderDate.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });

                        // تنسيق الحالة
                        let statusText = '';
                        if (data.order.status === 'pending') {
                            statusText = 'قيد الانتظار';
                        } else if (data.order.status === 'in_progress') {
                            statusText = 'قيد التنفيذ';
                        } else if (data.order.status === 'suspended') {
                            statusText = 'معلق';
                        } else if (data.order.status === 'draft') {
                            statusText = 'مسودة';
                        } else if (data.order.status === 'completed') {
                            statusText = 'مكتمل';
                        } else if (data.order.status === 'cancelled') {
                            statusText = 'ملغي';
                        }
                        document.getElementById('orderDetailStatus').textContent = statusText;

                        document.getElementById('orderDetailWaiter').textContent = data.order.user ? data.order.user.name : '-';

                        // عرض الملاحظات مع معلومات إضافية للطلبات الخارجية
                        let notesText = data.order.notes || '-';
                        if (data.order.order_type === 'takeaway') {
                            const additionalInfo = [];
                            if (data.order.customer_phone) {
                                additionalInfo.push(`الهاتف: ${data.order.customer_phone}`);
                            }
                            if (data.order.car_number) {
                                additionalInfo.push(`رقم السيارة: ${data.order.car_number}`);
                            }
                            if (additionalInfo.length > 0) {
                                notesText = (notesText === '-' ? '' : notesText + ' | ') + additionalInfo.join(' | ');
                            }
                        }
                        document.getElementById('orderDetailNotes').textContent = notesText;

                        // تعبئة جدول المنتجات
                        const tableBody = document.querySelector('#orderItemsTable tbody');
                        tableBody.innerHTML = '';

                        let subtotal = 0;

                        if (data.order.order_items && data.order.order_items.length > 0) {
                            data.order.order_items.forEach((item, index) => {
                                const row = document.createElement('tr');

                                // التأكد من أن الأسعار أرقام صالحة
                                const unitPrice = parseFloat(item.unit_price);
                                const quantity = parseInt(item.quantity);
                                const itemTotal = unitPrice * quantity;

                                // إضافة المجموع إلى المجموع الفرعي
                                subtotal += isNaN(itemTotal) ? 0 : itemTotal;

                                row.innerHTML = `
                                    <td>${index + 1}</td>
                                    <td>${item.product ? item.product.name : 'منتج محذوف'}</td>
                                    <td>${isNaN(unitPrice) ? '0.00' : unitPrice.toFixed(2)} ر.س</td>
                                    <td>${isNaN(quantity) ? '0' : quantity}</td>
                                    <td>${isNaN(itemTotal) ? '0.00' : itemTotal.toFixed(2)} ر.س</td>
                                    <td>${item.notes || '-'}</td>
                                `;

                                tableBody.appendChild(row);
                            });
                        }

                        // حساب المجاميع
                        const tax = subtotal * taxRate; // استخدام معدل الضريبة من الإعدادات
                        const total = subtotal + tax;

                        // التأكد من أن المبالغ أرقام صالحة
                        const formatAmount = (amount) => {
                            return isNaN(amount) ? '0.00' : amount.toFixed(2);
                        };

                        document.getElementById('orderDetailSubtotal').textContent = formatAmount(subtotal) + ' ر.س';
                        document.getElementById('orderDetailTax').textContent = formatAmount(tax) + ' ر.س';
                        document.getElementById('orderDetailTotal').textContent = formatAmount(total) + ' ر.س';

                        // إخفاء نافذة الطلبات النشطة وإظهار نافذة تفاصيل الطلب
                        bootstrap.Modal.getInstance(document.getElementById('activeOrdersModal')).hide();
                        const orderDetailsModal = new bootstrap.Modal(document.getElementById('activeOrderDetailsModal'));
                        orderDetailsModal.show();
                    } else {
                        alert('لم يتم العثور على الطلب');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء تحميل تفاصيل الطلب');
                });
        }

        // دالة لتفعيل مسودة (تحويلها إلى معلقة)
        function activateDraft(orderId) {
            if (confirm('هل أنت متأكد من رغبتك في تفعيل هذه المسودة؟\nسيتم تحويلها إلى طلب معلق في انتظار الدفع.')) {
                fetch(`{{ url('waiter/orders') }}/${orderId}/status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        status: 'suspended',
                        _method: 'PUT'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم تفعيل المسودة بنجاح!\nالطلب الآن معلق في انتظار الدفع.');

                        // إعادة تحميل الطلبات النشطة
                        loadActiveOrders();

                        // إغلاق نافذة تفاصيل الطلب إذا كانت مفتوحة
                        const orderDetailModal = bootstrap.Modal.getInstance(document.getElementById('orderDetailModal'));
                        if (orderDetailModal) {
                            orderDetailModal.hide();
                        }
                    } else {
                        alert('حدث خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء تفعيل المسودة');
                });
            }
        }

        // دالة لدفع الطلب النشط
        function payActiveOrder(orderId) {
            if (confirm('هل أنت متأكد من رغبتك في دفع هذا الطلب؟')) {
                fetch(`{{ url('waiter/orders') }}/${orderId}/pay`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({
                        payment_method: 'cash'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم دفع الطلب بنجاح');

                        // إذا كانت نافذة تفاصيل الطلب مفتوحة، نغلقها
                        if (document.getElementById('activeOrderDetailsModal').classList.contains('show')) {
                            bootstrap.Modal.getInstance(document.getElementById('activeOrderDetailsModal')).hide();
                        }

                        // تحديث قائمة الطلبات النشطة
                        loadActiveOrders();

                        // فتح نافذة الطباعة إذا كانت متوفرة
                        if (data.print_urls && data.print_urls['85mm']) {
                            if (confirm('هل ترغب في طباعة الفاتورة؟')) {
                                window.open(data.print_urls['85mm'], '_blank');
                            }
                        }
                    } else {
                        alert('حدث خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء دفع الطلب');
                });
            }
        }

        // إضافة مستمع الحدث لزر تحديث الطلبات النشطة
        document.getElementById('refreshActiveOrdersBtn').addEventListener('click', function() {
            loadActiveOrders();
        });

        // إضافة مستمع الحدث لزر الرجوع في نافذة تفاصيل الطلب
        document.getElementById('backToActiveOrdersBtn').addEventListener('click', function() {
            bootstrap.Modal.getInstance(document.getElementById('activeOrderDetailsModal')).hide();
            const activeOrdersModal = new bootstrap.Modal(document.getElementById('activeOrdersModal'));
            activeOrdersModal.show();
        });

        // إضافة مستمع الحدث لزر دفع الطلب في نافذة تفاصيل الطلب
        document.getElementById('payActiveOrderBtn').addEventListener('click', function() {
            if (currentActiveOrder) {
                payActiveOrder(currentActiveOrder.id);
            }
        });

        // إضافة مستمع الحدث لزر إضافة أصناف في نافذة تفاصيل الطلب
        document.getElementById('addItemsToOrderBtn').addEventListener('click', function() {
            if (currentActiveOrder) {
                // تعبئة بيانات الطلب في نافذة إضافة الأصناف
                document.getElementById('addItemsOrderId').textContent = currentActiveOrder.id;
                document.getElementById('addItemsOrderTable').textContent = currentActiveOrder.table ? currentActiveOrder.table.name : '-';
                document.getElementById('addItemsOrderCustomer').textContent = currentActiveOrder.customer_name || '-';

                // تنسيق الحالة
                let statusText = '';
                if (currentActiveOrder.status === 'pending') {
                    statusText = 'قيد الانتظار';
                } else if (currentActiveOrder.status === 'in_progress') {
                    statusText = 'قيد التنفيذ';
                } else if (currentActiveOrder.status === 'suspended') {
                    statusText = 'معلق';
                }
                document.getElementById('addItemsOrderStatus').textContent = statusText;

                // إخفاء نافذة تفاصيل الطلب وإظهار نافذة إضافة الأصناف
                bootstrap.Modal.getInstance(document.getElementById('activeOrderDetailsModal')).hide();
                const addItemsModal = new bootstrap.Modal(document.getElementById('addItemsModal'));
                addItemsModal.show();
            }
        });

        // إضافة مستمع الحدث لزر إلغاء إضافة الأصناف
        document.getElementById('cancelAddItemsBtn').addEventListener('click', function() {
            bootstrap.Modal.getInstance(document.getElementById('addItemsModal')).hide();
            const orderDetailsModal = new bootstrap.Modal(document.getElementById('activeOrderDetailsModal'));
            orderDetailsModal.show();
        });

        // إضافة مستمعي الأحداث لأزرار التصنيفات في نافذة إضافة الأصناف
        document.querySelectorAll('.add-items-category-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // إزالة الفئة النشطة من جميع الأزرار
                document.querySelectorAll('.add-items-category-btn').forEach(b => b.classList.remove('active'));
                // إضافة الفئة النشطة للزر المحدد
                this.classList.add('active');

                const category = this.getAttribute('data-category');

                // عرض/إخفاء المنتجات حسب الفئة المحددة
                document.querySelectorAll('.add-items-product-card').forEach(card => {
                    if (category === 'all' || card.getAttribute('data-category') === category) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        });

        // إضافة مستمع الحدث لحقل البحث في نافذة إضافة الأصناف
        document.getElementById('addItemsSearchProduct').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();

            document.querySelectorAll('.add-items-product-card').forEach(card => {
                const productName = card.querySelector('.add-items-product-name').textContent.toLowerCase();

                if (productName.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });

        // إضافة مستمعي الأحداث لبطاقات المنتجات في نافذة إضافة الأصناف
        document.querySelectorAll('.add-items-product-card').forEach(card => {
            card.addEventListener('click', function() {
                const productId = this.getAttribute('data-id');

                // تعبئة بيانات المنتج في نافذة إضافة ملاحظات للمنتج
                document.getElementById('addItemProductId').value = productId;
                document.getElementById('addItemQuantity').value = 1;
                document.getElementById('addItemNotes').value = '';

                // إخفاء نافذة إضافة الأصناف وإظهار نافذة إضافة ملاحظات للمنتج
                bootstrap.Modal.getInstance(document.getElementById('addItemsModal')).hide();
                const addItemNotesModal = new bootstrap.Modal(document.getElementById('addItemNotesModal'));
                addItemNotesModal.show();
            });
        });

        // إضافة مستمع الحدث لزر إلغاء إضافة ملاحظات للمنتج
        document.getElementById('cancelAddItemNotesBtn').addEventListener('click', function() {
            bootstrap.Modal.getInstance(document.getElementById('addItemNotesModal')).hide();
            const addItemsModal = new bootstrap.Modal(document.getElementById('addItemsModal'));
            addItemsModal.show();
        });

        // إضافة مستمع الحدث لزر تأكيد إضافة المنتج
        document.getElementById('confirmAddItemBtn').addEventListener('click', function() {
            const productId = document.getElementById('addItemProductId').value;
            const quantity = document.getElementById('addItemQuantity').value;
            const notes = document.getElementById('addItemNotes').value;

            if (!productId || !quantity || quantity < 1) {
                alert('الرجاء إدخال كمية صحيحة');
                return;
            }

            // إضافة المنتج إلى الطلب
            addProductToOrder(currentActiveOrder.id, productId, quantity, notes);
        });

        // دالة لإضافة منتج إلى طلب
        function addProductToOrder(orderId, productId, quantity, notes) {
            fetch(`{{ url('waiter/orders') }}/${orderId}/add-product`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    product_id: productId,
                    quantity: quantity,
                    notes: notes
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تمت إضافة المنتج إلى الطلب بنجاح');

                    // إغلاق نافذة إضافة ملاحظات للمنتج
                    bootstrap.Modal.getInstance(document.getElementById('addItemNotesModal')).hide();

                    // تحديث الطلب الحالي
                    currentActiveOrder = data.order;

                    // عرض تفاصيل الطلب المحدث
                    viewOrderDetails(orderId);
                } else {
                    alert('حدث خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء إضافة المنتج إلى الطلب');
            });
        }

        // تحميل الطلبات النشطة عند فتح النافذة
        document.getElementById('activeOrdersBtn').addEventListener('click', function() {
            loadActiveOrders();
        });

        // إضافة مستمع الحدث لزر الإعدادات
        document.querySelector('.pos-actions .btn:nth-child(6)').addEventListener('click', function() {
            const settingsModal = new bootstrap.Modal(document.getElementById('settingsModal'));
            settingsModal.show();
        });

        // إضافة مستمع الحدث لزر الإشعارات
        const notificationBtn = document.getElementById('notificationsBtn');
        if (notificationBtn) {
            console.log('تم العثور على زر الإشعارات بـ ID');
            notificationBtn.addEventListener('click', function() {
                console.log('تم النقر على زر الإشعارات');
                loadNotifications();
            });
        } else {
            console.error('لم يتم العثور على زر الإشعارات بـ ID');
            // محاولة البحث بطريقة أخرى
            const allBtns = document.querySelectorAll('.pos-actions .btn');
            console.log('عدد الأزرار الموجودة:', allBtns.length);
            allBtns.forEach((btn, index) => {
                console.log(`الزر ${index + 1}:`, btn);
                if (btn.querySelector('.fa-bell')) {
                    console.log('وجدت زر الإشعارات في الموضع:', index + 1);
                    btn.addEventListener('click', function() {
                        console.log('تم النقر على زر الإشعارات (fallback)');
                        loadNotifications();
                        const notificationsModal = new bootstrap.Modal(document.getElementById('notificationsModal'));
                        notificationsModal.show();
                    });
                }
            });
        }

        // إضافة مستمع الحدث لزر الإلغاء
        document.querySelector('.pos-actions .btn:nth-child(3)').addEventListener('click', function() {
            const cancelOrdersModal = new bootstrap.Modal(document.getElementById('cancelOrdersModal'));
            cancelOrdersModal.show();
        });

        // إضافة مستمع الحدث لزر الآلة الحاسبة
        document.querySelector('.pos-actions .btn:nth-child(4)').addEventListener('click', function() {
            // إعادة تعيين شاشة الآلة الحاسبة
            document.getElementById('calculatorDisplay').value = '';

            const calculatorModal = new bootstrap.Modal(document.getElementById('calculatorModal'));
            calculatorModal.show();
        });

        // إضافة مستمع الحدث لزر الطباعة
        document.querySelector('.pos-actions .btn:nth-child(5)').addEventListener('click', function() {
            const printOptionsModal = new bootstrap.Modal(document.getElementById('printOptionsModal'));
            printOptionsModal.show();
        });

        // دالة لتحميل الإشعارات
        function loadNotifications() {
            console.log('🚀 بدء تحميل الإشعارات...');
            console.log('👤 المستخدم الحالي:', '{{ auth()->user()->name ?? "غير مسجل دخول" }}');
            console.log('🆔 معرف المستخدم:', '{{ auth()->id() ?? "غير محدد" }}');
            console.log('🎭 دور المستخدم:', '{{ auth()->user()->role_name }}');
            const notificationsList = document.getElementById('notificationsList');
            const noNotifications = document.getElementById('noNotifications');

            if (!notificationsList) {
                console.error('عنصر notificationsList غير موجود في DOM');
                return;
            }
            if (!noNotifications) {
                console.error('عنصر noNotifications غير موجود في DOM');
                return;
            }

            console.log('عناصر DOM موجودة بنجاح');

            // إعادة تعيين قائمة الإشعارات
            notificationsList.innerHTML = '';

            // عرض مؤشر التحميل
            notificationsList.innerHTML = `
                <div class="text-center py-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل الإشعارات...</p>
                </div>
            `;

            // تحميل الإشعارات من الخادم
            console.log('🔗 URL الإشعارات النادل:', '{{ route("waiter.notifications.get") }}');
            console.log('🔄 بدء طلب الإشعارات...');

            fetch('{{ route("waiter.notifications.get") }}', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                credentials: 'same-origin'
            })
                .then(response => {
                    console.log('📡 استجابة الخادم - الحالة:', response.status);
                    console.log('📄 استجابة الخادم - نوع المحتوى:', response.headers.get('content-type'));
                    console.log('🔗 استجابة الخادم - URL:', response.url);

                    if (!response.ok) {
                        console.error('❌ خطأ في الاستجابة:', response.status, response.statusText);
                        throw new Error('خطأ في الشبكة: ' + response.status);
                    }

                    console.log('✅ الاستجابة ناجحة، جاري تحليل JSON...');
                    return response.json();
                })
                .then(data => {
                    console.log('بيانات الإشعارات المستلمة:', data);
                    console.log('نوع البيانات:', typeof data);
                    console.log('هل البيانات ناجحة؟', data.success);
                    console.log('عدد الإشعارات:', data.notifications ? data.notifications.length : 'غير محدد');
                    console.log('معرف المستخدم من الاستجابة:', data.user_id);

                    // إعادة تعيين قائمة الإشعارات
                    notificationsList.innerHTML = '';

                    if (data.success && data.notifications && data.notifications.length > 0) {
                        console.log('✅ تم العثور على إشعارات، سيتم عرضها');

                        // تحديث عنوان modal الإشعارات لإظهار العدد الفعلي
                        const modalTitle = document.getElementById('notificationsModalLabel');
                        if (modalTitle) {
                            modalTitle.innerHTML = `
                                الإشعارات
                                <small class="text-muted">(${data.notifications.length} من آخر 15 إشعار)</small>
                            `;
                        }

                        // تحديث عدد الإشعارات غير المقروءة
                        const unreadCount = data.notifications.filter(notification => !notification.read).length;

                        // الحصول على العدد السابق للإشعارات غير المقروءة
                        const badge = document.getElementById('notificationsBadge');
                        const previousCount = badge.classList.contains('d-none') ? 0 : parseInt(badge.textContent);

                        // تحديث شارة الإشعارات
                        updateNotificationBadge(unreadCount);

                        // البحث عن الإشعارات الجديدة (التي تم إنشاؤها بعد آخر فحص)
                        const newNotifications = data.notifications.filter(notification => {
                            const notificationDate = new Date(notification.created_at).getTime();
                            return !notification.read && notificationDate > lastNotificationCheck;
                        });

                        // تشغيل صوت التنبيه إذا كان هناك إشعارات جديدة
                        if (newNotifications.length > 0 && localStorage.getItem('sounds') !== 'false') {
                            notificationSound.play();

                            // عرض إشعار المتصفح إذا كان مدعومًا
                            if ("Notification" in window && Notification.permission === "granted") {
                                // البحث عن إشعارات الطلبات الجاهزة للتسليم الجديدة
                                const newReadyOrders = newNotifications.filter(notification =>
                                    notification.type === 'order_ready'
                                );

                                if (newReadyOrders.length > 0) {
                                    // عرض إشعار لكل طلب جاهز للتسليم جديد
                                    newReadyOrders.forEach(notification => {
                                        try {
                                            const notificationData = JSON.parse(notification.data);
                                            let notificationBody = `الطلب رقم ${notificationData.order_id} جاهز للتسليم`;

                                            // إضافة رقم السيارة إذا كان موجودًا
                                            if (notificationData.car_number) {
                                                notificationBody += `\nرقم السيارة: ${notificationData.car_number}`;
                                            }

                                            new Notification("طلب جاهز للتسليم", {
                                                body: notificationBody,
                                                icon: "{{ asset('favicon.ico') }}"
                                            });
                                        } catch (error) {
                                            console.error('Error parsing notification data:', error);
                                        }
                                    });
                                } else if (newNotifications.length > 0) {
                                    // عرض إشعار عام إذا كانت هناك إشعارات جديدة ولكن ليست طلبات جاهزة للتسليم
                                    new Notification("إشعارات جديدة", {
                                        body: `لديك ${newNotifications.length} إشعار جديد`,
                                        icon: "{{ asset('favicon.ico') }}"
                                    });
                                }
                            }
                        }

                        // عرض الإشعارات
                        notificationsList.classList.remove('d-none');
                        noNotifications.classList.add('d-none');

                        // تحديث آخر وقت تم فيه عرض الإشعارات
                        updateLastNotificationCheck();

                        data.notifications.forEach(notification => {
                            // تنسيق التاريخ والوقت
                            const notificationDate = new Date(notification.created_at);
                            const now = new Date();
                            const diffTime = Math.abs(now - notificationDate);
                            const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

                            let timeAgo;
                            if (diffDays > 0) {
                                timeAgo = `منذ ${diffDays} يوم`;
                            } else {
                                const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
                                if (diffHours > 0) {
                                    timeAgo = `منذ ${diffHours} ساعة`;
                                } else {
                                    const diffMinutes = Math.floor(diffTime / (1000 * 60));
                                    timeAgo = `منذ ${diffMinutes} دقيقة`;
                                }
                            }

                            // إنشاء عنصر الإشعار
                            const notificationItem = document.createElement('a');
                            notificationItem.href = '#';
                            notificationItem.className = 'list-group-item list-group-item-action' + (notification.read ? '' : ' bg-light');
                            notificationItem.setAttribute('data-id', notification.id);
                            notificationItem.innerHTML = `
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">${notification.title}</h6>
                                    <small>${timeAgo}</small>
                                </div>
                                <p class="mb-1">${notification.message}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">اضغط للتفاصيل</small>
                                    ${notification.read ? '' : '<span class="badge bg-primary rounded-pill">جديد</span>'}
                                </div>
                            `;

                            // إضافة مستمع الحدث للنقر على الإشعار
                            notificationItem.addEventListener('click', function(event) {
                                event.preventDefault();
                                markNotificationAsRead(notification.id);

                                // إذا كان الإشعار من نوع "طلب جاهز للتسليم"
                                if (notification.type === 'order_ready') {
                                    try {
                                        const data = JSON.parse(notification.data);
                                        if (data.order_id) {
                                            // إضافة معلومات إضافية للطلبات الخارجية
                                            if (data.car_number) {
                                                // إضافة زر لنسخ رقم السيارة
                                                const copyBtn = document.createElement('button');
                                                copyBtn.className = 'btn btn-sm btn-outline-primary mt-2';
                                                copyBtn.innerHTML = `<i class="fas fa-copy me-1"></i> نسخ رقم السيارة: <strong>${data.car_number}</strong>`;
                                                copyBtn.addEventListener('click', function(e) {
                                                    e.preventDefault();
                                                    e.stopPropagation();
                                                    navigator.clipboard.writeText(data.car_number)
                                                        .then(() => {
                                                            // تغيير نص الزر مؤقتًا للإشارة إلى نجاح النسخ
                                                            const originalText = copyBtn.innerHTML;
                                                            copyBtn.innerHTML = '<i class="fas fa-check me-1"></i> تم النسخ!';
                                                            setTimeout(() => {
                                                                copyBtn.innerHTML = originalText;
                                                            }, 2000);
                                                        })
                                                        .catch(err => {
                                                            console.error('فشل نسخ النص: ', err);
                                                        });
                                                });

                                                // إضافة الزر إلى عنصر الإشعار
                                                const actionsDiv = notificationItem.querySelector('.d-flex.justify-content-between.align-items-center');
                                                actionsDiv.parentNode.insertBefore(copyBtn, actionsDiv.nextSibling);
                                            }

                                            // فتح تفاصيل الطلب
                                            notificationItem.addEventListener('dblclick', function() {
                                                getOrderDetails(data.order_id, data.car_number || null);
                                            });

                                            // إضافة زر لفتح تفاصيل الطلب
                                            const viewBtn = document.createElement('button');
                                            viewBtn.className = 'btn btn-sm btn-primary mt-2 me-2';
                                            viewBtn.innerHTML = '<i class="fas fa-eye me-1"></i> عرض تفاصيل الطلب';
                                            viewBtn.addEventListener('click', function(e) {
                                                e.preventDefault();
                                                e.stopPropagation();
                                                getOrderDetails(data.order_id, data.car_number || null);
                                            });

                                            // إضافة الزر إلى عنصر الإشعار
                                            const actionsDiv = notificationItem.querySelector('.d-flex.justify-content-between.align-items-center');
                                            actionsDiv.parentNode.insertBefore(viewBtn, actionsDiv.nextSibling);
                                        }
                                    } catch (error) {
                                        console.error('Error parsing notification data:', error);
                                    }
                                }
                            });

                            notificationsList.appendChild(notificationItem);
                        });
                    } else {
                        console.log('❌ لا توجد إشعارات أو فشل في الحصول على البيانات');
                        console.log('السبب:', !data.success ? 'فشل API' : 'لا توجد إشعارات');
                        if (!data.success) {
                            console.log('رسالة الخطأ:', data.message);
                        }

                        // تحديث عنوان modal الإشعارات عند عدم وجود إشعارات
                        const modalTitle = document.getElementById('notificationsModalLabel');
                        if (modalTitle) {
                            modalTitle.innerHTML = `
                                الإشعارات
                                <small class="text-muted">(0 من آخر 15 إشعار)</small>
                            `;
                        }

                        notificationsList.classList.add('d-none');
                        noNotifications.classList.remove('d-none');
                    }
                })
                .catch(error => {
                    console.error('💥 خطأ في تحميل الإشعارات من route الكاشير:', error);
                    console.log('🔄 جاري المحاولة مع route العام...');

                    // محاولة استخدام route العام كـ fallback
                    fetch('{{ route("notifications.get") }}', {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        credentials: 'same-origin'
                    })
                    .then(response => {
                        console.log('📡 fallback - حالة الاستجابة:', response.status);
                        if (!response.ok) {
                            throw new Error('خطأ في الشبكة: ' + response.status);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('✅ fallback - تم الحصول على البيانات:', data);

                        // إعادة تعيين قائمة الإشعارات
                        notificationsList.innerHTML = '';

                        if (data.success && data.notifications && data.notifications.length > 0) {
                            console.log('🎉 fallback - تم العثور على إشعارات');
                            // نفس منطق عرض الإشعارات
                            notificationsList.classList.remove('d-none');
                            noNotifications.classList.add('d-none');

                            data.notifications.forEach(notification => {
                                const notificationItem = document.createElement('a');
                                notificationItem.href = '#';
                                notificationItem.className = 'list-group-item list-group-item-action' + (notification.read ? '' : ' bg-light');
                                notificationItem.innerHTML = `
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">${notification.title}</h6>
                                        <small>منذ قليل</small>
                                    </div>
                                    <p class="mb-1">${notification.message}</p>
                                    ${notification.read ? '' : '<span class="badge bg-primary rounded-pill">جديد</span>'}
                                `;
                                notificationsList.appendChild(notificationItem);
                            });
                        } else {
                            console.log('📭 fallback - لا توجد إشعارات');
                            notificationsList.classList.add('d-none');
                            noNotifications.classList.remove('d-none');
                        }
                    })
                    .catch(error2 => {
                        console.error('💥 fallback أيضاً فشل:', error2);
                        notificationsList.innerHTML = `
                            <div class="alert alert-danger" role="alert">
                                حدث خطأ أثناء تحميل الإشعارات: ${error.message}
                                <br>جرب route العام أيضاً فشل: ${error2.message}
                                <br>يرجى المحاولة مرة أخرى أو تحديث الصفحة.
                            </div>
                        `;
                    });
                });
        }

        // دالة لتعيين إشعار كمقروء
        function markNotificationAsRead(notificationId) {
            fetch(`{{ url('waiter/notifications') }}/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث واجهة المستخدم
                    const notificationItem = document.querySelector(`#notificationsList .list-group-item[data-id="${notificationId}"]`);
                    if (notificationItem) {
                        notificationItem.classList.remove('bg-light');
                        const badge = notificationItem.querySelector('.badge');
                        if (badge) {
                            badge.remove();
                        }
                    }

                    // تحديث عدد الإشعارات غير المقروءة
                    const unreadItems = document.querySelectorAll('#notificationsList .list-group-item.bg-light');
                    updateNotificationBadge(unreadItems.length);
                }
            })
            .catch(error => {
                console.error('Error marking notification as read:', error);
            });
        }

        // دالة لتحديث شارة الإشعارات
        function updateNotificationBadge(count) {
            const badge = document.getElementById('notificationsBadge');
            if (badge) {
                if (count > 0) {
                    badge.textContent = count;
                    badge.classList.remove('d-none');
                } else {
                    badge.classList.add('d-none');
                }
            }
        }

        // إضافة مستمع الحدث لزر تعيين كل الإشعارات كمقروءة
        document.getElementById('markAllReadBtn').addEventListener('click', function() {
            fetch('{{ route("waiter.notifications.mark-all-as-read") }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث واجهة المستخدم
                    const notificationItems = document.querySelectorAll('#notificationsList .list-group-item');
                    notificationItems.forEach(item => {
                        item.classList.remove('bg-light');
                        const badge = item.querySelector('.badge');
                        if (badge) {
                            badge.remove();
                        }
                    });

                    // تحديث عدد الإشعارات غير المقروءة
                    updateNotificationBadge(0);

                    alert('تم تعيين جميع الإشعارات كمقروءة');
                }
            })
            .catch(error => {
                console.error('Error marking all notifications as read:', error);
                alert('حدث خطأ أثناء تعيين الإشعارات كمقروءة');
            });
        });

        // إضافة مستمعي الأحداث لأزرار الآلة الحاسبة
        document.querySelectorAll('.calculator-key').forEach(key => {
            key.addEventListener('click', function() {
                const keyValue = this.getAttribute('data-key');
                const display = document.getElementById('calculatorDisplay');

                if (keyValue === 'c') {
                    // مسح الشاشة
                    display.value = '';
                } else if (keyValue === '=') {
                    try {
                        // حساب النتيجة
                        display.value = eval(display.value);
                    } catch (error) {
                        display.value = 'خطأ';
                    }
                } else {
                    // إضافة القيمة إلى الشاشة
                    display.value += keyValue;
                }
            });
        });

        // إضافة مستمعي الأحداث لبطاقات خيارات الطباعة
        document.querySelectorAll('.print-option-card').forEach(card => {
            card.addEventListener('click', function() {
                const option = this.getAttribute('data-option');

                if (option === 'custom-invoice') {
                    // عرض قسم البحث عن فاتورة محددة
                    document.getElementById('customInvoiceSection').classList.remove('d-none');
                } else {
                    // إخفاء قسم البحث عن فاتورة محددة
                    document.getElementById('customInvoiceSection').classList.add('d-none');

                    // معالجة خيار الطباعة المحدد
                    handlePrintOption(option);
                }
            });
        });

        // دالة لمعالجة خيار الطباعة المحدد
        function handlePrintOption(option) {
            switch (option) {
                case 'last-invoice':
                    // طباعة آخر فاتورة
                    // طباعة آخر فاتورة - نبحث عن آخر فاتورة
                    fetch('{{ url("invoices") }}?latest=true')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success && data.invoice && data.invoice.id) {
                                window.open(`{{ url('invoices') }}/${data.invoice.id}/print/85mm`, '_blank');
                            } else {
                                alert('لم يتم العثور على فواتير');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('حدث خطأ أثناء محاولة طباعة آخر فاتورة');
                        });
                    break;

                case 'daily-report':
                    // طباعة تقرير اليوم
                    window.open('{{ route("reports.sales") }}?period=daily', '_blank');
                    break;

                case 'kitchen-order':
                    // طباعة طلب للمطبخ
                    if (cart.length === 0) {
                        alert('السلة فارغة، الرجاء إضافة منتجات أولاً');
                        return;
                    }

                    // إنشاء طلب مؤقت للمطبخ
                    const kitchenOrderData = {
                        order_type: orderType,
                        table_id: orderType === 'dine_in' ? selectedTableId : null,
                        products: cart.map(item => ({
                            id: item.id,
                            quantity: item.quantity,
                            notes: item.notes
                        }))
                    };

                    // نقوم بإنشاء طلب مؤقت وطباعته
                    fetch('{{ route("waiter.kitchen-order.create") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({})
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.print_url) {
                            window.open(data.print_url, '_blank');
                        } else {
                            alert('حدث خطأ: ' + (data.message || 'لم يتم إنشاء طلب المطبخ'));
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('حدث خطأ أثناء محاولة طباعة طلب المطبخ');
                    });
                    break;
            }

            // إغلاق نافذة خيارات الطباعة
            bootstrap.Modal.getInstance(document.getElementById('printOptionsModal')).hide();
        }

        // إضافة مستمع الحدث لزر البحث عن فاتورة
        document.getElementById('searchInvoiceBtn').addEventListener('click', function() {
            const searchTerm = document.getElementById('invoiceSearchInput').value.trim();

            if (!searchTerm) {
                alert('الرجاء إدخال رقم الفاتورة أو اسم العميل');
                return;
            }

            // البحث عن الفاتورة
            fetch(`{{ url('invoices') }}?search=${encodeURIComponent(searchTerm)}`)
                .then(response => response.json())
                .then(data => {
                    const resultsContainer = document.getElementById('invoiceSearchResults');
                    const resultsBody = document.getElementById('invoiceSearchResultsBody');

                    resultsBody.innerHTML = '';

                    if (data.success && data.invoices && data.invoices.length > 0) {
                        resultsContainer.classList.remove('d-none');

                        data.invoices.forEach(invoice => {
                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>${invoice.invoice_number}</td>
                                <td>${new Date(invoice.created_at).toLocaleDateString('ar-SA')}</td>
                                <td>${parseFloat(invoice.final_amount || invoice.total_amount || 0).toFixed(2)} ر.س</td>
                                <td>
                                    <button class="btn btn-sm btn-info print-invoice-btn" data-id="${invoice.id}">
                                        <i class="fas fa-print"></i> طباعة
                                    </button>
                                </td>
                            `;

                            resultsBody.appendChild(row);
                        });

                        // إضافة مستمعي الأحداث لأزرار الطباعة
                        document.querySelectorAll('.print-invoice-btn').forEach(btn => {
                            btn.addEventListener('click', function() {
                                const invoiceId = this.getAttribute('data-id');
                                window.open(`{{ url('invoices') }}/${invoiceId}/print/85mm`, '_blank');
                            });
                        });
                    } else {
                        alert('لم يتم العثور على فواتير مطابقة');
                        resultsContainer.classList.add('d-none');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء البحث عن الفاتورة');
                });
        });

        // إضافة مستمع الحدث لزر البحث عن طلب للإلغاء
        document.getElementById('searchCancelOrderBtn').addEventListener('click', function() {
            const searchTerm = document.getElementById('cancelOrderSearch').value.trim();

            if (!searchTerm) {
                alert('الرجاء إدخال رقم الطلب أو اسم العميل');
                return;
            }

            // البحث عن الطلب
            fetch(`{{ url('waiter/orders/active') }}?search=${encodeURIComponent(searchTerm)}`)
                .then(response => response.json())
                .then(data => {
                    const ordersContainer = document.getElementById('cancelOrdersContainer');
                    const noOrders = document.getElementById('noCancelOrders');
                    const tableBody = document.getElementById('cancelOrdersTableBody');

                    tableBody.innerHTML = '';

                    if (data.success && data.orders && data.orders.length > 0) {
                        ordersContainer.classList.remove('d-none');
                        noOrders.classList.add('d-none');

                        data.orders.forEach(order => {
                            // تنسيق الحالة
                            let statusBadge = '';
                            if (order.status === 'pending') {
                                statusBadge = '<span class="badge bg-warning">قيد الانتظار</span>';
                            } else if (order.status === 'in_progress') {
                                statusBadge = '<span class="badge bg-info">قيد التنفيذ</span>';
                            } else if (order.status === 'suspended') {
                                statusBadge = '<span class="badge bg-warning">معلق</span>';
                            } else if (order.status === 'completed') {
                                statusBadge = '<span class="badge bg-success">مكتمل</span>';
                            } else if (order.status === 'cancelled') {
                                statusBadge = '<span class="badge bg-danger">ملغي</span>';
                            }

                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>${order.id}</td>
                                <td>${order.table ? order.table.name : '-'}</td>
                                <td>${new Date(order.created_at).toLocaleDateString('ar-SA')}</td>
                                <td>${statusBadge}</td>
                                <td>
                                    ${order.status !== 'completed' && order.status !== 'cancelled' ?
                                        `<button class="btn btn-sm btn-danger cancel-order-btn" data-id="${order.id}">
                                            <i class="fas fa-times"></i> إلغاء
                                        </button>` :
                                        '<span class="text-muted">لا يمكن الإلغاء</span>'}
                                </td>
                            `;

                            tableBody.appendChild(row);
                        });

                        // إضافة مستمعي الأحداث لأزرار الإلغاء
                        document.querySelectorAll('.cancel-order-btn').forEach(btn => {
                            btn.addEventListener('click', function() {
                                const orderId = this.getAttribute('data-id');
                                showCancelConfirmation(orderId);
                            });
                        });
                    } else {
                        alert('لم يتم العثور على طلبات مطابقة');
                        ordersContainer.classList.add('d-none');
                        noOrders.classList.remove('d-none');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء البحث عن الطلب');
                });
        });

        // دالة لعرض تأكيد إلغاء الطلب
        function showCancelConfirmation(orderId) {
            document.getElementById('cancelOrderId').textContent = orderId;
            document.getElementById('cancelOrdersContainer').classList.add('d-none');
            document.getElementById('noCancelOrders').classList.add('d-none');
            document.getElementById('confirmCancelSection').classList.remove('d-none');

            // إعادة تعيين حقول النموذج
            document.getElementById('cancelReason').value = '';
            document.getElementById('otherCancelReason').value = '';
            document.getElementById('otherReasonContainer').classList.add('d-none');
        }

        // إضافة مستمع الحدث لتغيير سبب الإلغاء
        document.getElementById('cancelReason').addEventListener('change', function() {
            if (this.value === 'other') {
                document.getElementById('otherReasonContainer').classList.remove('d-none');
            } else {
                document.getElementById('otherReasonContainer').classList.add('d-none');
            }
        });

        // إضافة مستمع الحدث لزر الرجوع في قسم تأكيد الإلغاء
        document.getElementById('backToCancelOrdersBtn').addEventListener('click', function() {
            document.getElementById('confirmCancelSection').classList.add('d-none');
            document.getElementById('cancelOrdersContainer').classList.remove('d-none');
        });

        // إضافة مستمع الحدث لزر تأكيد إلغاء الطلب
        document.getElementById('confirmCancelOrderBtn').addEventListener('click', function() {
            const orderId = document.getElementById('cancelOrderId').textContent;
            const cancelReason = document.getElementById('cancelReason').value;
            let reason = cancelReason;

            if (!cancelReason) {
                alert('الرجاء اختيار سبب الإلغاء');
                return;
            }

            if (cancelReason === 'other') {
                const otherReason = document.getElementById('otherCancelReason').value.trim();
                if (!otherReason) {
                    alert('الرجاء إدخال سبب الإلغاء');
                    return;
                }
                reason = otherReason;
            }

            // إلغاء الطلب
            fetch(`{{ url('waiter/orders') }}/${orderId}/status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    status: 'cancelled',
                    notes: 'سبب الإلغاء: ' + reason
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم إلغاء الطلب بنجاح');

                    // إغلاق نافذة إلغاء الطلبات
                    bootstrap.Modal.getInstance(document.getElementById('cancelOrdersModal')).hide();
                } else {
                    alert('حدث خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء إلغاء الطلب');
            });
        });

        // إضافة مستمع الحدث لزر حفظ الإعدادات
        document.getElementById('saveSettingsBtn').addEventListener('click', function() {
            // حفظ إعدادات الوضع الليلي
            const darkMode = document.getElementById('darkModeSwitch').checked;
            localStorage.setItem('darkMode', darkMode);

            // حفظ إعدادات أصوات التنبيهات
            const sounds = document.getElementById('soundsSwitch').checked;
            localStorage.setItem('sounds', sounds);

            // حفظ إعدادات حجم الخط
            let fontSize = 'medium';
            if (document.getElementById('fontSizeSmall').checked) {
                fontSize = 'small';
            } else if (document.getElementById('fontSizeLarge').checked) {
                fontSize = 'large';
            }
            localStorage.setItem('fontSize', fontSize);

            // تطبيق الإعدادات
            applySettings();

            alert('تم حفظ الإعدادات بنجاح');
            bootstrap.Modal.getInstance(document.getElementById('settingsModal')).hide();
        });

        // دالة لتطبيق الإعدادات
        function applySettings() {
            // تطبيق إعدادات الوضع الليلي
            const darkMode = localStorage.getItem('darkMode') === 'true';
            if (darkMode) {
                document.body.classList.add('dark-mode');
            } else {
                document.body.classList.remove('dark-mode');
            }

            // تطبيق إعدادات حجم الخط
            const fontSize = localStorage.getItem('fontSize') || 'medium';
            document.body.classList.remove('font-small', 'font-medium', 'font-large');
            document.body.classList.add('font-' + fontSize);
        }

        // تحميل الإعدادات المحفوظة عند تحميل الصفحة
        function loadSavedSettings() {
            // تحميل إعدادات الوضع الليلي
            const darkMode = localStorage.getItem('darkMode') === 'true';
            document.getElementById('darkModeSwitch').checked = darkMode;

            // تحميل إعدادات أصوات التنبيهات
            const sounds = localStorage.getItem('sounds') !== 'false'; // افتراضيًا مفعل
            document.getElementById('soundsSwitch').checked = sounds;

            // تحميل إعدادات حجم الخط
            const fontSize = localStorage.getItem('fontSize') || 'medium';
            if (fontSize === 'small') {
                document.getElementById('fontSizeSmall').checked = true;
            } else if (fontSize === 'large') {
                document.getElementById('fontSizeLarge').checked = true;
            } else {
                document.getElementById('fontSizeMedium').checked = true;
            }

            // تطبيق الإعدادات
            applySettings();
        }

        // تحميل الإعدادات المحفوظة عند تحميل الصفحة
        loadSavedSettings();

        // تحديث آخر وقت تم فيه عرض الإشعارات عند تحميل الصفحة
        // هذا يمنع ظهور إشعارات قديمة عند تحديث الصفحة
        updateLastNotificationCheck();

        // اختبار API الإشعارات مباشرة
        console.log('🔍 اختبار API الإشعارات...');
        console.log('🔗 رابط API الكاشير:', '{{ route("waiter.notifications.get") }}');
        console.log('🔗 رابط API العام:', '{{ route("notifications.get") }}');
        console.log('🆔 معرف المستخدم الحالي:', {{ auth()->id() ?? 'null' }});
        console.log('👤 اسم المستخدم الحالي:', '{{ auth()->user()->name ?? "غير مسجل دخول" }}');
        console.log('🎭 دور المستخدم الحالي:', '{{ auth()->user()->role_name }}');
        console.log('🔐 CSRF Token:', '{{ csrf_token() }}');

        // اختبار صلاحيات الكاشير
        console.log('🔒 اختبار صلاحيات الكاشير...');

        // اختبار الوصول لمسار الطاولات
        fetch('/tables', {
            method: 'GET',
            headers: {
                'Accept': 'text/html',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('📋 اختبار الوصول للطاولات - الحالة:', response.status);
            if (response.status === 200) {
                console.log('✅ الكاشير يمكنه الوصول للطاولات');
            } else if (response.status === 403) {
                console.log('❌ الكاشير لا يمكنه الوصول للطاولات - ممنوع');
            } else if (response.status === 302) {
                console.log('🔄 الكاشير يتم إعادة توجيهه - قد تكون مشكلة في الصلاحيات');
            }
        })
        .catch(error => {
            console.error('💥 خطأ في اختبار الطاولات:', error);
        });

        // اختبار كلا الـ routes
        console.log('🧪 اختبار route الكاشير أولاً...');

        // تحقق من cookies
        console.log('🍪 Cookies:', document.cookie);

        // تحقق من localStorage
        console.log('💾 LocalStorage keys:', Object.keys(localStorage));

        // تحقق من session
        console.log('🔐 Session info:');
        console.log('  - User Agent:', navigator.userAgent);
        console.log('  - Current URL:', window.location.href);
        console.log('  - Referrer:', document.referrer);

        // تحقق من Laravel session
        const sessionCookie = document.cookie.split(';').find(cookie => cookie.trim().startsWith('laravel_session='));
        if (sessionCookie) {
            console.log('✅ Laravel session cookie موجود');
        } else {
            console.error('❌ Laravel session cookie غير موجود');
        }

        // استخدام route النادل المخصص
        console.log('🔍 بدء اختبار API الإشعارات...');
        fetch('{{ route("waiter.notifications.get") }}', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('📡 اختبار API - حالة الاستجابة:', response.status);
            console.log('📄 اختبار API - نوع المحتوى:', response.headers.get('content-type'));
            console.log('🔗 اختبار API - URL:', response.url);

            if (response.status === 401) {
                console.error('❌ خطأ 401: غير مصرح - المستخدم غير مسجل دخول');
                throw new Error('غير مصرح - يرجى تسجيل الدخول');
            } else if (response.status === 403) {
                console.error('❌ خطأ 403: ممنوع - المستخدم لا يملك الصلاحية');
                throw new Error('ممنوع - لا تملك الصلاحية');
            } else if (response.status === 404) {
                console.error('❌ خطأ 404: الصفحة غير موجودة');
                throw new Error('الصفحة غير موجودة');
            } else if (response.status === 500) {
                console.error('❌ خطأ 500: خطأ في الخادم');
                throw new Error('خطأ في الخادم');
            } else if (!response.ok) {
                console.error('❌ خطأ غير متوقع:', response.status, response.statusText);
                throw new Error(`خطأ HTTP: ${response.status}`);
            }

            return response.text();
        })
        .then(text => {
            console.log('📝 اختبار API - نص الاستجابة الكامل:', text);
            console.log('📏 اختبار API - طول النص:', text.length);

            if (!text || text.trim() === '') {
                console.error('❌ الاستجابة فارغة');
                throw new Error('الاستجابة فارغة من الخادم');
            }

            try {
                const data = JSON.parse(text);
                console.log('✅ اختبار API - البيانات المحللة:', data);
                console.log('🔍 اختبار API - نوع البيانات:', typeof data);
                console.log('✔️ اختبار API - نجاح العملية:', data.success);

                if (data.success) {
                    console.log('🎉 API يعمل بنجاح');
                    console.log('📊 عدد الإشعارات:', data.notifications ? data.notifications.length : 0);
                    console.log('👤 معرف المستخدم:', data.user_id);

                    if (data.notifications && data.notifications.length > 0) {
                        console.log('📋 أول 3 إشعارات:');
                        data.notifications.slice(0, 3).forEach((notif, index) => {
                            console.log(`  ${index + 1}. ${notif.title} - ${notif.read ? 'مقروء' : 'غير مقروء'}`);
                        });
                    } else {
                        console.log('📭 لا توجد إشعارات');
                    }
                } else {
                    console.error('❌ API فشل:', data.message || 'سبب غير محدد');
                }
            } catch (e) {
                console.error('❌ اختبار API - خطأ في تحليل JSON:', e);
                console.log('📄 النص المستلم ليس JSON صالح');
                console.log('🔍 أول 200 حرف من النص:', text.substring(0, 200));
            }
        })
        .catch(error => {
            console.error('💥 اختبار API الكاشير - خطأ في الشبكة:', error);
            console.error('📋 تفاصيل الخطأ:', error.message);

            // إذا فشل route الكاشير، جرب route العام
            console.log('🔄 route الكاشير فشل، جاري تجربة route العام...');

            fetch('{{ route("notifications.get") }}', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                credentials: 'same-origin'
            })
            .then(response => {
                console.log('📡 اختبار API العام - حالة الاستجابة:', response.status);
                console.log('📄 اختبار API العام - نوع المحتوى:', response.headers.get('content-type'));
                return response.text();
            })
            .then(text => {
                console.log('📝 اختبار API العام - نص الاستجابة:', text);
                try {
                    const data = JSON.parse(text);
                    console.log('✅ اختبار API العام - البيانات المحللة:', data);
                    if (data.success) {
                        console.log('🎉 API العام يعمل بنجاح');
                        console.log('📊 عدد الإشعارات:', data.notifications ? data.notifications.length : 0);
                    } else {
                        console.error('❌ API العام فشل:', data.message);
                    }
                } catch (e) {
                    console.error('❌ خطأ في تحليل JSON للـ API العام:', e);
                }
            })
            .catch(error2 => {
                console.error('💥 API العام أيضاً فشل:', error2);
            });
        });

        // تحميل الإشعارات عند تحميل الصفحة
        loadNotifications();

        // تحديث الإشعارات كل 30 ثانية
        setInterval(function() {
            loadNotifications();
        }, 30000);

        // إضافة مستمع الحدث لزر حفظ كمسودة
        const draftBtn = document.getElementById('draftBtn');
        if (draftBtn) {
            console.log('✅ تم العثور على زر حفظ كمسودة');
            draftBtn.addEventListener('click', function() {
                console.log('🔄 تم النقر على زر حفظ كمسودة');

                if (!validateOrder()) {
                    console.log('❌ فشل في التحقق من صحة الطلب');
                    return;
                }

                console.log('✅ تم التحقق من صحة الطلب، بدء حفظ المسودة');

                // حفظ الطلب كمسودة
                saveDraft();
            });
        } else {
            console.error('❌ لم يتم العثور على زر حفظ كمسودة');
        }

        // إضافة مستمع الحدث لزر تعليق الطلب
        const suspendBtn = document.getElementById('suspendBtn');
        if (suspendBtn) {
            console.log('✅ تم العثور على زر التعليق');
            suspendBtn.addEventListener('click', function() {
                console.log('🔄 تم النقر على زر التعليق');

                if (!validateOrder()) {
                    console.log('❌ فشل في التحقق من صحة الطلب');
                    return;
                }

                console.log('✅ تم التحقق من صحة الطلب، فتح نافذة التعليق');

                // فتح نافذة تعليق الطلب
                const suspendOrderModal = new bootstrap.Modal(document.getElementById('suspendOrderModal'));
                suspendOrderModal.show();
            });
        } else {
            console.error('❌ لم يتم العثور على زر التعليق');
        }

        // إضافة مستمع الحدث لزر إلغاء تعليق الطلب
        document.getElementById('cancelSuspendBtn').addEventListener('click', function() {
            bootstrap.Modal.getInstance(document.getElementById('suspendOrderModal')).hide();
        });

        // إضافة مستمع الحدث لزر تأكيد تعليق الطلب
        document.getElementById('confirmSuspendBtn').addEventListener('click', function() {
            const customerName = document.getElementById('customerName').value;
            const suspendNotes = document.getElementById('suspendNotes').value;

            if (!customerName) {
                alert('الرجاء إدخال اسم العميل');
                return;
            }

            // إغلاق نافذة تعليق الطلب
            bootstrap.Modal.getInstance(document.getElementById('suspendOrderModal')).hide();

            // تعليق الطلب
            suspendOrder(customerName, suspendNotes);
        });

        // إضافة مستمع الحدث لزر عرض السعر
        document.getElementById('quotationBtn').addEventListener('click', function() {
            if (!validateOrder()) {
                return;
            }

            // حساب إجمالي الفاتورة
            const subtotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);
            const tax = subtotal * taxRate; // استخدام معدل الضريبة من الإعدادات
            const total = subtotal + tax;

            // إنشاء محتوى عرض السعر
            let quotationContent = `
                <div class="p-3">
                    <h4 class="text-center mb-4">عرض سعر</h4>
                    <div class="mb-3">
                        <p><strong>التاريخ:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
                        ${orderType === 'dine_in' ? `<p><strong>الطاولة:</strong> ${selectedTable}</p>` : ''}
                        ${customerPhone ? `<p><strong>رقم الهاتف:</strong> ${customerPhone}</p>` : ''}
                        ${carNumber ? `<p><strong>رقم السيارة:</strong> ${carNumber}</p>` : ''}
                    </div>
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>المنتج</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            cart.forEach((item, index) => {
                quotationContent += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${item.name}</td>
                        <td>${item.price.toFixed(2)} ر.س</td>
                        <td>${item.quantity}</td>
                        <td>${(item.price * item.quantity).toFixed(2)} ر.س</td>
                    </tr>
                `;
            });

            quotationContent += `
                        </tbody>
                        <tfoot>
                            <tr>
                                <th colspan="4" class="text-end">المجموع:</th>
                                <th>${subtotal.toFixed(2)} ر.س</th>
                            </tr>
                            <tr>
                                <th colspan="4" class="text-end">الضريبة ({{ \App\Models\Setting::get('tax_rate', 15) }}%):</th>
                                <th>${tax.toFixed(2)} ر.س</th>
                            </tr>
                            <tr>
                                <th colspan="4" class="text-end">الإجمالي:</th>
                                <th>${total.toFixed(2)} ر.س</th>
                            </tr>
                        </tfoot>
                    </table>
                    <div class="text-center mt-4">
                        <p>هذا عرض سعر وليس فاتورة ضريبية</p>
                        <p>صالح لمدة 24 ساعة من تاريخ الإصدار</p>
                    </div>
                </div>
            `;

            // فتح نافذة جديدة وعرض المحتوى
            const quotationWindow = window.open('', '_blank', 'width=800,height=600');
            quotationWindow.document.write(`
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>عرض سعر</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
                    <style>
                        body {
                            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                            padding: 20px;
                        }
                        @media print {
                            .no-print {
                                display: none;
                            }
                        }
                    </style>
                </head>
                <body>
                    ${quotationContent}
                    <div class="text-center mt-4 no-print">
                        <button class="btn btn-primary" onclick="window.print()">طباعة</button>
                        <button class="btn btn-secondary" onclick="window.close()">إغلاق</button>
                    </div>
                </body>
                </html>
            `);
        });

        // دالة لفتح تفاصيل الطلب
        function getOrderDetails(orderId, carNumber = null) {
            // إغلاق نافذة الإشعارات
            if (document.getElementById('notificationsModal').classList.contains('show')) {
                bootstrap.Modal.getInstance(document.getElementById('notificationsModal')).hide();
            }

            // فتح نافذة الطلبات النشطة
            const activeOrdersModal = new bootstrap.Modal(document.getElementById('activeOrdersModal'));
            activeOrdersModal.show();

            // تحميل الطلبات النشطة
            loadActiveOrders().then(() => {
                // البحث عن الطلب وفتح تفاصيله
                viewOrderDetails(orderId);

                // إذا كان هناك رقم سيارة، عرضه في نافذة منبثقة
                if (carNumber) {
                    setTimeout(() => {
                        // إنشاء عنصر تنبيه لعرض رقم السيارة
                        const alertDiv = document.createElement('div');
                        alertDiv.className = 'alert alert-info alert-dismissible fade show';
                        alertDiv.innerHTML = `
                            <strong>رقم السيارة:</strong> ${carNumber}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            <button class="btn btn-sm btn-outline-primary ms-2 copy-car-number" data-car-number="${carNumber}">
                                <i class="fas fa-copy"></i> نسخ
                            </button>
                        `;

                        // إضافة التنبيه إلى أعلى نافذة تفاصيل الطلب
                        const modalBody = document.querySelector('#activeOrderDetailsModal .modal-body');
                        modalBody.insertBefore(alertDiv, modalBody.firstChild);

                        // إضافة مستمع الحدث لزر النسخ
                        alertDiv.querySelector('.copy-car-number').addEventListener('click', function() {
                            const carNumber = this.getAttribute('data-car-number');
                            navigator.clipboard.writeText(carNumber)
                                .then(() => {
                                    this.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
                                    setTimeout(() => {
                                        this.innerHTML = '<i class="fas fa-copy"></i> نسخ';
                                    }, 2000);
                                });
                        });
                    }, 500); // تأخير قصير للتأكد من أن النافذة قد تم عرضها
                }
            });
        }

        // تهيئة السلة
        updateCart();

        // طلب إذن الإشعارات
        if ("Notification" in window && Notification.permission !== "granted" && Notification.permission !== "denied") {
            Notification.requestPermission();
        }
    });
</script>
@endsection
