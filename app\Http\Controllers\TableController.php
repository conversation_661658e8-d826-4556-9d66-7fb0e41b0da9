<?php

namespace App\Http\Controllers;

use App\Models\Table;
use Illuminate\Http\Request;

class TableController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $tables = Table::all();
        return view('tables.index', compact('tables'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('tables.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'capacity' => 'required|integer|min:1',
            'status' => 'required|in:available,occupied,reserved',
            'description' => 'nullable|string',
        ]);

        Table::create($validated);

        return redirect()->route('tables.index')
            ->with('success', 'تم إنشاء الطاولة بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $table = Table::findOrFail($id);
        $orders = $table->orders()->latest()->take(10)->get();
        return view('tables.show', compact('table', 'orders'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $table = Table::findOrFail($id);
        return view('tables.edit', compact('table'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $table = Table::findOrFail($id);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'capacity' => 'required|integer|min:1',
            'status' => 'required|in:available,occupied,reserved',
            'description' => 'nullable|string',
        ]);

        $table->update($validated);

        return redirect()->route('tables.index')
            ->with('success', 'تم تحديث الطاولة بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $table = Table::findOrFail($id);

        // التحقق من وجود طلبات مرتبطة بالطاولة
        if ($table->orders()->count() > 0) {
            return redirect()->route('tables.index')
                ->with('error', 'لا يمكن حذف الطاولة لأنها مرتبطة بطلبات');
        }

        $table->delete();

        return redirect()->route('tables.index')
            ->with('success', 'تم حذف الطاولة بنجاح');
    }
}
