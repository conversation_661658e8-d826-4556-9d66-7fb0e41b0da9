<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->enum('order_type', ['dine_in', 'takeaway'])->default('dine_in')->after('table_id');
            $table->string('customer_name')->nullable()->after('order_type');
            $table->string('customer_phone', 20)->nullable()->after('customer_name');
            $table->string('car_number', 20)->nullable()->after('customer_phone');

            // تحديث enum status لإضافة suspended
            $table->dropColumn('status');
        });

        // إضافة عمود status جديد مع القيم المحدثة
        Schema::table('orders', function (Blueprint $table) {
            $table->enum('status', ['pending', 'preparing', 'ready', 'delivered', 'completed', 'cancelled', 'suspended', 'in_progress'])->default('pending')->after('car_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn(['order_type', 'customer_name', 'customer_phone', 'car_number']);
            $table->dropColumn('status');
        });

        // إعادة إضافة عمود status الأصلي
        Schema::table('orders', function (Blueprint $table) {
            $table->enum('status', ['pending', 'preparing', 'ready', 'delivered', 'completed', 'cancelled'])->default('pending');
        });
    }
};
