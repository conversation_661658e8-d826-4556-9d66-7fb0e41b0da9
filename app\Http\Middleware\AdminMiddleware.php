<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {


        // التحقق من تسجيل دخول المستخدم
        if (!$request->user()) {
            return redirect()->route('login');
        }

        $user = $request->user();

        // السماح للمدير والمدير العام بالوصول إلى كل شيء
        if ($user->hasRole('admin') || $user->hasRole('manager')) {
            return $next($request);
        }

        // السماح للكاشير بالوصول إلى موارد محددة
        if ($user->hasRole('cashier')) {
            $currentPath = $request->path();

            // منع الوصول للتقارير للكاشير
            if (str_starts_with($currentPath, 'reports')) {
                return redirect()->route('dashboard')->with('error', 'ليس لديك صلاحية للوصول إلى التقارير');
            }

            // منع الوصول لإدارة المستخدمين والأدوار للكاشير
            if (str_starts_with($currentPath, 'users') || str_starts_with($currentPath, 'roles')) {
                return redirect()->route('dashboard')->with('error', 'ليس لديك صلاحية للوصول إلى إدارة المستخدمين');
            }

            // منع الوصول للإعدادات للكاشير
            if (str_starts_with($currentPath, 'settings')) {
                return redirect()->route('dashboard')->with('error', 'ليس لديك صلاحية للوصول إلى إعدادات النظام');
            }

            // منع الوصول للنسخ الاحتياطي للكاشير
            if (str_starts_with($currentPath, 'backup')) {
                return redirect()->route('dashboard')->with('error', 'ليس لديك صلاحية للوصول إلى النسخ الاحتياطي');
            }

            // منع الوصول للمصروفات والمشتريات للكاشير
            if (str_starts_with($currentPath, 'expenses') || str_starts_with($currentPath, 'purchases')) {
                return redirect()->route('dashboard')->with('error', 'ليس لديك صلاحية للوصول إلى إدارة المصروفات والمشتريات');
            }

            // منع الوصول للمنتجات والتصنيفات للكاشير فقط
            if ($user->hasRole('cashier') && (str_starts_with($currentPath, 'products') || str_starts_with($currentPath, 'categories') || str_starts_with($currentPath, 'tables'))) {
                return redirect()->route('dashboard')->with('error', 'ليس لديك صلاحية للوصول إلى إدارة المنتجات والتصنيفات والطاولات');
            }

            // منع الكاشير من تعديل وحذف الفواتير
            if ($user->hasRole('cashier') && str_starts_with($currentPath, 'invoices')) {
                // منع الوصول لصفحة التعديل
                if (preg_match('/invoices\/\d+\/edit/', $currentPath)) {
                    return redirect()->route('invoices.index')->with('error', 'ليس لديك صلاحية لتعديل الفواتير');
                }

                // منع عمليات التحديث والحذف
                if (preg_match('/invoices\/\d+$/', $currentPath) && ($request->isMethod('PUT') || $request->isMethod('PATCH') || $request->isMethod('DELETE'))) {
                    return redirect()->route('invoices.index')->with('error', 'ليس لديك صلاحية لتعديل أو حذف الفواتير');
                }
            }

            // قائمة المسارات المسموحة للكاشير
            $cashierAllowedPaths = [
                'orders',
                'invoices',
                'waiter',
                'chef',
                'notifications'
            ];

            // تحديد المسارات المسموحة للكاشير
            $allowedPaths = $cashierAllowedPaths;

            // التحقق من أن المسار الحالي مسموح
            foreach ($allowedPaths as $allowedPath) {
                if (str_starts_with($currentPath, $allowedPath)) {
                    return $next($request);
                }
            }
        }

        // السماح لدور المطبخ بالوصول إلى واجهة الشيف فقط
        if ($user->hasRole('kitchen')) {
            $currentPath = $request->path();

            // قائمة المسارات المسموحة لدور المطبخ
            $kitchenAllowedPaths = [
                'chef',
                'dashboard'
            ];

            // التحقق من أن المسار الحالي مسموح
            foreach ($kitchenAllowedPaths as $allowedPath) {
                if (str_starts_with($currentPath, $allowedPath)) {
                    return $next($request);
                }
            }

            // إذا لم يكن المسار مسموح، منع الوصول
            return redirect()->route('dashboard')->with('error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة');
        }

        // إذا لم يكن المستخدم مدير أو كاشير أو مطبخ مع صلاحية، منع الوصول
        return redirect()->route('dashboard')->with('error', 'ليس لديك صلاحية للوصول إلى هذه الصفحة');
    }
}
