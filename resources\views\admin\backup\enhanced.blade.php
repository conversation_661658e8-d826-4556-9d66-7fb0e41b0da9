@extends('layouts.app')

@section('title', 'النسخ الاحتياطي المطور')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">
            <i class="fas fa-shield-alt me-2"></i>
            النسخ الاحتياطي المطور
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">لوحة التحكم</a></li>
                <li class="breadcrumb-item active">النسخ الاحتياطي</li>
            </ol>
        </nav>
    </div>

    <!-- إحصائيات النسخ الاحتياطي -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ $statistics['total'] ?? 0 }}</h4>
                            <p class="mb-0">إجمالي النسخ</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-database fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ $statistics['successful'] ?? 0 }}</h4>
                            <p class="mb-0">نسخ ناجحة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ $statistics['failed'] ?? 0 }}</h4>
                            <p class="mb-0">نسخ فاشلة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ number_format(($statistics['total_size'] ?? 0) / 1024 / 1024, 1) }} MB</h4>
                            <p class="mb-0">إجمالي الحجم</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-hdd fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إنشاء نسخة احتياطية جديدة -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus-circle me-2"></i>
                        إنشاء نسخة احتياطية جديدة
                    </h5>
                </div>
                <div class="card-body">
                    <form id="backupForm">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="backup_type" class="form-label">نوع النسخة الاحتياطية</label>
                                    <select class="form-select" id="backup_type" name="backup_type" required>
                                        <option value="database">قاعدة البيانات فقط (سريع)</option>
                                        <option value="files">الملفات فقط</option>
                                        <option value="full">نسخة كاملة (قاعدة البيانات + الملفات)</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="backup_description" class="form-label">وصف النسخة (اختياري)</label>
                                    <input type="text" class="form-control" id="backup_description" name="description"
                                           placeholder="مثال: نسخة احتياطية قبل التحديث">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary" id="createBackupBtn">
                                            <i class="fas fa-plus me-2"></i>
                                            إنشاء نسخة احتياطية
                                        </button>
                                        <button type="button" class="btn btn-outline-primary" id="createBackupSyncBtn">
                                            <i class="fas fa-bolt me-2"></i>
                                            نسخ فوري (للاختبار)
                                        </button>
                                        <button type="button" class="btn btn-warning" onclick="cleanupPendingBackups()">
                                            <i class="fas fa-broom me-2"></i>
                                            تنظيف النسخ المعلقة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>



    <!-- قائمة النسخ الاحتياطية -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                النسخ الاحتياطية
            </h5>
            <button class="btn btn-outline-primary btn-sm" onclick="location.reload()">
                <i class="fas fa-sync-alt me-1"></i>
                تحديث
            </button>
        </div>
        <div class="card-body">
            @if($backups->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 20%;">اسم الملف</th>
                            <th style="width: 10%;">النوع</th>
                            <th style="width: 10%;">الحالة</th>
                            <th style="width: 10%;">الحجم</th>
                            <th style="width: 8%;">المدة</th>
                            <th style="width: 12%;">منشئ بواسطة</th>
                            <th style="width: 15%;">تاريخ الإنشاء</th>
                            <th style="width: 15%;">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($backups as $backup)
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    @if($backup->is_scheduled)
                                        <i class="fas fa-clock text-info me-2" title="نسخة مجدولة"></i>
                                    @else
                                        <i class="fas fa-user text-primary me-2" title="نسخة يدوية"></i>
                                    @endif
                                    <span class="font-monospace">{{ $backup->filename }}</span>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ $backup->type_name }}</span>
                            </td>
                            <td>
                                <span class="badge bg-{{ $backup->status_color }}">
                                    {{ $backup->status_name }}
                                </span>
                                @if($backup->status == 'running')
                                    <i class="fas fa-spinner fa-spin ms-1"></i>
                                @elseif($backup->status == 'pending')
                                    <i class="fas fa-clock ms-1" title="في انتظار المعالجة"></i>
                                @endif

                                @if($backup->status == 'pending' && $backup->created_at->diffInMinutes() > 30)
                                    <br><small class="text-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        معلقة منذ {{ $backup->created_at->diffForHumans() }}
                                    </small>
                                @endif
                            </td>
                            <td>{{ $backup->formatted_size }}</td>
                            <td>{{ $backup->duration ?? '-' }}</td>
                            <td>{{ $backup->creator->name ?? 'نظام' }}</td>
                            <td>{{ $backup->created_at->format('Y-m-d H:i') }}</td>
                            <td>
                                <div class="btn-group-vertical btn-group-sm d-grid gap-1" style="min-width: 120px;">
                                    @if($backup->status == 'completed' || $backup->fileExists())
                                        <!-- زر التحميل -->
                                        <a href="{{ route('backup.download', $backup->filename) }}"
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-download me-1"></i>
                                            تحميل
                                        </a>

                                        <!-- زر الاستعادة -->
                                        @if($backup->type == 'database' || $backup->type == 'full')
                                        <button class="btn btn-success btn-sm"
                                                onclick="restoreBackup('{{ $backup->filename }}')">
                                            <i class="fas fa-undo me-1"></i>
                                            استعادة
                                        </button>
                                        @endif
                                    @elseif($backup->status == 'running' || $backup->status == 'pending')
                                        <span class="btn btn-info btn-sm disabled">
                                            <i class="fas fa-spinner fa-spin me-1"></i>
                                            {{ $backup->status == 'pending' ? 'في الانتظار' : 'قيد التنفيذ' }}
                                        </span>

                                        <!-- زر إلغاء للنسخ المعلقة -->
                                        @if($backup->status == 'pending')
                                        <button class="btn btn-warning btn-sm"
                                                onclick="cancelPendingBackup('{{ $backup->filename }}')"
                                                title="إلغاء النسخة المعلقة">
                                            <i class="fas fa-times me-1"></i>
                                            إلغاء
                                        </button>
                                        @endif
                                    @elseif($backup->status == 'failed')
                                        <button class="btn btn-warning btn-sm"
                                                data-bs-toggle="tooltip"
                                                title="{{ $backup->error_message }}">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            فشل
                                        </button>
                                    @else
                                        <!-- إذا لم تكن هناك حالة واضحة، اعرض الأزرار -->
                                        <a href="{{ route('backup.download', $backup->filename) }}"
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-download me-1"></i>
                                            تحميل
                                        </a>

                                        @if($backup->type == 'database' || $backup->type == 'full')
                                        <button class="btn btn-success btn-sm"
                                                onclick="restoreBackup('{{ $backup->filename }}')">
                                            <i class="fas fa-undo me-1"></i>
                                            استعادة
                                        </button>
                                        @endif
                                    @endif

                                    <!-- زر الحذف - متاح دائماً -->
                                    <button class="btn btn-danger btn-sm"
                                            onclick="deleteBackup('{{ $backup->filename }}')">
                                        <i class="fas fa-trash me-1"></i>
                                        حذف
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {{ $backups->links() }}
            @else
            <div class="text-center py-4">
                <i class="fas fa-database fa-3x text-muted mb-3"></i>
                <p class="text-muted">لا توجد نسخ احتياطية حتى الآن</p>
                <p class="text-muted">قم بإنشاء أول نسخة احتياطية باستخدام النموذج أعلاه</p>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Modal للتقدم -->
<div class="modal fade" id="progressModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">جاري إنشاء النسخة الاحتياطية</h5>
            </div>
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p>يرجى الانتظار، جاري إنشاء النسخة الاحتياطية...</p>
                <small class="text-muted">قد تستغرق هذه العملية عدة دقائق حسب حجم البيانات</small>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
/* تحسين مظهر أزرار الإجراءات */
.btn-group-vertical .btn {
    border-radius: 4px !important;
    margin-bottom: 2px;
    font-size: 12px;
    padding: 4px 8px;
    white-space: nowrap;
}

.btn-group-vertical .btn:last-child {
    margin-bottom: 0;
}

/* تحسين مظهر الجدول */
.table td {
    vertical-align: middle;
}

/* تحسين مظهر badges الحالة */
.badge {
    font-size: 11px;
    padding: 4px 8px;
}

/* تحسين مظهر التنبيهات */
.alert {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* تحسين مظهر الأزرار المعطلة */
.btn.disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* تحسين responsive للجدول */
@media (max-width: 768px) {
    .btn-group-vertical .btn {
        font-size: 10px;
        padding: 2px 6px;
    }

    .table th, .table td {
        font-size: 12px;
        padding: 8px 4px;
    }
}
</style>
@endsection

@section('scripts')
<script>
// نموذج إنشاء النسخة الاحتياطية
document.getElementById('backupForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const btn = document.getElementById('createBackupBtn');
    const originalText = btn.innerHTML;

    // إظهار modal التقدم
    const progressModal = new bootstrap.Modal(document.getElementById('progressModal'));
    progressModal.show();

    // تعطيل الزر
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإنشاء...';

    const formData = new FormData(this);

    fetch('/backup/create', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.text().then(text => {
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('Response is not valid JSON:', text);
                throw new Error('الاستجابة من الخادم غير صحيحة');
            }
        });
    })
    .then(data => {
        progressModal.hide();

        if (data.success) {
            // إظهار رسالة نجاح
            showAlert('success', data.message);

            // تحديث الصفحة بعد 3 ثواني
            setTimeout(() => {
                location.reload();
            }, 3000);
        } else {
            showAlert('danger', data.message || 'حدث خطأ غير معروف');
        }
    })
    .catch(error => {
        progressModal.hide();
        console.error('Fetch error:', error);

        // إضافة معلومات debug في وضع التطوير
        let errorMessage = 'حدث خطأ: ' + error.message;
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            errorMessage += '\n\nتفاصيل إضافية (وضع التطوير):\n' + JSON.stringify(error, null, 2);
        }

        showAlert('danger', errorMessage);
    })
    .finally(() => {
        btn.disabled = false;
        btn.innerHTML = originalText;
    });
});

// زر النسخ الفوري
document.getElementById('createBackupSyncBtn').addEventListener('click', function() {
    const form = document.getElementById('backupForm');
    const formData = new FormData(form);

    const btn = this;
    const originalText = btn.innerHTML;

    // إظهار modal التقدم
    const progressModal = new bootstrap.Modal(document.getElementById('progressModal'));
    progressModal.show();

    // تعطيل الزر
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإنشاء...';

    fetch('/backup/create-sync', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.text().then(text => {
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('Response is not valid JSON:', text);
                throw new Error('الاستجابة من الخادم غير صحيحة');
            }
        });
    })
    .then(data => {
        progressModal.hide();

        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showAlert('danger', data.message || 'حدث خطأ غير معروف');
        }
    })
    .catch(error => {
        progressModal.hide();
        console.error('Fetch error:', error);
        showAlert('danger', 'حدث خطأ: ' + error.message);
    })
    .finally(() => {
        btn.disabled = false;
        btn.innerHTML = originalText;
    });
});

// حذف نسخة احتياطية
function deleteBackup(filename) {
    const confirmMessage = `هل أنت متأكد من رغبتك في حذف النسخة الاحتياطية؟

📁 الملف: ${filename}

⚠️ تحذير: هذه العملية لا يمكن التراجع عنها!

هل تريد المتابعة؟`;

    if (confirm(confirmMessage)) {
        console.log('🗑️ بدء عملية الحذف للملف:', filename);

        // استخدام fetch بسيط
        fetch(`/backup/delete/${filename}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                _method: 'DELETE'
            })
        })
        .then(response => {
            console.log('📡 استجابة الخادم:', response.status, response.statusText);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('✅ بيانات الاستجابة:', data);
            if (data.success) {
                showAlert('success', `✅ ${data.message}`);
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showAlert('danger', `❌ ${data.message || 'حدث خطأ أثناء الحذف'}`);
            }
        })
        .catch(error => {
            console.error('❌ خطأ في الحذف:', error);
            showAlert('danger', `❌ حدث خطأ أثناء الحذف: ${error.message}`);
        });
    }
}

// استعادة نسخة احتياطية مباشرة
function restoreBackup(filename) {
    // رسالة تأكيد مفصلة
    const confirmMessage = `هل أنت متأكد من رغبتك في استعادة النسخة الاحتياطية؟

📁 الملف: ${filename}

⚠️ تحذير مهم:
• سيتم إنشاء نسخة احتياطية من البيانات الحالية تلقائياً قبل الاستعادة
• ستتم استبدال البيانات الحالية بالبيانات من النسخة الاحتياطية
• هذه العملية لا يمكن التراجع عنها

هل تريد المتابعة؟`;

    if (confirm(confirmMessage)) {
        // إظهار modal التقدم
        const progressModal = new bootstrap.Modal(document.getElementById('progressModal'));
        progressModal.show();

        // تحديث نص modal
        document.querySelector('#progressModal .modal-title').textContent = 'جاري استعادة النسخة الاحتياطية';
        document.querySelector('#progressModal .modal-body p').textContent = `يرجى الانتظار، جاري استعادة: ${filename}`;
        document.querySelector('#progressModal .modal-body small').textContent = 'سيتم إنشاء نسخة احتياطية من البيانات الحالية أولاً...';

        fetch('/backup/restore-direct', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                filename: filename
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text().then(text => {
                try {
                    return JSON.parse(text);
                } catch (e) {
                    console.error('Response is not valid JSON:', text);
                    throw new Error('الاستجابة من الخادم غير صحيحة');
                }
            });
        })
        .then(data => {
            progressModal.hide();

            if (data.success) {
                let successMessage = `✅ ${data.message}`;
                if (data.current_backup) {
                    successMessage += `\n\n📋 تم إنشاء نسخة احتياطية من البيانات السابقة: ${data.current_backup}`;
                }

                showAlert('success', successMessage);

                // تحديث الصفحة بعد 3 ثواني
                setTimeout(() => {
                    location.reload();
                }, 3000);
            } else {
                showAlert('danger', `❌ ${data.message || 'حدث خطأ أثناء الاستعادة'}`);
            }
        })
        .catch(error => {
            progressModal.hide();
            console.error('Restore error:', error);
            showAlert('danger', `❌ حدث خطأ أثناء الاستعادة: ${error.message}`);
        });
    }
}

// إلغاء نسخة احتياطية معلقة
function cancelPendingBackup(filename) {
    const confirmMessage = `هل تريد إلغاء النسخة الاحتياطية المعلقة؟

📁 الملف: ${filename}

سيتم حذف هذه النسخة من قائمة الانتظار.

هل تريد المتابعة؟`;

    if (confirm(confirmMessage)) {
        fetch(`/backup/delete/${filename}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                _method: 'DELETE'
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showAlert('success', `✅ ${data.message}`);
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showAlert('danger', `❌ ${data.message || 'حدث خطأ أثناء الإلغاء'}`);
            }
        })
        .catch(error => {
            console.error('❌ خطأ في إلغاء النسخة المعلقة:', error);
            showAlert('danger', `❌ حدث خطأ أثناء الإلغاء: ${error.message}`);
        });
    }
}

// تنظيف النسخ المعلقة
function cleanupPendingBackups() {
    const confirmMessage = `هل تريد تنظيف النسخ الاحتياطية المعلقة؟

⚠️ سيتم تحديث حالة النسخ التي تعلقت لأكثر من 30 دقيقة إلى "فاشلة"

هذا سيسمح بحذفها أو إعادة المحاولة.

هل تريد المتابعة؟`;

    if (confirm(confirmMessage)) {
        fetch('/backup/cleanup-pending', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}',
                'Accept': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showAlert('success', `✅ ${data.message}`);
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showAlert('info', `ℹ️ ${data.message || 'لا توجد نسخ معلقة للتنظيف'}`);
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تنظيف النسخ المعلقة:', error);
            showAlert('danger', `❌ حدث خطأ أثناء التنظيف: ${error.message}`);
        });
    }
}

// إظهار التنبيهات
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 500px; white-space: pre-wrap;';

    // تنظيف الرسالة وتحويل \n إلى <br>
    const cleanMessage = message.replace(/\n/g, '<br>');

    alertDiv.innerHTML = `
        <div style="max-height: 200px; overflow-y: auto;">${cleanMessage}</div>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزالة التنبيه بعد 8 ثواني للرسائل الطويلة
    const timeout = message.length > 100 ? 8000 : 5000;
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, timeout);
}

// تفعيل tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// تحديث تلقائي للحالة كل 30 ثانية
setInterval(() => {
    // فحص إذا كان هناك نسخ قيد التنفيذ
    const runningBackups = document.querySelectorAll('.badge.bg-info');
    if (runningBackups.length > 0) {
        location.reload();
    }
}, 30000);
</script>
@endsection
