<?php

namespace Database\Seeders;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Table;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get waiters and tables
        $waiters = User::whereHas('role', function ($query) {
            $query->where('name', 'waiter');
        })->get();
        
        $tables = Table::all();
        
        // Create 20 orders
        for ($i = 0; $i < 20; $i++) {
            $waiter = $waiters->random();
            $table = $tables->random();
            $status = $this->getRandomStatus();
            $createdAt = now()->subDays(rand(0, 30))->subHours(rand(0, 23));
            
            $order = Order::create([
                'user_id' => $waiter->id,
                'table_id' => $table->id,
                'status' => $status,
                'total_amount' => 0, // Will be calculated
                'notes' => rand(0, 10) > 7 ? 'ملاحظات خاصة بالطلب' : null,
                'created_at' => $createdAt,
                'updated_at' => $createdAt->copy()->addMinutes(rand(15, 120)),
            ]);
            
            // Add 2-5 items to each order
            $itemCount = rand(2, 5);
            $totalAmount = 0;
            
            $products = Product::where('is_available', true)->inRandomOrder()->take($itemCount)->get();
            
            foreach ($products as $product) {
                $quantity = rand(1, 3);
                $unitPrice = $product->price;
                $subtotal = $quantity * $unitPrice;
                $totalAmount += $subtotal;
                
                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'quantity' => $quantity,
                    'unit_price' => $unitPrice,
                    'subtotal' => $subtotal,
                    'notes' => rand(0, 10) > 8 ? 'ملاحظات خاصة بالمنتج' : null,
                ]);
            }
            
            // Update order total
            $order->update(['total_amount' => $totalAmount]);
            
            // If order is completed, create an invoice
            if ($status === 'completed') {
                $this->createInvoice($order);
            }
        }
    }
    
    /**
     * Get a random order status.
     */
    private function getRandomStatus(): string
    {
        $statuses = [
            'pending' => 10,
            'preparing' => 15,
            'ready' => 15,
            'delivered' => 20,
            'completed' => 35,
            'cancelled' => 5,
        ];
        
        $rand = rand(1, 100);
        $sum = 0;
        
        foreach ($statuses as $status => $probability) {
            $sum += $probability;
            if ($rand <= $sum) {
                return $status;
            }
        }
        
        return 'completed';
    }
    
    /**
     * Create an invoice for the order.
     */
    private function createInvoice($order): void
    {
        $cashiers = User::whereHas('role', function ($query) {
            $query->where('name', 'cashier');
        })->get();
        
        $cashier = $cashiers->isNotEmpty() ? $cashiers->random() : User::first();
        $totalAmount = $order->total_amount;
        $taxRate = 0.15; // 15% tax
        $taxAmount = $totalAmount * $taxRate;
        $discountAmount = rand(0, 10) > 7 ? $totalAmount * (rand(5, 20) / 100) : 0;
        $finalAmount = $totalAmount + $taxAmount - $discountAmount;
        
        \App\Models\Invoice::create([
            'order_id' => $order->id,
            'user_id' => $cashier->id,
            'invoice_number' => 'INV-' . str_pad($order->id, 4, '0', STR_PAD_LEFT),
            'total_amount' => $totalAmount,
            'tax_amount' => $taxAmount,
            'discount_amount' => $discountAmount,
            'final_amount' => $finalAmount,
            'payment_method' => $this->getRandomPaymentMethod(),
            'payment_status' => 'paid',
            'notes' => rand(0, 10) > 8 ? 'ملاحظات خاصة بالفاتورة' : null,
            'created_at' => $order->updated_at,
            'updated_at' => $order->updated_at,
        ]);
    }
    
    /**
     * Get a random payment method.
     */
    private function getRandomPaymentMethod(): string
    {
        $methods = [
            'cash' => 60,
            'card' => 35,
            'online' => 5,
        ];
        
        $rand = rand(1, 100);
        $sum = 0;
        
        foreach ($methods as $method => $probability) {
            $sum += $probability;
            if ($rand <= $sum) {
                return $method;
            }
        }
        
        return 'cash';
    }
}
